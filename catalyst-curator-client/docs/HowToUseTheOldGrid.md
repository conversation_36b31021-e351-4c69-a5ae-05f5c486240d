import { colors } from '@react-spring/shared';
import React, { Component, ReactNode, useEffect } from 'react';
import { StyleProp, TextStyle, View, ViewStyle } from 'react-native';
import { Text, withTheme } from 'react-native-paper';
import { ColumnConfigEntry, DataTable, DataTableCell, DataTableTextHeaderRow, Dates, Title } from '../lib';
import { Priority } from '../lib/Priority';
import { Router } from '../platform/Router';
import { MainStackParamList } from '../routing/screens';
import { Opportunity, SortField } from '../services/codegen/types';
import OpportunityListStore from '../stores/OpportunityListStore';
import UserStore from '../stores/UserStore';
import { FilterOpportunitiesBar } from './sections/opportunity/FilterOpportunitiesBar';

interface DashboardProps {
opportunityListStore: OpportunityListStore;
router: Router<MainStackParamList>;
theme: ReactNativePaper.ThemeProp;
userStore: UserStore;
}

export const DashboardPage = withTheme(({ opportunityListStore, router, theme }: DashboardProps) => {
const {
fonts,
fontSizes,
styles: { margins, paddings, components, borders },
colors,
} = theme;
return (
<View style={[components.flexAll, components.globalPageConstraints]}>
<View
style={[
components.flexAll,
components.panelStyle,
components.shadow,
]} >
<Title style={[fonts.mediumTitle, fontSizes.medium, { color: colors.primary }]}>Opportunities Dashboard</Title>
<FilterOpportunitiesBar style={[margins.VerticalML]} opportunityListStore={opportunityListStore} />
<DataTable
columnRenderer={(columnId, rowItem) => columnRenderer(router, theme, columnId, rowItem as Opportunity)}
columnIds={columnIds}
getRows={() => opportunityListStore.opportunities}
style={{}}
rowStyle={{}}
header={
<DataTableTextHeaderRow
columnIds={columnIds}
columnConfig={columnConfig}
columnHeaderStyle={getColumnHeaderStyle(theme)}
getSortIndicator={(columnId) => opportunityListStore.getSortFieldStatus(columnId)}
getColumnHeaderTextStyle={(columnId) => getColumnHeaderTextStyle(theme, columnId)}
onHeaderCellPress={(columnId) => handleColumnHeaderOnPress(opportunityListStore, columnId)}
/>
}
/>
</View>
</View>
);
});

const handleColumnHeaderOnPress = (opportunityListStore: OpportunityListStore, columnId: string) => {
//@TODO refactor opportunity list store to be consistent with other stores
if (opportunityListStore.queryInProgress) return;

const propertySort = opportunityListStore.getSortedColumn();
if (propertySort?.fieldName === columnId && propertySort.ascending) {
opportunityListStore.setSortedColumn({ fieldName: columnId, ascending: false });
} else {
opportunityListStore.setSortedColumn({ fieldName: columnId, ascending: true });
}
opportunityListStore.queryOpportunities();
};

const columnRenderer = (
router: Router<MainStackParamList>,
theme: ReactNativePaper.ThemeProp,
columnId: string,
oppportunity: Opportunity,
): ReactNode => {
const cellRenderer = cellRenderMap[columnId]
? cellRenderMap[columnId](oppportunity, theme)
: defaultRenderer(oppportunity, columnId);
return (
<DataTableCell
onCellPress={() => router.navigate('curation', { id: oppportunity.id })}
getCellContent={cellRenderer}
style={[columnStyle[columnId]]}
key={columnId}
/>
);
};

const defaultRenderer = (oppportunity: Opportunity, columnId: string): (() => ReactNode) => {
return () => getTextCellContent({ value: (oppportunity as any)[columnId] });
};

const cellRenderMap: Record<string, (opportunity: Opportunity, theme: ReactNativePaper.ThemeProp) => () => ReactNode> = {
priority: (opportunity: Opportunity, theme: ReactNativePaper.ThemeProp) => {
const { colors, fonts } = theme;
const priority = opportunity.priority || Priority.NONE;
return () => getTextCellContent({ value: Priority[priority], textStyle: [fonts.bold] });
},
categories: (opportunity: Opportunity) => {
return () => getTextListCellContent(opportunity.categories.map((category) => category.name));
},
stakeholders: (opportunity: Opportunity) => {
return () => getTextListCellContent(opportunity.stakeholders.map((stakeholder) => stakeholder.name || ''));
},
updatedAt: (opportunity: Opportunity) => {
return () => getTextListCellContent(Dates.asDateAndTimeStringTuple(opportunity.curationInfo?.lastCurated));
},
createdAt: (opportunity: Opportunity) => {
return () => getTextListCellContent(Dates.asDateAndTimeStringTuple(opportunity.createdAt));
},
org1: (opportunity: Opportunity) => {
return () => getTextCellContent({ value: `${opportunity.org1}/ ${opportunity.org2}` });
},
statusNotes: (opportunity: Opportunity) => {
return () => getTextCellContent({ value: opportunity.statusNotes as string, textStyle: { textAlign: 'left' } });
},
};

const getTextListCellContent = (values?: string[] | number[], textStyle?: StyleProp<TextStyle>) => (
<View style={{ flexDirection: 'column', flex: 1, justifyContent: 'center', alignItems: 'center' }}>
{values?.map((value, index) => getTextCellContent({ value, textStyle, key: index }))}
</View>
);

const getTextCellContent = (params: {
value: string | number;
textStyle?: StyleProp<TextStyle>;
key?: string | number;
numberOfLines?: number;
}) => (
<Text
ellipsizeMode="tail"
numberOfLines={params.numberOfLines}
style={[{ textAlign: 'center', flexWrap: 'wrap' }, params.textStyle]}
key={params.key}

>

    {params.value}

  </Text>
);

const columnIds = [
'priority',
'status',
'title',
'updatedAt',
'createdAt',
'org1',
'stakeholders',
'categories',
'solutionPathway',
'statusNotes',
];

const columnConfig: Record<string, ColumnConfigEntry> = {
priority: { label: 'Priority' },
status: { label: 'Status' },
title: { label: 'Problem Title' },
updatedAt: { label: 'Last Curated' },
createdAt: { label: 'Created' },
org1: { label: 'Org / Team' },
stakeholders: { label: 'Stakeholders', disableSort: true },
categories: { label: 'Categories', disableSort: true },
solutionPathway: { label: 'Solution Pathway' },
statusNotes: { label: 'Status Notes' },
};

const getColumnHeaderStyle = (theme: ReactNativePaper.ThemeProp): Record<string, StyleProp<ViewStyle>> => {
const { styles: { defaultValues }, colors } = theme;
const commonStyle = { borderRightWidth: 1, borderColor: colors.border };
return {
priority: { flex: 2, ...commonStyle},
status: { flex: 2, ...commonStyle },
title: { flex: 5, ...commonStyle },
updatedAt: { flex: 2, ...commonStyle },
createdAt: { flex: 2, ...commonStyle },
org1: { flex: 3, ...commonStyle },
stakeholders: { flex: 3, ...commonStyle },
categories: { flex: 3, ...commonStyle },
solutionPathway: { flex: 4, ...commonStyle },
statusNotes: { flex: 6 },
};
};

const getColumnHeaderTextStyle = (theme: ReactNativePaper.ThemeProp, columnId: string) => {
/_return {
priority: {
fontSize: theme.fonts.xLarge.fontSize,
},
}[columnId];
_/
return {};
};

const columnStyle: Record<string, StyleProp<ViewStyle>> = {
priority: { flex: 2 },
status: { flex: 2 },
title: { flex: 5 },
updatedAt: { flex: 2 },
createdAt: { flex: 2 },
org1: { flex: 3 },
stakeholders: { flex: 3 },
categories: { flex: 3 },
solutionPathway: { flex: 4 },
statusNotes: { flex: 6, borderRightWidth: 0 },
};
