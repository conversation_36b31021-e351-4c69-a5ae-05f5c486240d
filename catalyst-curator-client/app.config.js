import 'dotenv/config';

export default {
  expo: {
    name: 'Opportunity Curator',
    slug: 'catalyst-curator-client',
    version: '1.0.0',
    orientation: 'portrait',
    icon: './src/assets/images/icon.png',
    scheme: 'myapp',
    userInterfaceStyle: 'automatic',
    splash: {
      image: './src/assets/images/splash.png',
      resizeMode: 'contain',
      backgroundColor: '#ffffff',
    },
    updates: {
      fallbackToCacheTimeout: 0,
    },
    assetBundlePatterns: ['**/*'],
    ios: {
      supportsTablet: true,
    },
    android: {
      adaptiveIcon: {
        foregroundImage: './src/assets/images/adaptive-icon.png',
        backgroundColor: '#ffffff',
      },
    },
    web: {
      favicon: './src/assets/images/favicon.png',
    },
    experiments: {
      baseUrl: "/ic",
    },
    extra: {
      graphQLUrl: process.env.GRAPHQL_URL,
      NODE_DEV: process.env.NODE_DEV,
      NODE_ENV: process.env.NODE_ENV,
      TenantDefault: process.env.TENANT_DEFAULT,
      DownloadManager: process.env.DOWNLOAD_MANAGER,
    },
    entryPoint: 'App.tsx',
  },
};
