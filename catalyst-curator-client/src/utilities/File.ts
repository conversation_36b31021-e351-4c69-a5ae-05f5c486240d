import Constants from 'expo-constants';
// import { openURL } from 'expo-linking';

import { Location } from '../services/codegen/types';
import FetchClient from '../services/fetch/FetchClient';

interface FetchResults {
  contentType: string;
  href: string;
}

interface ReadResult {
  done: boolean;
  value: Uint8Array;
}
export const downloadManagerLocation = Constants.expoConfig?.extra?.DownloadManager;

export function fetchStream(hostUrl: string, body?: BodyInit, headers?: Record<string, string>): Promise<FetchResults> {
  const fetchClient = new FetchClient();
  if (!hostUrl) return Promise.reject('Could not discover url path.');

  let contentType: string = '';
  return new Promise((resolve, reject) => {
    fetchClient
      .post(hostUrl, headers, body)
      .then((response: any) => {
        const header = response.headers;
        contentType = header.get('content-type') || '';
        return response.body;
      })
      .then((rb) => {
        if (!rb) return;
        const reader = rb.getReader();
        return new ReadableStream({
          start(controller) {
            function push() {
              reader?.read().then(({ done, value }: ReadResult) => {
                if (done) {
                  controller.close();
                  return;
                }
                // Get the data and send it to the browser via the controller
                controller.enqueue(value);
                // Check chunks by logging to the console
                // console.log(done, value);
                push();
              });
            }
            push();
          },
        });
      })
      .then((stream) => {
        return new Response(stream);
      })
      .then((response) => response.blob())
      .then((blob) => {
        const blobData = new Blob([blob], { type: contentType });
        resolve({
          contentType,
          href: URL.createObjectURL(blobData),
        });
      })
      .catch((e) => {
        reject(e);
      });
  });
}

export function openWebFileStream(
  location: string,
  fileName: string,
  body?: BodyInit,
  headers = { 'Content-Type': 'application/json' },
): Promise<void> {
  return fetchStream(location, body, headers)
    .then((results: FetchResults) => {
      openWebFileLink(results.href, fileName, results.contentType);
    })
    .catch((e) => {
      return e;
    });
}

export function openWebFileLink(location: string, fileName: string, mimeType: string): void {
  try {
    let a = document.createElement('a');
    a.setAttribute('href', location);
    a.setAttribute('type', mimeType);
    a.setAttribute('download', fileName);
    a.setAttribute('target', '_blank');
    a.click();
    // openURL(location);
  } catch {
    console.log('Failed to open file.');
  }
}
