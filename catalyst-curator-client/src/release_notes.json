{"development": {"title": "What's New?", "text": "•• PLEASE CLICK ‘RESET TABLE’ TO ACCESS NEW DASHBOARD IMPROVEMENTS ••\n\nWe are pleased to announce the following improvements to the table layout in this release:\n\nWARFIGHTING FUNCTION COLUMN:\n• A new ‘Warfighting Function’ column has been introduced, displaying the Warfighting Function associated with each submission.\n\nADDING WARFIGHTING FUNCTIONS:\n• Submissions must now be categorized according to their corresponding Army Warfighting Function (WFF). A WFF is a group of tasks and systems united by a common purpose that commanders use to accomplish missions and training objectives. The new WFF label allows you to organize your submissions and projects by a common purpose, facilitating a more efficient innovation initiative workflow.\n\n RETROACTIVE LABELING FOR WARFIGHTING FUNCTIONS:\n• Action required to update legacy submissions. This update introduces Warfighting Functions as a required field for all submissions and project initiatives. By default, ‘legacy projects’ will have an ‘unassigned’ WFF. Note that you MUST now assign a WFF to all past and ongoing submissions and innovation initiatives before you continue to use the platform, or data reporting and analytics will be incomplete/inaccurate.\n\nCHANGING A WFF:\n• This update allows for an authorized curator to change a Warfighting Function. Accordingly, the selection you make now is not irreversible. However, only one (1) warfighting function may be selected at a time.\n\nCOLUMN FILTERING:\n• With the addition of Warfighting Functions to the Opportunity table, filtering has moved from the header filter button to column-specific filtering with a new filter icon.\n• When a filter has been applied, the filter icon changes color to notify you that the column has a filter applied.\n•We have included a ’RESET FILTERS’ button to clear all filters applied to the table.​ \n\n​Thank you for your continued support, and we trust that these enhancements will contribute to an even more efficient and effective user interaction within the Soldier Innovation platform.\n\n"}, "1.2.8": {"title": "What's New?", "text": "•• PLEASE CLICK ‘RESET TABLE’ TO ACCESS NEW DASHBOARD IMPROVEMENTS ••\n\nWe are pleased to announce the following improvements to the table layout in this release:\n\nWARFIGHTING FUNCTION COLUMN:\n• A new ‘Warfighting Function’ column has been introduced, displaying the Warfighting Function associated with each submission.\n\nADDING WARFIGHTING FUNCTIONS:\n• Submissions must now be categorized according to their corresponding Army Warfighting Function (WFF). A WFF is a group of tasks and systems united by a common purpose that commanders use to accomplish missions and training objectives. The new WFF label allows you to organize your submissions and projects by a common purpose, facilitating a more efficient innovation initiative workflow.\n\n RETROACTIVE LABELING FOR WARFIGHTING FUNCTIONS:\n• Action required to update legacy submissions. This update introduces Warfighting Functions as a required field for all submissions and project initiatives. By default, ‘legacy projects’ will have an ‘unassigned’ WFF. Note that you MUST now assign a WFF to all past and ongoing submissions and innovation initiatives before you continue to use the platform, or data reporting and analytics will be incomplete/inaccurate.\n\nCHANGING A WFF:\n• This update allows for an authorized curator to change a Warfighting Function. Accordingly, the selection you make now is not irreversible. However, only one (1) warfighting function may be selected at a time.\n\nCOLUMN FILTERING:\n• With the addition of Warfighting Functions to the Opportunity table, filtering has moved from the header filter button to column-specific filtering with a new filter icon.\n• When a filter has been applied, the filter icon changes color to notify you that the column has a filter applied.\n•We have included a ’RESET FILTERS’ button to clear all filters applied to the table.​ \n\n​Thank you for your continued support, and we trust that these enhancements will contribute to an even more efficient and effective user interaction within the Soldier Innovation platform.\n\n"}}