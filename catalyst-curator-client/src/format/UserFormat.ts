import { User } from '../services/codegen/types';

export class UserFormat {
  static formatUserLong(user: User) {
    let display = this.formatUser(user);
    if (user?.org1 || user?.org2) display = `${display} - ${this.formatUserOrg(user)}`;
    return display;
  }

  static formatUserOrg(user: User) {
    return user?.org1 || user?.org2 ? `${user?.org1 || 'n/a'}${user.org2 ? ' / ' + user.org2 : ''}` : 'n/a';
  }

  static formatUser(user: User) {
    return `${user?.lastName || ''}, ${user?.firstName || ''}`;
  }

  static formatEmails(user: User) {
    return user?.emailAddress || user?.altContact
      ? `${user.emailAddress || 'n/a'}${user.altContact ? ', ' + user.altContact : ''}`
      : 'n/a';
  }
}
