import { StyleProp, View, ViewStyle, useWindowDimensions } from 'react-native';
import { withTheme } from 'react-native-paper';
import { Title } from '../lib';
import { FilterOpportunitiesBar } from './FilterOpportunitiesBar';
import { SMALL_DEVICE_BREAKPOINT } from '../constants/breakpoints';
import { ApplicationMetaStore } from '../stores/ApplicationMetaStore';
import { OpportunityListStore, OpportunityReportsStore, TenantStore, UserStore } from '../stores';
import { ResourcesStore } from '../stores/ResourcesStore';

type GridHeader = {
  theme: ReactNativePaper.ThemeProp;
  handleOnDownload: (
    opportunityListStore: OpportunityListStore,
    applicationMetaStore: ApplicationMetaStore,
    allFields?: boolean,
  ) => void;
  opportunityListStore: OpportunityListStore;
  userStore: UserStore;
  userMetaStore: ApplicationMetaStore;
  usePortfolioFilter?: boolean;
  title: string;
  titleColor: string;
  style?: StyleProp<ViewStyle>;
  resourcesStore?: ResourcesStore;
  opportunityReportsStore?: OpportunityReportsStore;
  tenantStore: TenantStore;
  setEventFilterInfo?: () => void;
};
export const GridHeader = withTheme(
  ({
    theme,
    handleOnDownload,
    opportunityListStore,
    userMetaStore,
    userStore,
    title,
    titleColor,
    style,
    opportunityReportsStore,
    usePortfolioFilter = false,
    resourcesStore,
    tenantStore,
  }: GridHeader) => {
    const { width } = useWindowDimensions();
    const shouldFilterStack = width <= SMALL_DEVICE_BREAKPOINT;

    const {
      styles: { margins, fontSizes, fonts },
    } = theme;
    return (
      <View
        style={[
          { flexDirection: shouldFilterStack ? 'column' : 'row', alignItems: 'center', gap: shouldFilterStack ? 8 : 0 },
          style,
        ]}
      >
        <Title style={[fonts.mediumTitle, fontSizes.medium, { color: titleColor }]}>{title}</Title>
        <FilterOpportunitiesBar
          handleOnDownload={handleOnDownload}
          style={[margins.BottomXS]}
          opportunityListStore={opportunityListStore}
          applicationMetaStore={userMetaStore}
          userStore={userStore}
          usePortfolioFilter={usePortfolioFilter}
          resourcesStore={resourcesStore}
          opportunityReportsStore={opportunityReportsStore}
          tenantStore={tenantStore}
        />
      </View>
    );
  },
);
