import React, { Component } from 'react';
import { observer } from 'mobx-react';
import { withTheme } from 'react-native-paper';
import { Modal } from '../lib/ui/atoms/Modal';
import { Button, TextInput } from '../lib';

interface ModalSetTenantProps {
  defaultOverrideTenant?: string;
  isVisible?: boolean;
  onDismiss?: Function;
  onDone?: Function;
  theme: ReactNativePaper.ThemeProp;
  style?: object;
}
interface ModalSetTenantState {
  overrideTenant: string;
}

@observer
class ModalSetTenant extends Component<ModalSetTenantProps, ModalSetTenantState> {
  static defaultProps = {
    isVisible: false,
    onDismiss: () => {},
    style: {},
  };

  constructor(props: ModalSetTenantProps) {
    super(props);
    const { defaultOverrideTenant } = this.props;
    this.state = {
      overrideTenant: defaultOverrideTenant || '',
    };
  }
  render() {
    const { isVisible, onDone, style } = this.props;
    const { overrideTenant } = this.state;

    return (
      <Modal style={{ padding: 20, elevation: 0, ...style }} getIsVisible={() => !!isVisible} onDismiss={() => {}}>
        <TextInput
          getValue={() => overrideTenant}
          setValue={this.handleOnChangeText}
          placeholder={'Override Tenant Handle'}
          spellcheck={false}
        />
        <Button
          style={{ marginTop: 10, elevation: 0 }}
          mode="contained"
          onPress={() => onDone && onDone(overrideTenant)}
        >
          {'DONE'}
        </Button>
      </Modal>
    );
  }

  handleOnChangeText = (value: string) => {
    this.setState({
      overrideTenant: value,
    });
  };
}

export default withTheme(ModalSetTenant);
