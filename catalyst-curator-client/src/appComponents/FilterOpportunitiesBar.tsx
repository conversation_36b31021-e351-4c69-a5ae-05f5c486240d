import { Style<PERSON>rop, View, ViewStyle } from 'react-native';
import { withTheme } from 'react-native-paper';
import { ApplicationMetaStore } from '../stores/ApplicationMetaStore';
import OpportunityListStore from '../stores/OpportunityListStore';
import { FilterBar } from './FilterBar';
import { OpportunityReportsStore, TenantStore, UserStore } from '../stores';
import { ReactNode } from 'react';
import { ResourcesStore } from '../stores/ResourcesStore';

interface FilterOpportunitiesBarProps {
  style?: StyleProp<ViewStyle>;
  theme: ReactNativePaper.ThemeProp;
  opportunityListStore: OpportunityListStore;
  applicationMetaStore: ApplicationMetaStore;
  handleOnDownload: (
    opportunityListStore: OpportunityListStore,
    applicationMetaStore: ApplicationMetaStore,
    allFields: boolean,
    userStore?: UserStore,
  ) => void;
  usePortfolioFilter?: boolean;
  userStore: UserStore;
  opportunityReportsStore?: OpportunityReportsStore;
  resourcesStore?: ResourcesStore;
  tenantStore: TenantStore;
}

export const FilterOpportunitiesBar = withTheme(
  ({
    opportunityListStore,
    applicationMetaStore,
    style,
    handleOnDownload,
    usePortfolioFilter = false,
    userStore,
    resourcesStore,
    opportunityReportsStore,
    tenantStore,
  }: FilterOpportunitiesBarProps) => {
    const props = {
      style,
      onChooseDownload: ({ value }: { value: string }) =>
        handleOnDownload(opportunityListStore, applicationMetaStore, value === 'all', userStore),
      opportunityListStore,
      usePortfolioFilter,
      userStore,
      resourcesStore,
      opportunityReportsStore,
      tenantStore,
    };

    return <FilterBar {...props} />;
  },
);
