import { observer, useLocalObservable } from 'mobx-react';
import { withTheme } from 'react-native-paper';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  CartesianGrid,
  LabelList,
  Legend,
  RectangleProps,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts';
import { DataKey } from 'recharts/types/util/types';
import { Payload as LegendPayload } from 'recharts/types/component/DefaultLegendContent';
import { CustomTooltip } from './CustomTooltip.web';

interface StackedBarChartProps {
  data: any | undefined[];
  theme: ReactNativePaper.ThemeProp;
  barColors: string[];
  xAxisDataKey: string;
  tenantKeys: string[];
}

interface LocalStore {
  activeKey: DataKey<any> | undefined;
  setActiveKey: (key: DataKey<any> | undefined) => void;
}

export const StackedBarChart = withTheme(
  observer(({ theme, barColors, data, xAxisDataKey, tenantKeys }: StackedBarChartProps) => {
    const localStore = useLocalObservable<LocalStore>(() => ({
      activeKey: undefined,
      setActiveKey(key: DataKey<any> | undefined) {
        this.activeKey = key;
      },
    }));
    function handleMouseEnter(localStore: LocalStore, key: DataKey<any> | undefined) {
      localStore.setActiveKey(key);
    }

    function handleMouseLeave(localStore: LocalStore) {
      localStore.setActiveKey(undefined);
    }

    return (
      <ResponsiveContainer minWidth={0} minHeight={50} width={'100%'}>
        <BarChart
          barGap={100}
          data={data}
          margin={{
            top: 20,
            right: 30,
            left: 20,
            bottom: 5,
          }}
          style={{
            fontFamily: theme.fonts.regularTitle.fontFamily,
            fill: '#676D79',
            fontSize: theme.fontSizes.xSmall.fontSize,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" vertical={false} style={{ zIndex: 100 }} />
          <XAxis tickLine={false} dataKey={xAxisDataKey} />
          <YAxis tickLine={false} axisLine={false} allowDecimals={false} />
          <Tooltip
            cursor={{ fill: 'transparent' }}
            content={({ payload }) => {
              if (!payload) return null;
              for (const bar of payload) {
                if (bar.dataKey === localStore.activeKey)
                  return <CustomTooltip data={[{ name: bar.dataKey as string, value: bar.value as number }]} />;
              }
              return null;
            }}
          />

          {tenantKeys.length > 0 ? (
            <>
              <Legend
                iconType="circle"
                iconSize={12}
                layout="vertical"
                content={({ payload }) => {
                  return (
                    <CustomLegend
                      handleMouseEnter={handleMouseEnter}
                      handleMouseLeave={handleMouseLeave}
                      locals={localStore}
                      payload={payload}
                    />
                  );
                }}
              />
              {tenantKeys.map((key, index) => {
                return (
                  <Bar
                    onMouseEnter={() => handleMouseEnter(localStore, key)}
                    onMouseLeave={() => handleMouseLeave(localStore)}
                    radius={index === 0 ? [1, 1, 0, 1] : 1}
                    key={index}
                    dataKey={key}
                    stackId="a"
                    fill={barColors[index % barColors.length]}
                    strokeWidth={1}
                    stroke="#FFF"
                    opacity={localStore.activeKey === undefined || localStore.activeKey === key ? 1 : 0.2}
                    style={{ zIndex: 0 }}
                    shape={(props: any) => <CustomBarShape {...props} />}
                  >
                    {index === tenantKeys.length - 1 ? (
                      <LabelList
                        position="top"
                        dataKey="submissionCount"
                        style={{
                          fontFamily: theme.fonts.regularTitle.fontFamily,
                          fill: theme.colors.text,
                          fontSize: theme.fontSizes.xSmall.fontSize,
                        }}
                      />
                    ) : null}
                  </Bar>
                );
              })}
            </>
          ) : (
            <Bar dataKey={'submissionCount'} fill="#8884d8" radius={[2, 2, 0, 0]} animationDuration={700}>
              <LabelList
                dataKey={'submissionCount'}
                position="top"
                style={{
                  fontFamily: theme.fonts.regularTitle.fontFamily,
                  fill: theme.colors.text,
                  fontSize: theme.fontSizes.xSmall.fontSize,
                }}
              />
            </Bar>
          )}
        </BarChart>
      </ResponsiveContainer>
    );
  }),
);

const CustomBarShape = (props: RectangleProps) => {
  const { x, y, width, height, fill, stroke, strokeWidth, opacity, ...rest } = props;
  if (height === 0) return null;
  return (
    <g>
      <rect x={x} y={y} width={width} height={height} fill={fill} opacity={opacity} />
      <rect x={x} y={y} width={width} height={strokeWidth} fill="none" stroke={stroke} />
    </g>
  );
};

interface CustomLegendProps {
  payload: LegendPayload[] | undefined;
  locals: LocalStore;
  handleMouseEnter: (locals: LocalStore, key: DataKey<any> | undefined) => void;
  handleMouseLeave: (locals: LocalStore) => void;
}
const CustomLegend = ({ payload, locals, handleMouseEnter, handleMouseLeave }: CustomLegendProps) => {
  const reversedPayload = payload?.slice().reverse();
  return (
    <ul style={{ listStyle: 'none', display: 'flex', padding: 0, justifyContent: 'center' }}>
      {reversedPayload?.map((entry, index) => {
        return (
          <li
            onMouseEnter={() => handleMouseEnter(locals, entry.dataKey)}
            onMouseLeave={() => handleMouseLeave(locals)}
            key={`item-${index}`}
            style={{
              marginRight: 10,
              opacity: locals.activeKey === undefined || locals.activeKey === entry.dataKey ? 1 : 0.5,
            }}
          >
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <svg height="10" width="10">
                <circle cx="5" cy="5" r="5" fill={entry.color} />
              </svg>
              <span style={{ marginLeft: 5, color: '#676D79' }}>{entry.value}</span>
            </div>
          </li>
        );
      })}
    </ul>
  );
};
