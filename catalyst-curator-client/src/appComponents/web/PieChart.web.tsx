import { observable, set } from 'mobx';
import { observer, useLocalObservable } from 'mobx-react';
import { useRef } from 'react';
import { withTheme } from 'react-native-paper';
import { Cell, Pie, PieChart as <PERSON><PERSON><PERSON><PERSON><PERSON>, ResponsiveContainer, Sector, Tooltip } from 'recharts';
import { PieSectorDataItem } from 'recharts/types/polar/Pie';
import { CustomTooltip } from './CustomTooltip.web';

interface PieChartProps {
  data: any[];
  theme: ReactNativePaper.ThemeProp;
  colors: string[];
  dataKey: string;
  defaultIndex: number;
}

interface Locals {
  activeIndex: number;
  hoverName: string | null;
  setActiveIndex: (index: number) => void;
  setHoverName: (name: string | null) => void;
}

export const PieChart = withTheme(
  observer(({ data, colors, dataKey, theme, defaultIndex }: PieChartProps) => {
    const localStore = useLocalObservable<Locals>(() => ({
      activeIndex: defaultIndex,
      hoverName: null,
      setActiveIndex(index: number) {
        this.activeIndex = index;
      },
      setHoverName(name: string | null) {
        this.hoverName = name;
      },
    }));

    const isEmpty = data.length === 0 || data.every((item) => item[dataKey] === 0);

    const greyedOutData = [{ name: 'No data available', [dataKey]: 1 }];

    function onPieClick(_: any, index: number) {
      localStore.setActiveIndex(index);
    }

    function onPieEnter(name: string) {
      localStore.setHoverName(name);
    }

    function onPieLeave() {
      localStore.setHoverName(null);
    }
    return (
      <ResponsiveContainer minWidth={0} minHeight={50} height={300} width={'100%'}>
        <RCPieChart>
          <Pie
            animationDuration={700}
            activeIndex={localStore.activeIndex}
            activeShape={(props: unknown) => {
              const propsAsSliceProps = props as SliceProps;
              return <Slice {...propsAsSliceProps} isEmpty={isEmpty} />;
            }}
            startAngle={90}
            endAngle={-270}
            data={isEmpty ? greyedOutData : data}
            cx="50%"
            cy="50%"
            innerRadius="65%"
            outerRadius="80%"
            dataKey={dataKey}
            onClick={onPieClick}
            onMouseEnter={(e) => {
              onPieEnter(e.name);
            }}
            onMouseLeave={onPieLeave}
            cornerRadius={3}
            paddingAngle={!isEmpty ? 1.5 : 0}
            inactiveShape={(props: unknown) => {
              const propsAsSliceProps = props as HoverSliceProps;

              return (
                <HoverSlice
                  {...{
                    ...propsAsSliceProps,
                    localStore,
                  }}
                />
              );
            }}
          >
            {(isEmpty ? greyedOutData : data).map((_, index) => {
              return <Cell key={`cell-${index}`} fill={isEmpty ? '#ECEDF5' : colors[index % colors.length]} />;
            })}
          </Pie>
          {!isEmpty && (
            <Tooltip
              animationDuration={500}
              content={(props) => {
                const payload = props.payload![0];
                if (!payload) return;
                const tenantBreakdown = payload.payload.tenantBreakdown;
                const name = localStore.hoverName;
                const nameStrings = name ? name.split(' ') : [];
                const hoverName = nameStrings.map((word) => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
                let title = `Top 5 - ${hoverName}`;
                if (tenantBreakdown.length <= 5) {
                  title = hoverName;
                }
                return <CustomTooltip data={tenantBreakdown} title={`${title}`} />;
              }}
            />
          )}
        </RCPieChart>
      </ResponsiveContainer>
    );
  }),
);

interface SliceProps {
  cx: number;
  cy: number;
  fill: string;
  midAngle: number;
  innerRadius: number;
  outerRadius: number;
  startAngle: number;
  endAngle: number;
  payload: PieSectorDataItem;
  percent: number;
  value: number;
  isEmpty: boolean;
  theme: ReactNativePaper.ThemeProp;
}
export const Slice = withTheme(
  observer((props: SliceProps) => {
    const { cx, cy, innerRadius, outerRadius, startAngle, endAngle, fill, payload, value } = props;
    const nameWords = payload.name ? payload.name.toLowerCase().split(' ') : [];

    return (
      <g>
        <text
          x={cx}
          y={cy}
          dy={4}
          textAnchor="middle"
          style={{
            fontFamily: props.theme.fonts.regularTitle.fontFamily,
            fontSize: props.theme.fontSizes.mediumSmall.fontSize,
            fill: '#676D79',
            opacity: props.isEmpty ? 0.4 : 1,
          }}
        >
          {props.isEmpty ? (
            <>
              <tspan x={cx} dy={-8}>
                No data
              </tspan>
              <tspan x={cx} dy={24}>
                available
              </tspan>
            </>
          ) : (
            nameWords.map((word, index) => (
              <tspan
                x={cx}
                dy={index === 0 ? 0 - (nameWords.length - 1) * 16 : 16}
                key={index}
                style={{
                  fontFamily: props.theme.fonts.regularTitle.fontFamily,
                  fontSize: props.theme.fontSizes.mediumSmall.fontSize,
                }}
              >
                {word.charAt(0).toUpperCase() + word.slice(1)}
              </tspan>
            ))
          )}
        </text>
        {!props.isEmpty && (
          <text
            x={cx}
            y={cy}
            dy={24}
            textAnchor="middle"
            style={{
              fontFamily: props.theme.fonts.regularTitle.fontFamily,
              fontSize: props.theme.fontSizes.mediumSmall.fontSize,
            }}
          >
            {value}
          </text>
        )}
        <Sector
          cx={cx}
          cy={cy}
          innerRadius={innerRadius}
          outerRadius={outerRadius}
          cornerRadius={3}
          startAngle={startAngle}
          endAngle={endAngle}
          fill={fill}
        />
        {!props.isEmpty && (
          <Sector
            cx={cx}
            cy={cy}
            startAngle={startAngle}
            endAngle={endAngle}
            cornerRadius={0.5}
            innerRadius={outerRadius + 6}
            outerRadius={outerRadius + 10}
            fill={fill}
          />
        )}
      </g>
    );
  }),
);

interface HoverSliceProps extends SliceProps {
  name: string;
  localStore: Locals;
}

export const HoverSlice = withTheme(
  observer((props: HoverSliceProps) => {
    const { cx, cy, innerRadius, outerRadius, startAngle, endAngle, fill, localStore, name } = props;
    const isHovered = name === localStore.hoverName;
    return (
      <g>
        <Sector
          cx={cx}
          cy={cy}
          innerRadius={innerRadius}
          outerRadius={outerRadius}
          cornerRadius={3}
          startAngle={startAngle}
          endAngle={endAngle}
          fill={fill}
        />
        {isHovered && (
          <Sector
            cx={cx}
            cy={cy}
            startAngle={startAngle}
            endAngle={endAngle}
            cornerRadius={0.5}
            innerRadius={outerRadius + 6}
            outerRadius={outerRadius + 10}
            fill={'#DCDFEC'}
          />
        )}
      </g>
    );
  }),
);
