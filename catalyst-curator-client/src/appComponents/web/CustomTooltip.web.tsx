import { Text } from '../../lib/ui/atoms/Text';

interface CustomTooltipProps {
  data: TooltipData[];
  title?: string;
}
interface TooltipData {
  name: string;
  value: number;
}

export const CustomTooltip = ({ data, title }: CustomTooltipProps) => {
  const topFiveData = transformToTopFive(data);
  return (
    <div
      style={{
        backgroundColor: '#323438',
        padding: '4px 8px',
        borderRadius: '4px',
      }}
    >
      {title && <Text style={{ color: 'white' }}>{title}</Text>}
      <div style={{ display: 'flex', flexDirection: 'column', borderTop: title ? '1px solid white' : undefined }}>
        {topFiveData.map((tenant: { name: string; value: unknown }) => {
          return (
            <div key={tenant.name} style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Text style={{ color: 'white', paddingRight: 8 }}>{`${tenant.name}`}</Text>
              <Text style={{ color: 'white' }}>{`${tenant.value}`}</Text>
            </div>
          );
        })}
      </div>
    </div>
  );
};

const transformToTopFive = (data: TooltipData[]): TooltipData[] => {
  //If other already exists, return data
  if (data.find((item) => item.name === 'Other')) return data;
  const tenantsSortedByValue = data.sort((a, b) => b.value - a.value);
  const topFive = tenantsSortedByValue.slice(0, 5);
  // const sortedTopFive = topFive.sort((a, b) => b.value).reverse();
  const other = tenantsSortedByValue.slice(5);

  if (data.length <= 5) return topFive;
  return [...topFive, { name: 'Other', value: other.reduce((acc, curr) => acc + curr.value, 0) }];
};

export function getTooltipDataFromRecord(data: any) {
  const tooltipData = Object.entries(data).map(([name, value]) => ({
    name,
    value: value as number,
  }));

  const filteredData = tooltipData.filter((item) => item.name !== 'total' && item.name !== 'month');
  filteredData.sort((a, b) => {
    if (a.name === 'Other') return 1;
    if (b.name === 'Other') return -1;
    return b.value - a.value;
  });
  return filteredData;
}
