import { observer } from 'mobx-react';
import { withTheme } from 'react-native-paper';
import {
  ResponsiveContainer,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  LabelList,
  Cell,
  ReferenceLine,
  Tooltip,
} from 'recharts';
import { CustomTooltip, getTooltipDataFromRecord } from './CustomTooltip.web';

interface BarChartProps {
  data: any[] | undefined;
  theme: ReactNativePaper.ThemeProp;
  barColors: string[];
  dataKey: string;
  xAxisDataKey: string;
  highlightedBarColor: string;
}

export const HorizontalBarChart = withTheme(
  observer(({ data, barColors, dataKey, theme, xAxisDataKey, highlightedBarColor }: BarChartProps) => {
    return (
      <ResponsiveContainer minWidth={0} minHeight={50} width={'100%'}>
        <BarChart
          data={data}
          layout="horizontal"
          margin={{
            top: 20,
            right: 30,
            left: 20,
            bottom: 5,
          }}
          style={{
            fontFamily: theme.fonts.regularTitle.fontFamily,
            fill: '#676D79',
            fontSize: theme.fontSizes.xSmall.fontSize,
          }}
        >
          <Tooltip
            cursor={{ fill: 'transparent' }}
            content={({ payload }) => {
              if (!payload || !payload[0] || !payload[0].dataKey || payload[0].payload.submissionCount === 0)
                return null;
              const tooltipData = payload[0].payload.tenantBreakdown;
              const month = payload[0].payload[xAxisDataKey];
              let title = `Top 5 - ${month}`;
              if (tooltipData.length <= 5) {
                title = month;
              }
              return <CustomTooltip data={tooltipData} title={title} />;
            }}
          />
          <CartesianGrid strokeDasharray="3 3" vertical={false} />
          <XAxis
            dataKey={xAxisDataKey}
            type="category"
            tickLine={false}
            fontFamily={theme.fonts.regularTitle.fontFamily}
            style={{
              textTransform: 'capitalize',
            }}
            tickFormatter={(value) => value.toLowerCase()}
          />
          <YAxis axisLine={false} tickLine={false} allowDecimals={false} />
          <Bar dataKey={dataKey} fill="#8884d8" radius={[2, 2, 0, 0]} animationDuration={700}>
            {data?.map((entry, index) => {
              const fillColor = index === data.length - 1 ? highlightedBarColor : barColors[index % barColors.length];
              return <Cell key={`cell-${index}`} fill={fillColor} />;
            })}
            <LabelList
              dataKey={dataKey}
              position="top"
              style={{
                fontFamily: theme.fonts.regularTitle.fontFamily,
                fill: theme.colors.text,
                fontSize: theme.fontSizes.xSmall.fontSize,
              }}
            />
          </Bar>
        </BarChart>
      </ResponsiveContainer>
    );
  }),
);
