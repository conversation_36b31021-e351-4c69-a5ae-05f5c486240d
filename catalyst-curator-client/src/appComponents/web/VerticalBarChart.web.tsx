import { observer, useLocalObservable } from 'mobx-react';
import { withTheme } from 'react-native-paper';
import { ResponsiveContainer, BarChart, Bar, XAxis, YAxis, CartesianGrid, LabelList, Cell, Tooltip } from 'recharts';
import { CustomTooltip } from './CustomTooltip.web';

interface BarChartProps {
  data: any[] | undefined;
  theme: ReactNativePaper.ThemeProp;
  barColors: string[];
  dataKey: string;
  yAxisDataKey: string;
}

export const VerticalBarChart = withTheme(
  observer(({ data, barColors, dataKey, theme, yAxisDataKey }: BarChartProps) => {
    const length = data?.length || 0;
    const valueSizes = new Map();
    const defaultYAxisLabelWidth: number = 100;
    const defaultYAxisLabelMaxWidth: number = 200;

    // TODO: Extract this method and make it a utility function in a generic location for all to use.
    // This would be a beneficial routine for getting string width to set UI width in certain situations.
    // This should not be the norm, but on occation when working with dynamic content and need boundaries this can help.
    const getTextWidth = (value: string, font: string) => {
      try {
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d')!;
        context.font = font;
        return Math.ceil(context.measureText(value).width) + 5; // 5 is just an added padding buffer.
      } catch {
        return -1;
      }
    };

    // TODO: It would be good to move this out since we don't know when the data actually changes.
    // The width this is setting is required in this functional component and can be measured outside.
    const localStore = useLocalObservable(() => ({
      yAxisWidth: defaultYAxisLabelWidth,
      setYAxisWidth(value: string, index: number) {
        const valueWidth = getTextWidth(value, theme.fonts.regularTitle.fontFamily);
        valueSizes.set(index, valueWidth);
        if (index + 1 >= length) {
          const largestSize = Math.max(...valueSizes.values());
          if (largestSize <= 0) return (this.yAxisWidth = defaultYAxisLabelWidth);

          // 1.2 is around about line height for 16px font for spliting content in half
          const width = Math.ceil(largestSize / 1.2);

          // 200 px is about as wide as we want to go
          if (width >= defaultYAxisLabelMaxWidth) return (this.yAxisWidth = defaultYAxisLabelWidth);

          this.yAxisWidth = width;
        }
      },
    }));

    return (
      <ResponsiveContainer minWidth={0} width={'100%'} height={'100%'}>
        <BarChart
          data={data}
          layout="vertical"
          maxBarSize={36}
          margin={{ left: 30, right: 40 }}
          style={{
            fontFamily: theme.fonts.regularTitle.fontFamily,
            fill: '#676D79',
            fontSize: theme.fontSizes.xSmall.fontSize,
          }}
        >
          <Tooltip
            animationDuration={500}
            cursor={{ fill: 'transparent' }}
            content={(props) => {
              const payload = props.payload![0];
              if (!payload) return;
              const tenantBreakdown = payload.payload.tenantBreakdown;
              const name = payload.payload[yAxisDataKey];
              let title = `Top 5 - ${name}`;
              if (tenantBreakdown.length <= 5) {
                title = name;
              }
              return <CustomTooltip data={tenantBreakdown} title={`${title}`} />;
            }}
          />
          <CartesianGrid strokeDasharray="3 3" horizontal={false} y={30} height={220} />
          <YAxis
            dataKey={yAxisDataKey}
            type="category"
            tickLine={false}
            axisLine={false}
            width={localStore.yAxisWidth}
            style={{
              textTransform: 'capitalize',
            }}
            fontFamily={theme.fonts.regularTitle.fontFamily}
            tickFormatter={(value, index) => {
              localStore.setYAxisWidth(value, index);
              // TODO: Need to define a configurable cap for number of characters.
              // For not we can set this to 75 which is pretty large for this.
              return value.length > 75 ? `${value.substring(0, 75)}...` : value;
            }}
          />
          <XAxis
            dataKey={dataKey}
            type="number"
            axisLine={false}
            color={theme.colors.border}
            tickLine={false}
            allowDecimals={false}
          />
          <Bar dataKey={dataKey} fill="#8884d8" radius={[0, 2, 2, 0]} animationDuration={700}>
            {data?.map((entry, index) => <Cell key={`cell-${index}`} fill={barColors[index % barColors.length]} />)}
            <LabelList
              dataKey={dataKey}
              position="right"
              style={{
                fontFamily: theme.fonts.regularTitle.fontFamily,
                fill: theme.colors.text,
                fontSize: theme.fontSizes.xSmall.fontSize,
              }}
            />
          </Bar>
        </BarChart>
      </ResponsiveContainer>
    );
  }),
);
