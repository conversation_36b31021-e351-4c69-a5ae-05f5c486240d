import React, { useRef, useMemo } from 'react';
import { AgGridReact } from 'ag-grid-react';
import { CellClickedEvent, ColDef, GetRowIdParams, IDatasource } from 'ag-grid-community';
import 'ag-grid-community/styles/ag-grid.css';
import 'ag-grid-community/styles/ag-theme-alpine-no-font.css';
import { observer } from 'mobx-react';
import { withTheme } from 'react-native-paper';

export interface GridProps<T> {
  getRowData: () => T[];
  getTableConfig: () => { colDefs: ColDef[] };
  getDefaultHeaderComponent: () => any;
  getMoreRows: () => void;
  onRowPress?: (rowEvent: CellClickedEvent) => void;
  onMoveColumn?: ({ colId, newIndex }: { colId: string; newIndex: number }) => void;
  onColSizeChange?: ({ colId, newWidth }: { colId: string; newWidth: number }) => void;
  onPinUnpinCol?: ({ colId, pinned }: { colId: string; pinned?: 'left' | 'right' | boolean | null }) => void;
  onShowHideCol?: ({ colId, visible }: { colId: string; visible: boolean }) => void;
  onGridReady?: (gridControl: GridControl) => void;
  onDataUpdated?: () => void;
  // may be observable
  minRowHeight?: number;
  theme: ReactNativePaper.ThemeProp;
  datasource?: IDatasource;
}

export const Grid = withTheme(
  observer(
    <T,>({
      getRowData,
      getTableConfig,
      onRowPress,
      onMoveColumn,
      onColSizeChange,
      onPinUnpinCol,
      onShowHideCol,
      onGridReady,
      onDataUpdated,
      getDefaultHeaderComponent,
      minRowHeight,
      theme,
      getMoreRows,
    }: GridProps<T>) => {
      const {
        colors,
        styles: { components },
      } = theme;
      // private ref to ag-grid
      const gridRef = useRef<AgGridReact<T>>(null);

      const { colDefs } = getTableConfig();
      const headerComponent = getDefaultHeaderComponent();
      const defaultColDef: ColDef<T> = useMemo(
        () => ({
          sortable: false,
          resizable: true,
          headerComponent,
        }),
        [],
      );
      const columnDefs: ColDef<T>[] = useMemo(() => colDefs, [colDefs]);

      /*const onFirstDataRendered = useCallback((event: FirstDataRenderedEvent) => {
      event.api.sizeColumnsToFit();
    }, []);*/
      return (
        <>
          {getStyle(theme)}
          <div className="ag-theme-alpine" style={{ flex: 1 }}>
            <AgGridReact<T>
              ref={gridRef}
              rowHeight={minRowHeight}
              getRowId={getRowId}
              columnDefs={columnDefs}
              defaultColDef={defaultColDef}
              rowData={getRowData()}
              rowSelection="single"
              animateRows={true}
              onGridReady={(e) => {
                onGridReady && onGridReady(new GridControl(gridRef.current!));
              }}
              onCellClicked={onRowPress}
              onBodyScrollEnd={async (event) => {
                const eGridDiv = document.querySelector('.ag-center-cols-clipper');

                const scrollTop = event.top;
                const scrollHeight = eGridDiv!!.scrollHeight;
                const bottomThreshold = 700;
                if (scrollTop + bottomThreshold >= scrollHeight && getRowData().length > 0 && scrollTop > 0) {
                  getMoreRows();
                }
              }}
              onRowDataUpdated={() => {
                onDataUpdated && onDataUpdated();
              }}
              /*
                Note there is a bug in ag-grid when moving a column to position zero and pinning it
                ag-grid does not ever report event finsished as true
                This is worked around in onColumnPinned()
              */
              onColumnMoved={(event) => {
                if (event.column?.getColId() && event.toIndex !== undefined) {
                  onColMoved({
                    colId: event.column?.getColId(),
                    toIndex: event.toIndex,
                    finished: event.finished,
                    onMoveColumn,
                  });
                  //console.log(event);
                }
              }}
              onColumnPinned={(event) => {
                if (event.column?.getColId()) {
                  //console.log(`pinned ${event.column?.getColId()}`);
                  //console.log(event);
                  // NOTE! This is a workaround for an ag-grid bug when moving a column to index 0 and pinning it
                  // ag-grid does not report event.finished as true
                  if (event.column.getLeft() === 0) {
                    onColMoved({
                      colId: event.column?.getColId(),
                      toIndex: 0,
                      finished: true,
                      onMoveColumn,
                    });
                  }
                  onPinUnpinCol &&
                    onPinUnpinCol({
                      colId: event.column!.getColId(),
                      pinned: event.pinned,
                    });
                }
              }}
              onColumnResized={(event) => {
                if (event.column?.getColId()) {
                  onColSizeChange &&
                    event.finished &&
                    onColSizeChange({
                      colId: event.column.getColId(),
                      newWidth: event.column!.getActualWidth(),
                    });
                }
              }}
              onColumnVisible={(event) => {
                if (event.column?.getColId()) {
                  onShowHideCol &&
                    onShowHideCol({
                      colId: event.column.getColId(),
                      visible: !!event.visible,
                    });
                }
              }}
            />
          </div>
        </>
      );
    },
  ),
);

const onColMoved = ({
  colId,
  toIndex,
  finished,
  onMoveColumn,
}: {
  colId: string;
  toIndex: number;
  finished: boolean;
  onMoveColumn?: ({ colId, newIndex }: { colId: string; newIndex: number }) => void;
}) => {
  //console.log(`moved ${colId} to ${toIndex} ${finished}`);
  onMoveColumn && finished && onMoveColumn({ colId: colId, newIndex: toIndex || 0 });
};

export class GridControl {
  constructor(private ref: AgGridReact) {}
  showAllColumns() {
    this.ref.columnApi.setColumnsVisible(this.ref.columnApi.getColumns()!, true);
  }
  scrollToIndex(index: number, position?: 'top' | 'bottom' | 'middle' | null | undefined) {
    this.ref.api.ensureIndexVisible(index, position);
  }
  scrollToItem(id: string, position?: 'top' | 'bottom' | 'middle' | null | undefined) {
    const node = this.ref.api.getRowNode(id);
    this.ref.api.ensureNodeVisible(node, position);
  }
  getRowNode(id: string) {
    const node = this.ref.api.getRowNode(id);
    return node;
  }
}

const getRowId = (params: GetRowIdParams) => params.data.id;

const getStyle = (theme: ReactNativePaper.ThemeProp) => {
  const {
    colors,
    styles: { components, paddings },
    fonts,
    fontSizes,
  } = theme;
  return (
    <style>
      {`
          .ag-theme-alpine {
                --ag-foreground-color: ${colors.text};
                --ag-background-color: ${colors.surface};
                --ag-header-foreground-color: ${colors.text};
                --ag-header-background-color: ${colors.surface};
                --ag-odd-row-background-color: ${colors.oddRow};
              	--ag-alpine-active-color: transparent;
                --ag-borders: none;
                --ag-borders-critical: solid 1px;
                --ag-data-color: ${colors.text};
                --ag-row-hover-color: ${colors.accent};
                --ag-border-color: ${colors.border};
                --ag-row-border-color: transparent;
                --ag-row-border-width: 0;
                --ag-font-size: ${fontSizes.xSmall.fontSize - 1}px;
                --ag-font-family: ${fonts.regular.fontFamily};
                --ag-cell-horizontal-border: solid ${colors.border} 1px;
                --ag-selected-row-background-color: ${colors.accent}
            }
            .ag-cell-value {
                line-height: normal !important;
                word-break: normal;
                padding-top: 16px;
                padding-bottom: 16px;
                border-width: 0px;
            }
            .ag-header {
              border-bottom-width: 2px;
              border-color: #757575;
            }
            .ag-pinned-left-header {
              border-width: 0px;
              border-right-width: 2px;
              border-right-color: #757575;
            }
            .ag-cell.ag-cell-last-left-pinned:not(.ag-cell-range-right):not(.ag-cell-range-single-cell) {
              border-width: 0px;
              border-right-width: 2px;
              border-right-color: #757575;
            }
        `}
    </style>
  );
  /*
  //--ag-cell-horizontal-border: solid ${colors.border} 1px;
              shadow-color: '#
    shadowColor: '#aaa',
    shadowOffset: { width: 1, height: 1 },
    shadowRadius: 5,
    shadowOpacity: 0.5,
    */
};
