import { FC } from 'react';
import { UserStore } from '../../stores';

export interface WithAdminRightsProps {
  userStore: UserStore;
}

export function withAdminRights<T extends {}>(
  WrappedComponent: React.ComponentType<T>,
): FC<WithAdminRightsProps & Omit<T, keyof WithAdminRightsProps>> {
  const WithAdminRightsComponent: FC<WithAdminRightsProps & Omit<T, keyof WithAdminRightsProps>> = (props: any) => {
    const { userStore, ...propData } = props;
    if (userStore && userStore.isAdminUser) {
      return <WrappedComponent {...propData} />;
    }
    return <></>;
  };
  return WithAdminRightsComponent;
}
