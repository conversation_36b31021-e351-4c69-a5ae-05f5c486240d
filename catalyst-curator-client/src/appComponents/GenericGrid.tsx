import { ColDef } from 'ag-grid-community';
import { Router } from '../platform/Router';
import { MainStackParamList } from '../routing/screens';
import { SortIndicator } from '../lib';
import { OpportunityListStore, UserStore } from '../stores';
import { Grid, GridControl } from './web/Grid.web';
import { TableMetaStore } from '../lib/stores/TableMetaStore';
import { PageInfo, SortField } from '../services/codegen/types';
import { ColumnHeader } from '../pages/sections/opportunity/ColumnHeader';
import { Text } from '../lib/ui/atoms/Text';
import { SearchFields } from '../lib/stores/SearchFields';
import { JsonSearchGroups } from '../lib/stores/JsonSearchGroups';

export interface Store<T> {
  pageInfo: PageInfo;
  pageSize: number;
  updatePageSize: () => Promise<void>;
  getSortFieldStatus: (fieldName: string) => SortIndicator;
  items: T[];
  selection: string | undefined;
  queryInProgress: boolean;
  getSortedColumn: () => SortField | undefined;
  setSortedColumn: (sortField: SortField) => void;
  queryItems: (pageSize?: number) => Promise<void>;
  pageNumber: number;
  pageCount: number;
  returnPageSize?: number;
  resetSearchValues: () => void;
  searchFields: SearchFields;
  searchGroups: JsonSearchGroups;
  // Add other common methods and properties here
}

interface GenericGridProps<T> {
  store: Store<T>;
  theme: ReactNativePaper.ThemeProp;
  router: Router<MainStackParamList>;
  colDefsMap: Record<string, ColDef>;
  sortableCols: string[];
  userStore: UserStore;
  onRowPress: (router: Router<MainStackParamList>, event: { data: T }, store: Store<T>) => void;
  getGridControl: () => GridControl | undefined;
  onGridReady?: (gridControl: GridControl) => void;
  table?: TableMetaStore;
}

export function GenericGrid<T>({
  store,
  colDefsMap,
  getGridControl,
  onRowPress,
  router,
  sortableCols,
  theme,
  userStore,
  onGridReady,
  table,
}: GenericGridProps<T>) {
  return (
    <Grid
      getMoreRows={async () => {
        if (store.pageInfo.hasNext) {
          await store.updatePageSize();
        }
      }}
      getRowData={() => store.items}
      getTableConfig={() => getTableConfig(store, router, colDefsMap, onRowPress, table)}
      minRowHeight={64}
      onColSizeChange={(event) => handleOnColSizeChange(userStore, event, table)}
      onMoveColumn={(event) => handleOnMoveColumn(userStore, event, table)}
      onPinUnpinCol={(event) => handleOnPinUnpinCol(userStore, event, table)}
      onShowHideCol={(event) => handleOnShowHideCol(userStore, event, table)}
      onGridReady={onGridReady}
      onDataUpdated={() => handleTableDataUpdated(store, getGridControl())}
      getDefaultHeaderComponent={() =>
        getHeaderComponent(
          theme,
          (columnId) => {
            const fieldName = columnId;
            return store.getSortFieldStatus(fieldName);
          },
          store,
          sortableCols,
          (columnId) => handleColumnHeaderOnPress(userStore, store, columnId, table),
        )
      }
    />
  );
}

const handleTableDataUpdated = async <T,>(store: Store<T>, gridControl?: GridControl) => {
  if (store.selection && !store.pageInfo.hasPrevious) {
    gridControl?.scrollToItem(store.selection);
  }
  if (store.selection) {
    const node = gridControl?.getRowNode(store.selection);
    if (node) node.setSelected(true);
  }
};

const handleColumnHeaderOnPress = async <T,>(
  userStore: UserStore,
  store: Store<T>,
  columnId: string,
  table?: TableMetaStore,
) => {
  //@TODO refactor opportunity list store to be consistent with other stores
  if (store.queryInProgress) return;

  const propertySort = store.getSortedColumn();

  const ascending = propertySort?.fieldName !== columnId || !propertySort.ascending?.valueOf();

  if (!ascending) {
    store.setSortedColumn({ fieldName: columnId, ascending });
  } else {
    store.setSortedColumn({ fieldName: columnId, ascending });
  }
  await store.queryItems();
  table?.columnSorted({ colId: columnId, ascending });

  userStore.saveUserApplicationMeta();
};

const handleOnColSizeChange = (
  userStore: UserStore,
  event: { colId: string; newWidth: number },
  table?: TableMetaStore,
) => {
  table?.columnSizeChanged({ ...event });
  userStore.saveUserApplicationMeta();
};

const handleOnMoveColumn = (
  userStore: UserStore,
  event: { colId: string; newIndex: number },
  table?: TableMetaStore,
) => {
  table?.columnMoved({ ...event });
  userStore.saveUserApplicationMeta();
};

const handleOnPinUnpinCol = (
  userStore: UserStore,
  event: { colId: string; pinned?: 'left' | 'right' | boolean | null },
  table?: TableMetaStore,
) => {
  table?.columnPinnedUnpinned(event);
  userStore.saveUserApplicationMeta();
};

const handleOnShowHideCol = (
  userStore: UserStore,
  event: { colId: string; visible: boolean },
  table?: TableMetaStore,
) => {
  table?.columnShownHidden(event);
  userStore.saveUserApplicationMeta();
};

const getHeaderComponent = <T,>(
  theme: ReactNativePaper.ThemeProp,
  getSortIndicator: (columnId: string) => SortIndicator,
  store: Store<T>,
  sortableCols: string[],
  onColumnPress?: (columnId: string) => void,
) => {
  const {
    styles: { components, fontSizes },
  } = theme;
  return (props: any) => {
    const fieldName = props.sortPath ? props.column.getColId() + '.' + props.sortPath : props.column.getColId();


    return (
      <ColumnHeader
        sortableCols={sortableCols}
        getSortIndicator={getSortIndicator}
        onColumnPress={onColumnPress}
        fieldName={fieldName}
        filterInfo={props.filterInfo}
        store={store}
      >
        {props.headerRenderer ? (
          props.headerRenderer()
        ) : (
          <Text style={[components.dataTableHeaderTextStyle, fontSizes.small]}>{props.displayName}</Text>
        )}
      </ColumnHeader>
    );
  };
};

const getTableConfig = <T,>(
  store: Store<T>,
  router: Router<MainStackParamList>,
  colDefsMap: Record<string, ColDef>,
  onRowPress: (
    router: Router<MainStackParamList>,
    event: {
      data: T;
    },
    store: Store<T>,
  ) => void,
  table?: TableMetaStore,
) => {
  // add the tenant or user's col settings to the base ColDefs
  const colDefs =
    table?.tableMeta?.cols?.map((col) => {
      const colDef = colDefsMap[col.colId];
      if (colDef) {
        colDef.width = col.colWidth;
        colDef.pinned = col.pinned;
        colDef.hide = col.hidden;
        if (!colDef.onCellClicked) colDef.onCellClicked = (event) => onRowPress(router, event, store);
        return colDef;
      } else {
        return { field: col.colId, header: col.colId };
      }
    }) || [];
  return {
    colDefs: colDefs,
  };
};
