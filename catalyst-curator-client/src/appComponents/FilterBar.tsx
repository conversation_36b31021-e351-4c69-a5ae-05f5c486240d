import Feather from '@expo/vector-icons/Feather';
import { observer, useLocalObservable } from 'mobx-react';
import { StyleProp, View, ViewStyle, useWindowDimensions } from 'react-native';
import { IconButton, withTheme } from 'react-native-paper';
import { useRoute } from '@react-navigation/native';
import { OpportunityListStore, OpportunityReportsStore, TenantStore, UserStore } from '../stores';
import { DownloadOpportunityDialog } from '../pages/sections/opportunity/DownloadOpportunityDialog';
import { IconLabel } from '../lib/ui/atoms/IconLabel';
import { SearchInput } from '../lib';
import { SMALL_DEVICE_BREAKPOINT } from '../constants/breakpoints';
import { PortfolioDropdown } from '../pages/sections/analytics/PortfolioDropdown';
import { getPortfolios } from '../lib/Privileges';
import { ResourcesStore } from '../stores/ResourcesStore';
import { RoleNames } from '../services/codegen/types';
import { toPng } from 'html-to-image';

interface FilterBarProps {
  // may be observable
  onChooseDownload: (options: { value: 'all' | 'view' }) => void;
  userStore: UserStore;
  opportunityListStore: OpportunityListStore;
  style?: StyleProp<ViewStyle>;
  theme: ReactNativePaper.ThemeProp;
  usePortfolioFilter: boolean;
  resourcesStore?: ResourcesStore;
  opportunityReportsStore?: OpportunityReportsStore;
  tenantStore: TenantStore;
}

export const FilterBar = withTheme(
  observer((props: FilterBarProps) => {
    const {
      onChooseDownload,
      opportunityListStore,
      usePortfolioFilter,
      userStore,
      resourcesStore,
      opportunityReportsStore,
      tenantStore,
      theme: {
        colors,
        styles: { margins, paddings, components, fonts },
      },
      style,
    } = props;

    // TODO: This is not an advised approach for handling how you toggle component sections.
    // The functionality this is toggling could be driven with a prop and should be.
    // This was put in for a hotfix on IN-1171 but should be refactored. This code is
    // hiding the search input if the page displayed is 'overview'
    const routeInfo = useRoute();
    const showSearchInput = routeInfo.name.toLowerCase() === 'overview' ? false : true;

    const localStore = useLocalObservable(() => ({
      chooseDownloadVisible: false,
      setChooseDownloadVisible(value: boolean) {
        this.chooseDownloadVisible = value;
      },
    }));

    const { width } = useWindowDimensions();
    const shouldFilterStack = width <= SMALL_DEVICE_BREAKPOINT;
    const userNotAnalyst = !userStore.user?.roles?.some((role) => role.name === RoleNames.Analyst);

    function handleDownload() {
      const node = document.getElementById('overview');

      if (!node) return;
      toPng(node, {
        quality: 1,
        fetchRequestInit: { cache: 'no-cache' },
        cacheBust: true,
      })
        .then((dataUrl) => {
          const year = new Date().getFullYear();
          const month = new Date().getMonth() + 1;
          const day = new Date().getDate();
          const fileName = `${tenantStore.tenantHandleOrAlias}-${[year, month, day].join('-')}-analytics-overview.png`;
          const link = document.createElement('a');
          link.download = fileName;
          link.href = dataUrl;
          link.click();
        })
        .catch((error) => {
          console.error('oops, something went wrong!', error);
        });
    }

    const shouldShowDropdown =
      userStore.privilegeGroups && userStore.privilegeGroups && Number(userStore.privilegeGroups.length) > 1;

    const downloadDisabled = Boolean(!userStore.user);

    const { keywordSearchValue } = opportunityListStore;
    function setKeywordSearchValue(value: string) {
      opportunityListStore.keywordSearchValue = value;
      if (value.length <= 0) submitSearchQuery();
    }

    function submitSearchQuery() {
      opportunityListStore.queryItems();
    }

    return (
      <View
        style={[
          {
            flexDirection: shouldFilterStack ? 'column' : 'row',
            width: shouldFilterStack ? '100%' : undefined,
            justifyContent: 'flex-end',
            flex: 1,
            gap: shouldFilterStack ? 8 : 16,
          },
          style,
        ]}
      >
        {showSearchInput && (
          <SearchInput
            style={[
              {
                maxWidth: shouldFilterStack ? '100%' : 400,
                marginLeft: shouldFilterStack ? 0 : margins.LeftML.marginLeft,
                flex: 1,
              },
            ]}
            searchBarStyle={[{ backgroundColor: colors.background, justifyContent: 'center' }]}
            getValue={() => keywordSearchValue}
            setValue={setKeywordSearchValue}
            onSubmitEditing={submitSearchQuery}
            onIconPress={submitSearchQuery}
          />
        )}
        {usePortfolioFilter && shouldShowDropdown && resourcesStore && opportunityReportsStore && !userNotAnalyst && (
          <PortfolioDropdown
            opportunityListStore={opportunityListStore}
            resourcesStore={resourcesStore}
            userStore={userStore}
            opportunityReportsStore={opportunityReportsStore}
            style={{ height: 38 }}
            anchor={
              <IconLabel
                iconName="account-group"
                style={[
                  // components.buttonTertiaryStyle,
                  // paddings.RightM,
                  { flex: 1, paddingLeft: 2, height: 38, maxHeight: undefined },
                ]}
                textStyle={[fonts.regularTitle, paddings.LeftS, { color: colors.textSecondary }]}
              >
                {'Portfolios'} (
                {getPortfolios(props.userStore.user?.privilegeGroups).length === props.resourcesStore?.resources?.length
                  ? 'ALL'
                  : props.resourcesStore?.resources?.length}
                )
              </IconLabel>
            }
          />
        )}
        <IconButton
          icon={() => <Feather name="download" size={24} color="black" />}
          // mode="contained"
          style={[
            {
              margin: 0,
              alignSelf: 'center',
              width: shouldFilterStack ? '100%' : undefined,
              display: downloadDisabled ? 'none' : 'flex',
            },
          ]}
          theme={props.theme}
          disabled={downloadDisabled}
          onPress={() => {
            if (window.location.pathname.includes('overview')) {
              handleDownload();
            } else localStore.setChooseDownloadVisible(true);
          }}
        />
        <DownloadOpportunityDialog
          getVisible={() => localStore.chooseDownloadVisible}
          onChooseDownload={(options) => {
            localStore.setChooseDownloadVisible(false);
            onChooseDownload(options);
          }}
          onDismiss={() => localStore.setChooseDownloadVisible(false)}
        />
      </View>
    );
  }),
);
