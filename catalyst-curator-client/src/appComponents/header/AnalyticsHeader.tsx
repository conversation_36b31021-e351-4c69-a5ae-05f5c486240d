import { observer } from 'mobx-react';
import { withTheme } from 'react-native-paper';
import { AnalyticsStackParamList } from '../../routing/analytics/screens';
import { Router } from '../../platform/Router';
import { analyticsTabs } from '../../constants/tabs';
import { View, useWindowDimensions } from 'react-native';
import { OpportunityListStore, OpportunityReportsStore, TenantStore, UserStore } from '../../stores';
import { GridHeader } from '../GridHeader';
import { ApplicationMetaStore } from '../../stores/ApplicationMetaStore';
import { Link } from '@react-navigation/native';
import { RootStackParamList } from '../../routing/screens';
import { Title } from '../../lib';
import { SMALL_DEVICE_BREAKPOINT } from '../../constants/breakpoints';
import { ResourcesStore } from '../../stores/ResourcesStore';
import { StoresProvider } from '../../lib/stores/StoresProvider';
import { CuratorStores } from '../../platform/initializers';

interface AnalyticsHeaderProps {
  router: Router<AnalyticsStackParamList>;
  theme: ReactNativePaper.ThemeProp;
  userStore: UserStore;
  resourcesStore: ResourcesStore;
  opportunityReportsStore: OpportunityReportsStore;
  tenantStore: TenantStore;
  navGroup?: string;
  setEventFilterInfo: () => void;
}

export const AnalyticsHeader = observer(
  withTheme(
    ({
      router,
      theme,
      navGroup,
      userStore,
      opportunityReportsStore,
      resourcesStore,
      tenantStore,
      setEventFilterInfo,
    }: AnalyticsHeaderProps) => {
      const {
        colors,
        styles: { components, margins, fonts },
      } = theme;

      const { userMetaStore } = userStore;
      const { width } = useWindowDimensions();
      const shouldHeaderStack = width <= SMALL_DEVICE_BREAKPOINT;

      const shouldShowTable =
        userStore.user?.privilegeGroups &&
        userStore.privilegeGroups &&
        userStore.user.privilegeGroups &&
        Number(userStore.privilegeGroups?.length) > 1;

      const storesProvider = StoresProvider.get<CuratorStores>();
      const opportunityListStore = storesProvider.getStore('OpportunityListStore');
      return (
        <View style={[components.pageContainerConstraints]}>
          <View style={[components.flexAll]}>
            <View
              style={[
                components.analyticsHeaderStyle,
                components.flexAll,
                components.shadow,
                {
                  flexDirection: shouldHeaderStack ? 'column' : 'row',
                  justifyContent: 'space-between',
                  backgroundColor: 'white',
                  height: 500,
                  alignItems: 'center',
                },
              ]}
            >
              <View style={{ flexDirection: 'row' }}>
                {analyticsTabs.map((tab, index) => {
                  if (tab.name === 'Table' && !shouldShowTable) return null;
                  return (
                    <Link<AnalyticsStackParamList & RootStackParamList>
                      key={index}
                      to={{ screen: tab.location }}
                      onPress={() => router.navigate(tab.location)}
                      style={{
                        display: 'flex',
                        flexDirection: 'column',
                        justifyContent: 'center',
                        alignItems: 'center',
                        gap: 4,
                      }}
                    >
                      <View style={[margins.RightL, { justifyContent: 'center', alignItems: 'center' }]}>
                        <Title
                          style={[
                            theme.fontSizes.small,
                            fonts.regularTitle,
                            margins.LeftL,
                            {
                              color: colors.textSecondary,
                              textTransform: 'capitalize',
                            },
                          ]}
                        >
                          {tab.name}
                        </Title>
                      </View>
                      <View
                        style={[
                          navGroup === tab.name
                            ? { backgroundColor: colors.buttonPrimary }
                            : { backgroundColor: colors.border },
                          { height: 2, width: '100%' },
                        ]}
                      />
                    </Link>
                  );
                })}
              </View>
              <GridHeader
                tenantStore={tenantStore}
                handleOnDownload={handleOnDownload}
                opportunityListStore={opportunityListStore}
                userMetaStore={userMetaStore}
                userStore={userStore}
                theme={theme}
                usePortfolioFilter
                title=""
                titleColor={colors.secondary}
                resourcesStore={resourcesStore}
                style={{ width: shouldHeaderStack ? '100%' : undefined }}
                opportunityReportsStore={opportunityReportsStore}
                setEventFilterInfo={setEventFilterInfo}
              />
            </View>
          </View>
        </View>
      );
    },
  ),
);

const handleOnDownload = (
  opportunityListStore: OpportunityListStore,
  applicationMetaStore: ApplicationMetaStore,
  allFields = false,
) => {
  const year = new Date().getFullYear();
  const month = new Date().getMonth() + 1;
  const day = new Date().getDate();
  const fileName = `${[year, month, day].join('-')}-opportunities-analytics.xlsx`;
  const fields = !allFields
    ? applicationMetaStore.anaTable?.tableMeta?.cols?.reduce((prev, currrent) => {
        if (!currrent.hidden) {
          prev.push(currrent.colId);
        }
        return prev;
      }, [] as string[])
    : undefined;

  opportunityListStore.handleDownload(fileName, fields);
};
