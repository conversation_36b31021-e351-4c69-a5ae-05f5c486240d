import { observer } from 'mobx-react';
import { View } from 'react-native';
import { withTheme } from 'react-native-paper';
import { Router } from '../../platform/Router';
import { MainStackParamList, RootStackParamList } from '../../routing/screens';
import { Title } from '../../lib';
import { Link } from '@react-navigation/native';

type HeaderTabProps = {
  theme: ReactNativePaper.ThemeProp;
  router: Router<MainStackParamList>;
  location: keyof MainStackParamList;
  name: string;
  navGroup?: string;
};
export const HeaderTab = observer(
  withTheme(({ theme, router, navGroup, location, name }: HeaderTabProps) => {
    const {
      fonts,
      colors,
      styles: { margins },
    } = theme;

    return (
      <Link<MainStackParamList & RootStackParamList>
        to={{ screen: location }}
        onPress={() => router.navigate(location)}
      >
        <View style={[margins.RightL, { flex: 1, justifyContent: 'center', alignItems: 'center' }]}>
          <Title
            style={[
              theme.fontSizes.small,
              fonts.mediumTitle,
              {
                color: colors.text,
                textTransform: 'uppercase',
              },
            ]}
          >
            {name}
          </Title>
          <View
            style={[
              navGroup === name ? { backgroundColor: colors.buttonPrimary } : {},
              { height: 2, width: '50%', marginVertical: 4 },
            ]}
          />
        </View>
      </Link>
    );
  }),
);
