import { observer } from 'mobx-react';
import { withTheme } from 'react-native-paper';
import { Router } from '../../platform/Router';
import { MainStackParamList } from '../../routing/screens';
import { View, useWindowDimensions } from 'react-native';
import { headerTabs } from '../../constants/tabs';
import { PopupListMenu, Title, UserProfileBadge } from '../../lib';
import { StoresProvider } from '../../lib/stores/StoresProvider';
import { CuratorStores } from '../../platform/initializers';
import { TenantStore } from '../../stores';
import { RoleNames } from '../../services/codegen/types';
import { Icon } from '../../lib/ui/atoms/Icon';

const BADGE_BREAKPOINT = 1300;

export interface SmallDeviceHeaderContentProps {
  router: Router<MainStackParamList>;
  navGroup?: string;
  children?: React.ReactNode;
  theme: ReactNativePaper.ThemeProp;
}
export const SmallDeviceHeaderContent = observer(
  withTheme(({ router, theme, children, navGroup }: SmallDeviceHeaderContentProps) => {
    const { width } = useWindowDimensions();
    const shouldUseBadgeBreakpoint = width < BADGE_BREAKPOINT;
    const storesProvider = StoresProvider.get<CuratorStores>();
    const userStore = storesProvider.getStore('UserStore');
    const isSignedIn = userStore.signedIn;
    const adminDisabled = !userStore.user?.roles?.some((role) => role.name === RoleNames.Admin);
    const analystDisabled = !userStore.user?.roles?.some((role) => role.name === RoleNames.Analyst);

    const tenantStore: TenantStore = storesProvider.getStore('TenantStore');

    const {
      colors,
      styles: { margins },
    } = theme;

    const {
      tenantConfig: { applicationTitle: applicationTitle, sLogo },
    } = tenantStore;

    return (
      <>
        <View
          style={{
            minHeight: 40,
            flexDirection: 'row',
            paddingLeft: 30,
            paddingRight: 30,
            flexShrink: 1,
            elevation: 0,
            alignItems: 'center',
          }}
        >
          {/* @TODO rob convert this to a navigator bar */}
          {isSignedIn && (
            <>
              <PopupListMenu
                style={{ maxWidth: 300 }}
                getMenuItems={() => {
                  const items = headerTabs.map((tab) => {
                    return { label: tab.name, value: tab.location };
                  });
                  if (analystDisabled) items.filter((item) => item.label !== 'analyst');
                  return items;
                }}
                onItemSelected={(item) => {
                  if (item.label === 'Opportunities') {
                    router.navigate('dashboard');
                  } else if (item.label === 'Projects') {
                    router.navigate('projects');
                  } else if (item.label === 'Analytics') {
                    router.navigate('analytics');
                  }
                }}
                anchor={<Icon name="menu" />}
              />
              <View
                style={[
                  margins.LeftM,
                  {
                    flexDirection: 'row',
                    justifyContent: 'center',
                    flex: 1,
                    alignItems: 'stretch',
                    marginTop: 5,
                    marginLeft: shouldUseBadgeBreakpoint ? 42 : margins.LeftM.marginLeft,
                  },
                ]}
              >
                <Title
                  style={[
                    theme?.fontSizes.medium,
                    theme.fonts.mediumTitle,
                    {
                      color: theme?.colors.text,
                      textAlign: 'center',
                      marginHorizontal: 20,
                      textTransform: 'uppercase',
                      marginVertical: 5,
                      letterSpacing: 1.2,
                    },
                  ]}
                >
                  {applicationTitle}
                </Title>
              </View>
              <PopupListMenu
                style={{ maxWidth: 300 }}
                getMenuItems={() => {
                  const items = [
                    { label: 'Basic Settings', value: 'basic' },
                    { label: 'Advanced Settings', value: 'advanced' },
                  ];
                  if (!adminDisabled) items.push({ label: 'User Management', value: 'manage' });
                  items.push({ label: 'Logout', value: 'logout' });
                  return items;
                }}
                onItemSelected={(item) => {
                  if (item.value === 'basic') {
                    router.navigate('profile', { location: 'basic' });
                  } else if (item.value === 'advanced') {
                    router.navigate('profile', { location: 'advanced' });
                  } else if (item.value === 'manage') {
                    router.navigate('profile', { location: 'manage' });
                  } else if (item.value === 'logout') {
                    userStore.signOut();
                  }
                }}
                anchor={
                  <UserProfileBadge
                    {...{
                      firstName: userStore.user?.firstName,
                      lastName: userStore.user?.lastName,
                      style: { marginHorizontal: shouldUseBadgeBreakpoint ? 0 : 20 },
                      avatarStyle: { backgroundColor: colors.mediumGray },
                      textStyle: [
                        theme.fontSizes.xSmall,
                        theme.fonts.mediumTitle,
                        { color: colors.text, display: shouldUseBadgeBreakpoint ? 'none' : 'flex' },
                      ],
                      avatarTextSize: 47,
                    }}
                  />
                }
              />
            </>
          )}
        </View>
        {children}
      </>
    );
  }),
);
