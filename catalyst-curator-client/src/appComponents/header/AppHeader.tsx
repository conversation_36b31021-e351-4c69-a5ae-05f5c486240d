import { observer } from 'mobx-react';
import React from 'react';
import { View, useWindowDimensions } from 'react-native';
import { withTheme } from 'react-native-paper';
import { Router } from '../../platform/Router';
import { MainStackParamList } from '../../routing/screens';
import { DesktopHeaderContent } from './DesktopHeaderContent';
import { SmallDeviceHeaderContent } from './SmallDeviceHeaderContent';
import { SMALL_DEVICE_BREAKPOINT } from '../../constants/breakpoints';

export interface AppHeaderProps {
  router: Router<MainStackParamList>;
  navGroup?: string;
  showBack?: boolean;
  previousTitle?: string;
  children?: React.ReactNode;
  theme: ReactNativePaper.ThemeProp;
}

export const AppHeader = observer(
  withTheme(({ router, navGroup, children, theme }: AppHeaderProps) => {
    const {
      colors,
      styles: { components },
    } = theme;

    const { width } = useWindowDimensions();

    const shouldUseLargeHeader = width > SMALL_DEVICE_BREAKPOINT;

    return (
      <View
        style={[
          components.pageContainerConstraints,
          { borderBottomWidth: 1, borderColor: colors.border, backgroundColor: colors.surface },
        ]}
      >
        <View
          style={[
            components.globalPageConstraints,
            {
              flex: 1,
              flexDirection: 'column',
              elevation: 0,
              alignItems: 'stretch',
              backgroundColor: colors.surface,
              margin: 0,
              padding: 0,
            },
          ]}
        >
          {shouldUseLargeHeader ? (
            <DesktopHeaderContent router={router} navGroup={navGroup} theme={theme}>
              {children}
            </DesktopHeaderContent>
          ) : (
            <SmallDeviceHeaderContent router={router} theme={theme} navGroup={navGroup}>
              {children}
            </SmallDeviceHeaderContent>
          )}
        </View>
      </View>
    );
  }),
);
