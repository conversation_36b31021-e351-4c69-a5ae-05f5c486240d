import { observer } from 'mobx-react';
import { Image, View, useWindowDimensions } from 'react-native';
import { withTheme } from 'react-native-paper';
import { PopupListMenu, Title, UserProfileBadge } from '../../lib';
import { HeaderTab } from './HeaderTab';
import { StoresProvider } from '../../lib/stores/StoresProvider';
import { CuratorStores } from '../../platform/initializers';
import { Router } from '../../platform/Router';
import { MainStackParamList } from '../../routing/screens';
import { ReactNode } from 'react';
import { TenantStore } from '../../stores';
import { Vr } from '../../lib/ui/atoms/Vr';
import { headerTabs } from '../../constants/tabs';
import { RoleNames } from '../../services/codegen/types';

const BADGE_BREAKPOINT = 1300;

type DesktopHeaderContentProps = {
  theme: ReactNativePaper.ThemeProp;
  children: ReactNode;
  router: Router<MainStackParamList>;
  navGroup?: string;
};
export const DesktopHeaderContent = observer(
  withTheme(({ children, router, theme, navGroup }: DesktopHeaderContentProps) => {
    const { width } = useWindowDimensions();
    const shouldUseBadgeBreakpoint = width < BADGE_BREAKPOINT;
    const storesProvider = StoresProvider.get<CuratorStores>();
    const userStore = storesProvider.getStore('UserStore');
    const isSignedIn = userStore.signedIn;
    const adminDisabled = !userStore.user?.roles?.some((role) => role.name === RoleNames.Admin);
    const analystDisabled = !userStore.user?.roles?.some((role) => role.name === RoleNames.Analyst);
    const tenantStore: TenantStore = storesProvider.getStore('TenantStore');
    const logoSize = tenantStore.tenantConfig.sLogoSize || { width: 100, height: '100%' };

    const {
      colors,
      styles: { margins },
    } = theme;

    const {
      tenantConfig: { applicationTitle: applicationTitle, sLogo },
    } = tenantStore;

    const sourceValue = tenantStore.getImageSourceProp(sLogo);
    return (
      <>
        <View
          style={{
            minHeight: 40,
            flexDirection: 'row',
            paddingLeft: 30,
            paddingRight: 30,
            flexShrink: 1,
            elevation: 0,
            alignItems: 'center',
          }}
        >
          {sourceValue && !shouldUseBadgeBreakpoint && (
            <View
              style={{
                paddingHorizontal: 10,
                paddingVertical: 5,
                backgroundColor: colors.logoBackground,
                justifyContent: 'center',
                alignItems: 'center',
                alignSelf: 'stretch',
              }}
            >
              <Image
                style={{
                  width: logoSize.width,
                  height: logoSize.height as number,
                }}
                resizeMode="contain"
                resizeMethod="auto"
                source={sourceValue}
              />
            </View>
          )}
          <Title
            style={[
              theme?.fontSizes.medium,
              theme.fonts.mediumTitle,
              {
                color: theme?.colors.text,
                textAlign: 'center',
                marginHorizontal: 20,
                textTransform: 'uppercase',
                marginVertical: 5,
                letterSpacing: 1.2,
              },
            ]}
          >
            {applicationTitle}
          </Title>
          {/* @TODO rob convert this to a navigator bar */}
          {isSignedIn && (
            <>
              <Vr style={{ height: '60%' }} />
              <View
                style={[
                  margins.LeftM,
                  {
                    flexDirection: 'row',
                    justifyContent: 'center',
                    flex: 1,
                    alignItems: 'stretch',
                    marginTop: 5,
                  },
                ]}
              >
                {headerTabs.map((tab) => {
                  if (tab.name === 'Analytics' && analystDisabled) return null;
                  return (
                    <HeaderTab
                      key={tab.name}
                      theme={theme}
                      router={router}
                      navGroup={navGroup}
                      location={tab.location}
                      name={tab.name}
                    />
                  );
                })}
              </View>
              <Vr style={{ height: '60%' }} />
              <PopupListMenu
                style={{ maxWidth: 300 }}
                getMenuItems={() => {
                  const items = [
                    { label: 'Basic Settings', value: 'basic' },
                    { label: 'Advanced Settings', value: 'advanced' },
                  ];
                  if (!adminDisabled) items.push({ label: 'User Management', value: 'manage' });
                  items.push({ label: 'Logout', value: 'logout' });
                  return items;
                }}
                onItemSelected={(item) => {
                  if (item.value === 'basic') {
                    router.navigate('profile', { location: 'basic' });
                  } else if (item.value === 'advanced') {
                    router.navigate('profile', { location: 'advanced' });
                  } else if (item.value === 'manage') {
                    router.navigate('profile', { location: 'manage' });
                  } else if (item.value === 'logout') {
                    userStore.signOut();
                  }
                }}
                anchor={
                  <UserProfileBadge
                    {...{
                      firstName: userStore.user?.firstName,
                      lastName: userStore.user?.lastName,
                      style: { marginRight: shouldUseBadgeBreakpoint ? 0 : 20 },
                      avatarStyle: { backgroundColor: colors.mediumGray },
                      textStyle: [
                        theme.fontSizes.xSmall,
                        theme.fonts.mediumTitle,
                        { color: colors.text, display: shouldUseBadgeBreakpoint ? 'none' : 'flex' },
                      ],
                      avatarTextSize: 47,
                    }}
                  />
                }
              />
            </>
          )}
        </View>
        {children}
      </>
    );
  }),
);
