import React from 'react';
import { View } from 'react-native';
import { Text, withTheme } from 'react-native-paper';
import { StoresProvider } from '../lib/stores/StoresProvider';
import { CuratorStores } from '../platform/initializers';
import { TenantStore } from '../stores';

export interface AppFooterProps {
  theme: ReactNativePaper.ThemeProp;
}

export const AppFooter = withTheme(({ theme }: AppFooterProps) => {
  const storesProvider = StoresProvider.get<CuratorStores>();
  const tenantStore: TenantStore = storesProvider.getStore('TenantStore');
  const {
    colors,
    fontSizes,
    styles: { margins, paddings, components },
  } = theme;
  return (
    <View
      style={[
        components.pageContainerConstraints,
        {
          backgroundColor: colors.background,
        },
      ]}
    >
      <View
        style={[
          {
            flex: 1,
            elevation: 0,
            alignItems: 'stretch',
          },
        ]}
      >
        <Text
          style={[{ color: colors.lightGray, alignSelf: 'flex-end' }, fontSizes.xxSmall, paddings.M, paddings.TopMS]}
        >{`Server Version: ${tenantStore.serverVersion} `}</Text>
      </View>
    </View>
  );
});
