import { observer } from 'mobx-react';
import { SafeAreaView, StyleProp, ViewStyle } from 'react-native';
import { withTheme } from 'react-native-paper';
import UserStore from './stores/UserStore';
import { useEffect, useRef } from 'react';
import { DebounceFn, Debouncer } from './lib';

interface AppBackgroundTasksProp {
  userStore: UserStore;
  theme: ReactNativePaper.ThemeProp;
  logoutAfterMs?: number;
  children?: React.ReactNode;
  onLayoutRootView?: () => Promise<void>;
}

let inactivityTimerRef: ReturnType<typeof setTimeout> | null;

const AppBackgroundTasks = withTheme(
  observer((props: AppBackgroundTasksProp) => {
    const {
      userStore,
      logoutAfterMs,
      theme: {
        styles: { components },
      },
      children,
      onLayoutRootView,
    } = props;

    const { signedIn } = userStore;

    // Use this debouncer to reduce chatter around mouse movements
    const debouncerRef = useRef(Debouncer()).current;

    // Clear timer that signs the user out
    function killTimer() {
      if (!inactivityTimerRef) return;
      clearTimeout(inactivityTimerRef);
      inactivityTimerRef = null;
    }

    // Create an active timer that will sign the user out after X amount of time.
    const startInactivityTimer = () => {
      // If we are signed in and already have a timer running, exit
      if (signedIn && inactivityTimerRef) return;

      // Create timer that will log the user out after logoutAfterMs is reached.
      inactivityTimerRef = setTimeout(async () => {
        await userStore.signOut();
        killTimer();
      }, logoutAfterMs);
    };

    // Get a fresh timer
    const resetInactivityTimer = () => {
      killTimer();
      startInactivityTimer();
    };

    // Handler for mouse or gesture activity
    const handleResetInactivityTimer = (debouncer: DebounceFn, debounceTime: number) => {
      debouncer(() => {
        resetInactivityTimer();
      }, debounceTime);
    };

    // Wait until the user is signed in before starting a timer
    useEffect(() => {
      startInactivityTimer();
    }, [signedIn]);

    if (!signedIn) return children;

    return (
      <SafeAreaView
        style={[components.flexAll]}
        onLayout={onLayoutRootView}
        onTouchStart={() => handleResetInactivityTimer(debouncerRef, 50)}
        onPointerMove={() => handleResetInactivityTimer(debouncerRef, 500)}
        onPointerDown={() => handleResetInactivityTimer(debouncerRef, 50)}
      >
        {children}
      </SafeAreaView>
    );
  }),
);

AppBackgroundTasks.defaultProps = {
  logoutAfterMs: 900000,
};
export default AppBackgroundTasks;
