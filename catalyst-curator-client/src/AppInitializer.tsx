import { observer, useLocalObservable } from 'mobx-react';
import App from './index';
import { Platform } from 'react-native';
import * as SplashScreen from 'expo-splash-screen';
import { PlatformFactory } from './platform/PlatformFactory';

SplashScreen.preventAutoHideAsync();

if (Platform.OS === 'web' && typeof window !== 'undefined') {
  require('smoothscroll-polyfill').polyfill();
}

export default observer(() => {
  const localStore = useLocalObservable(() => ({
    appIsReady: false,
    setAppIsReady(value: boolean) {
      this.appIsReady = value;
    },
  }));
  async function prepare() {
    try {
      const platform = PlatformFactory.getPlatformInitializer();
      await platform();
    } catch (e) {
      console.warn(e);
    } finally {
      await SplashScreen.hideAsync();
      localStore.setAppIsReady(true);
    }
  }

  if (localStore.appIsReady) {
    return <App />;
  }

  prepare();
  return null;
});