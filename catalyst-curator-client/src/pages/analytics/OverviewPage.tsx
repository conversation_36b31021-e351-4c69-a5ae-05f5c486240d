import { withTheme } from 'react-native-paper';
import { View } from 'react-native';
import { observer } from 'mobx-react';

import { DataCard } from '../sections/analytics/DataCard';
import { OpportunityReportsStore } from '../../stores/OpportunityReportsStore';
import { ProblemSubmissionsVisual } from '../sections/analytics/ProblemSubmissionsVisual';
import { PieChartVisual } from '../sections/analytics/PieChartVisual';
import { CampaignVisual } from '../sections/analytics/CampaignVisual';
import { DataVisualTooltipText } from '../../constants/DataVisualTooltipText';
import { OpportunityListStore, TenantStore, UserStore } from '../../stores';
import getHeaderConfig from '../../routing/analytics/getHeaderConfig';
import { Router } from '../../platform/Router';
import { AnalyticsStackParamList } from '../../routing/analytics/screens';
import { Route } from '@react-navigation/native';
import { AnalyticsHeader } from '../../appComponents/header/AnalyticsHeader';
import { ResourcesStore } from '../../stores/ResourcesStore';

interface TablePageProps {
  theme: ReactNativePaper.ThemeProp;
  userStore: UserStore;
  opportunityReportsStore: OpportunityReportsStore;
  opportunityListStore: OpportunityListStore;
  resourcesStore: ResourcesStore;
  tenantStore: TenantStore;
  router: Router<AnalyticsStackParamList>;
  route: Route<string>;
}

export const OverviewPage = withTheme(
  observer(
    ({
      theme,
      router,
      route,
      userStore,
      opportunityReportsStore,
      opportunityListStore,
      resourcesStore,
      tenantStore,
    }: TablePageProps) => {
      const {
        styles: { components, paddings, margins },
      } = theme;

      const appHeaderProps = getHeaderConfig(route.name as keyof AnalyticsStackParamList);

      function setEventFilterInfo() {
        const eventsCheckboxes = opportunityListStore.listFilterStores.campaignListFilter.info;
        if (location.pathname.includes('overview')) {
          eventsCheckboxes.splice(0, 1);
          const noneIndex = eventsCheckboxes.findIndex((info) => info.label === 'None');
          if (noneIndex !== -1) {
            eventsCheckboxes.splice(noneIndex, 1);
          }
        }
        return eventsCheckboxes;
      }

      return (
        <View style={[components.flexAll]}>
          <AnalyticsHeader
            {...appHeaderProps}
            router={router}
            userStore={userStore}
            resourcesStore={resourcesStore}
            opportunityReportsStore={opportunityReportsStore}
            tenantStore={tenantStore}
            setEventFilterInfo={setEventFilterInfo}
          />
          <View style={[components.flexAll]}>
            {/* <View style={[components.flexAll, paddings.TopML, paddings.HorizontalML, { gap: 24, flex: 1 }]}> */}
            <View style={[{ padding: 20, gap: 24, flexShrink: 1 }]} id="overview">
              <View style={[{ alignItems: 'flex-start', flexDirection: 'row', gap: 16, maxHeight: 360, flex: 1 }]}>
                <DataCard
                  style={{ flex: 1 }}
                  data={[
                    {
                      name: 'Total Submissions',
                      value: Number(opportunityReportsStore.submissionReportData?.totalSubmissions),
                      tooltipText: DataVisualTooltipText.totalSubmissions,
                    },
                    {
                      name: 'Total Submitters',
                      value: Number(opportunityReportsStore.submissionReportData?.totalSubmitters),
                      tooltipText: DataVisualTooltipText.submitters,
                    },
                    {
                      name: 'Uncurated Submissions',
                      value: Number(opportunityReportsStore.submissionReportData?.uncuratedOpportunities),
                      tooltipText: DataVisualTooltipText.uncuratedSubmissions,
                    },
                  ]}
                />
                <ProblemSubmissionsVisual opportunityReportsStore={opportunityReportsStore} userStore={userStore} />
              </View>
              <View
                style={[
                  {
                    alignItems: 'flex-start',
                    flexDirection: 'row',
                    gap: 16,
                    // width: '100%',
                    // height: '100%',
                    maxHeight: 360,
                    flex: 1,
                  },
                ]}
              >
                <PieChartVisual
                  title="Problem Status"
                  data={opportunityReportsStore.statusReportData}
                  tooltipText={DataVisualTooltipText.status}
                />
                <PieChartVisual
                  title="Problem Priority"
                  data={opportunityReportsStore.priorityReportData}
                  tooltipText={DataVisualTooltipText.priority}
                />
                <PieChartVisual
                  title="Warfighting Functions"
                  data={opportunityReportsStore.WFFReportData}
                  tooltipText={DataVisualTooltipText.wff}
                />
                <CampaignVisual opportunityReportsStore={opportunityReportsStore} />
              </View>
            </View>
          </View>
        </View>
      );
    },
  ),
);
