import { observer } from 'mobx-react';
import React, { version } from 'react';
import { withTheme } from 'react-native-paper';
import { TenantStore, UserStore } from '../stores';
import { MessageDialog } from '../lib/ui/molecules/MessageDialog';
import { ReleaseNote } from '../lib';
export interface WelcomeDialogProps {
  userStore: UserStore;
  tenantStore: TenantStore;
  theme: ReactNativePaper.ThemeProp;
}

export const WelcomeDialog = withTheme(
  observer(({ userStore, tenantStore, theme }: WelcomeDialogProps) => {
    const versionReleaseNotes = getVersionReleaseNotes(tenantStore);
    if (!versionReleaseNotes) {
      if (isDifferentVersion(userStore, tenantStore)) {
        userStore.setValue('options', {
          ...userStore.getValue('options', {}),
          lastUsedServerVersion: tenantStore.serverVersion,
        });
        userStore.saveCurrentUser();
      }
      return null;
    }
    const options = userStore.getValue('options', {});
    return (
      <MessageDialog
        confirmLabel={'Close'}
        messageProps={{}}
        getVisible={
          () => isDifferentVersion(userStore, tenantStore)
          // () => true
        }
        dismissable={true}
        onConfirm={() => {
          userStore.setValue('options', { ...options, lastUsedServerVersion: tenantStore.serverVersion });
          userStore.saveCurrentUser();
        }}
        message={versionReleaseNotes.text}
        title={versionReleaseNotes.title}
        subtitle={`Release version: ${tenantStore.serverVersion}`}
      />
    );
  }),
);

const getVersionReleaseNotes = (tenantStore: TenantStore) => {
  if (!tenantStore?.tenantContent?.releaseNotes) return null;

  return (tenantStore?.tenantContent?.releaseNotes as any)[tenantStore.serverVersion] as ReleaseNote | null;
};

const isDifferentVersion = (userStore: UserStore, tenantStore: TenantStore) => {
  if (!userStore.signedIn || !tenantStore.serverVersion) return false;
  const lastUsedServerVersion = userStore.user?.options?.lastUsedServerVersion;
  const currentServerVersion = tenantStore.serverVersion;
  return lastUsedServerVersion !== currentServerVersion;
};
