import { BlurView } from 'expo-blur';
import { canOpenURL, openURL } from 'expo-linking';
import { observer, useLocalObservable } from 'mobx-react';
import { ImageBackground, Pressable, View } from 'react-native';
import { Text, TouchableRipple, withTheme } from 'react-native-paper';
import ModalSetTenant from '../appComponents/ModalSetTenant';
import { Button, Label, LabeledTextInput } from '../lib';
import { CuratorStores } from '../platform/initializers';
import { Router } from '../platform/Router';
import { StoresProvider } from '../lib/stores/StoresProvider';
import { MainStackParamList } from '../routing/screens';
import { ApplicationStore } from '../stores';
import { TenantStore } from '../stores/TenantStore';
import UserStore from '../stores/UserStore';

interface LoginPageProps {
  tenantStore: TenantStore;
  applicationStore: ApplicationStore;
  userStore: UserStore;
  router: Router<MainStackParamList>;
  theme: ReactNativePaper.ThemeProp;
}

interface Locals {
  userId?: string;
  password?: string;
  showTenantDialog?: boolean;
  tenantCounter: number;
  setUserId(value: string): void;
  setPassword(value: string): void;
  setShowTenantDialog(value: boolean): void;
  setTenantCounter(value: number): void;
}

export const LoginPage = withTheme(
  observer((props: LoginPageProps) => {
    const { userStore, theme, tenantStore, applicationStore } = props;
    const { fonts, fontSizes, styles, colors } = theme;
    const { margins } = styles;
    const { medium } = fonts;
    const errorMessage = userStore.errorMessage;

    const localStore = useLocalObservable<Locals>(() => ({
      userId: undefined,
      password: undefined,
      showTenantDialog: undefined,
      tenantCounter: 0,
      setUserId(value: string) {
        this.userId = value;
      },
      setPassword(value: string) {
        this.password = value;
      },
      setShowTenantDialog(value: boolean) {
        this.showTenantDialog = value;
      },
      setTenantCounter(value: number) {
        this.tenantCounter = value;
      },
    }));

    const {
      tenantHandleOrAlias,
      tenantConfig: { backdropImage },
    } = tenantStore;
    const backdropImageSource = tenantStore.getImageSourceProp(backdropImage);

    const signIn = () => {
      if (localStore.userId?.length && localStore.password?.length)
        userStore.signIn(tenantHandleOrAlias, localStore.userId, localStore.password);
    };

    return (
      <View
        style={{
          flexDirection: 'column',
          flexGrow: 1,
          justifyContent: 'flex-start',
          marginHorizontal: '10%',
          marginVertical: 100,
          alignItems: 'center',
        }}
      >
        <ImageBackground
          source={backdropImageSource}
          resizeMode="cover"
          imageStyle={{ borderRadius: 5, width: '100%' }}
          style={{
            flexDirection: 'row',
            flex: 1,
            justifyContent: 'flex-start',
            alignItems: 'stretch',
            width: '100%',
            maxWidth: 1200,
            maxHeight: 600,
          }}
        >
          <BlurView
            intensity={40}
            tint="dark"
            style={{
              borderTopLeftRadius: 5,
              borderTopRightRadius: 5,
              flexDirection: 'column',
              justifyContent: 'space-between',
              minWidth: 420,
            }}
          >
            {/* @TODO rob make this overlay color configurable */}
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'center',
                paddingHorizontal: 45,
                paddingTop: 40,
                paddingBottom: 40,
                backgroundColor: 'rgba(32, 32, 32, 0.75)',
                flex: 1,
                borderTopLeftRadius: 5,
                borderTopRightRadius: 5,
              }}
            >
              <View style={{ alignItems: 'stretch', flex: 1 }}>
                <View
                  style={{
                    flexDirection: 'row',
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                >
                  <Label
                    textStyle={[
                      theme.fontSizes.medium,
                      fonts.mediumTitle,
                      {
                        color: colors.textLight,
                        paddingTop: 20,
                        paddingBottom: 15,
                      },
                    ]}
                  >
                    {'Innovation Center Login'}
                  </Label>
                </View>
                <LabeledTextInput
                  style={[margins.TopM]}
                  textInputProps={{
                    multiline: false,
                    spellcheck: false,
                    numberOfLines: 1,
                    getValue: () => localStore.userId,
                    setValue: (value) => localStore.setUserId(value),
                    placeholder: '<EMAIL>',
                    containerStyle: { backgroundColor: 'rgba(255,255,255,1)', borderWidth: 0 },
                  }}
                  labelProps={{ textStyle: { color: colors.textLight } }}
                  labelText="Email"
                />
                <LabeledTextInput
                  style={[margins.BottomL]}
                  textInputProps={{
                    multiline: false,
                    spellcheck: false,
                    numberOfLines: 1,
                    secure: true,
                    getValue: () => localStore.password,
                    setValue: (value) => localStore.setPassword(value),
                    onSubmitEditing: signIn,
                    containerStyle: { backgroundColor: 'rgba(255,255,255,1)' },
                  }}
                  labelProps={{ textStyle: { color: colors.textLight, borderWidth: 0 } }}
                  labelText="Password"
                />
                <Button type="primary" iconName="play" iconRight={true} onPress={signIn}>
                  {'CONTINUE'}
                </Button>
                <View
                  style={{
                    flexDirection: 'row',
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                >
                  <Text style={{ ...medium, color: colors.error, height: 10 }}>{`${
                    errorMessage ? errorMessage : ''
                  }`}</Text>
                </View>
              </View>
            </View>
            <View
              style={{
                flexDirection: 'row',
                backgroundColor: '#EDEDED',
                justifyContent: 'center',
                alignItems: 'center',
                paddingVertical: 15,
                paddingHorizontal: 30,
              }}
            >
              <Label textStyle={{ ...fonts.regular, ...fontSizes.mediumSmall, color: colors.text }}>
                {`Need to submit an Innovation Opportunity? `}
              </Label>
              <TouchableRipple onPress={() => handleOpenInnovationCenter(props)}>
                <Label
                  textStyle={{ ...theme.fonts.medium, color: colors.buttonPrimary, fontWeight: 'bold', marginLeft: 5 }}
                >
                  {'Click Here.'}
                </Label>
              </TouchableRipple>
            </View>
          </BlurView>
          <View
            style={{
              flex: 1,
              justifyContent: 'flex-end',
              alignItems: 'flex-end',
            }}
          >
            <Pressable
              onPress={() => handleHiddenSetTenant(localStore)}
              style={{ height: 50, width: 50, borderRadius: 50 }}
            >
              <ModalSetTenant
                defaultOverrideTenant={applicationStore.applicationConfig.overrideTenant}
                style={{ width: 300, alignSelf: 'center', backgroundColor: colors.surface }}
                isVisible={localStore.showTenantDialog}
                onDismiss={() => localStore.setShowTenantDialog(false)}
                onDone={async (overrideTenant: string) => {
                  try {
                    localStore.setShowTenantDialog(false);
                    await tenantStore.loadTenant(overrideTenant);
                    StoresProvider.get<CuratorStores>()
                      .getStore('OrganizationStore')
                      .reset(tenantStore.tenantConfig.orgValues);
                  } catch (e) {
                    console.error(e);
                  }
                }}
              />
            </Pressable>
          </View>
        </ImageBackground>
        <View>
          <Text
            style={[{ color: colors.lightGray, alignSelf: 'flex-end' }, fontSizes.xxSmall, margins.M, margins.TopS]}
          >{`Server Version: ${tenantStore.serverVersion} `}</Text>
        </View>
      </View>
    );
  }),
);

const handleOpenInnovationCenter = (props: LoginPageProps) => {
  const { applicationStore } = props;
  const { origin: Origin } = applicationStore.applicationConfig;
  if (Origin) {
    canOpenURL(Origin).then((canOpen) => {
      if (canOpen) openURL(Origin);
    }).catch;
  }
};

const handleHiddenSetTenant = (localStore: Locals) => {
  localStore.setTenantCounter(localStore.tenantCounter + 1);
  if (localStore.tenantCounter >= 10) {
    localStore.setTenantCounter(0);
    localStore.setShowTenantDialog(true);
  }
};
