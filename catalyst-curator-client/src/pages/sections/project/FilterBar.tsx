import { observer } from 'mobx-react';
import React from 'react';
import { StyleProp, View, ViewStyle, useWindowDimensions } from 'react-native';
import { IconButton, withTheme } from 'react-native-paper';
import { Button, SearchInput } from '../../../lib';
import { SMALL_DEVICE_BREAKPOINT } from '../../../constants/breakpoints';

interface FilterBarProps {
  // may be observable
  getSearchValue: () => string | undefined;
  setSearchValue: (value: string) => void;
  onClearSearch: () => void;
  onDownload: () => void;
  onSearch: () => void;
  style?: StyleProp<ViewStyle>;
  theme: ReactNativePaper.ThemeProp;
}

export const FilterBar = withTheme(
  observer((props: FilterBarProps) => {
    const {
      getSearchValue,
      setSearchValue,
      onClearSearch,
      onDownload,
      onSearch,
      theme: {
        colors,
        styles: { margins, paddings, components, fontSizes },
      },
      style,
    } = props;

    const { width } = useWindowDimensions();
    const shouldFilterStack = width <= SMALL_DEVICE_BREAKPOINT;
    return (
      <View
        style={[
          {
            flexDirection: shouldFilterStack ? 'column' : 'row',
            width: shouldFilterStack ? '100%' : undefined,
            justifyContent: 'flex-end',
            flex: 1,
            gap: shouldFilterStack ? 8 : 16,
          },
          style,
        ]}
      >
        <SearchInput
          style={[
            {
              maxWidth: shouldFilterStack ? '100%' : 400,
              marginLeft: shouldFilterStack ? 0 : margins.LeftML.marginLeft,
              flex: 1,
            },
          ]}
          searchBarStyle={[{ backgroundColor: colors.background, justifyContent: 'center' }]}
          getValue={getSearchValue}
          setValue={setSearchValue}
          onSubmitEditing={onSearch}
          onIconPress={onSearch}
        />
        <IconButton
          icon="download"
          mode="contained"
          style={[
            props.theme.styles.components.buttonTertiaryStyle,
            { width: shouldFilterStack ? '100%' : undefined, margin: 0, alignSelf: 'center' },
          ]}
          theme={props.theme}
          onPress={onDownload}
        />
      </View>
    );
  }),
);
