import React from 'react';
import { StyleProp, ViewStyle } from 'react-native';
import { withTheme } from 'react-native-paper';
import ProjectsStore from '../../../stores/ProjectsStore';
import { downloadManagerLocation, openWebFileStream } from '../../../utilities/File';
import { FilterBar } from './FilterBar';

interface FilterProjectsBarProps {
  style?: StyleProp<ViewStyle>;
  theme: ReactNativePaper.ThemeProp;
  projectsStore: ProjectsStore;
}

export const FilterProjectsBar = withTheme(({ projectsStore: projectsStore, style }: FilterProjectsBarProps) => {
  const props = {
    style,
    getSearchValue: () => projectsStore.getKeywordSearchValue(),
    setSearchValue: (value: string) => handleOnSearchValueChange(projectsStore, value),
    onClearSearch: () => handleClearSearch(projectsStore),
    onDownload: () => handleOnDownload(projectsStore),
    onSearch: () => handleOnSearch(projectsStore),
  };
  return <FilterBar {...props} />;
});

const handleClearSearch = (projectsStore: ProjectsStore) => {
  projectsStore.resetSearchValues();
  handleOnSearch(projectsStore);
};

const handleOnSearchValueChange = (projectsStore: ProjectsStore, value: string) => {
  projectsStore.setKeywordSearchValue(value);
  if (value.length === 0) handleOnSearch(projectsStore);
};

const handleOnSearch = (projectsStore: ProjectsStore) => {
  projectsStore.queryItems();
};

const handleOnDownload = (projectsStore: ProjectsStore) => {
  const year = new Date().getFullYear();
  const month = new Date().getMonth() + 1;
  const day = new Date().getDate();
  const fileName: string = `${[year, month, day].join('-')}-projects.xlsx`;

  openWebFileStream(`${downloadManagerLocation}/project`, fileName).catch((e) => {
    projectsStore.addError(e);
  });
};
