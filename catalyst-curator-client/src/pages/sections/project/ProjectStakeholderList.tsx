import { View } from 'react-native';
import { StakeholderForm } from '../opportunity/curated-opportunity-groups/StakeholderForm';
import { ProjectStakeholder, ProjectStakeholderType, UpdateOperator } from '../../../services/codegen/types';
import { StakeholderStore } from '../../../stores';
import { withTheme } from 'react-native-paper';
import { observer, useLocalObservable } from 'mobx-react';
import { Button } from '../../../lib';
import { ProjectStore } from '../../../stores/ProjectStore';

type ProjectStakeholderListProps = {
  projectStakeholder: ProjectStakeholder;
  stakeholderStore: StakeholderStore;
  theme: ReactNativePaper.ThemeProp;
  projectStore: ProjectStore;
  onStakeholderChanged: (
    projectStore: ProjectStore,
    operator: UpdateOperator,
    id: string,
    type: ProjectStakeholderType,
  ) => Promise<void>;
};
export const ProjectStakeholderList = withTheme(
  observer(
    ({
      projectStakeholder,
      stakeholderStore,
      onStakeholderChanged,
      projectStore,
      theme,
    }: ProjectStakeholderListProps) => {
      const { colors, fonts } = theme;

      const isStakeholderEditable = (stakeholderId: string) => {
        return locals.editingStakeholderId === stakeholderId;
      };

      const locals = useLocalObservable(() => ({
        editingStakeholderId: null as string | null,
        setEditingStakeholderId(id: string | null) {
          this.editingStakeholderId = id;
        },
      }));

      async function updateStakeholder() {
        if (stakeholderStore.hasErrors) {
          return;
        }

        const oldType = projectStakeholder.type;
        const type = stakeholderStore.getValue('type') as ProjectStakeholderType;
        await stakeholderStore.updateStakeholder(projectStakeholder.stakeholder.id);
        await onStakeholderChanged(projectStore, UpdateOperator.Rm, projectStakeholder.stakeholder.id, oldType);
        await onStakeholderChanged(projectStore, UpdateOperator.Add, projectStakeholder.stakeholder.id, type);
      }

      async function deleteStakeholder() {
        await onStakeholderChanged(
          projectStore,
          UpdateOperator.Rm,
          projectStakeholder.stakeholder.id,
          projectStakeholder.type,
        );
      }

      return (
        <View key={projectStakeholder.id} style={{ backgroundColor: colors.surface, padding: 24, borderRadius: 8 }}>
          <View style={{ flexDirection: 'row', gap: 8, alignItems: 'flex-start' }}>
            <View style={{ flex: 1 }}>
              <StakeholderForm
                stakeholder={projectStakeholder.stakeholder}
                stakeholderStore={stakeholderStore}
                isNew={false}
                isEditing={isStakeholderEditable(projectStakeholder.id)}
                theme={theme}
                isProjectStakeholder
                defaultType={projectStakeholder.type}
              />
              {!isStakeholderEditable(projectStakeholder.id) && (
                <View
                  style={{
                    flexDirection: 'row',
                    gap: 8,
                    justifyContent: 'flex-end',
                    alignItems: 'center',
                    marginTop: 16,
                  }}
                >
                  <Button
                    labelStyle={[{ textTransform: 'capitalize', color: colors.secondaryTextColor }, fonts.mediumTitle]}
                    type="secondary"
                    compact
                    onPress={() => {
                      locals.setEditingStakeholderId(projectStakeholder.id);
                      stakeholderStore.initializeBufferForEditing(
                        projectStakeholder.stakeholder,
                        projectStakeholder.type,
                      );
                    }}
                  >
                    Edit
                  </Button>
                </View>
              )}
              {isStakeholderEditable(projectStakeholder.id) && (
                <View
                  style={{
                    flexDirection: 'row',
                    gap: 8,
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    marginTop: 16,
                  }}
                >
                  <Button
                    labelStyle={[{ textTransform: 'capitalize', color: colors.error }, fonts.mediumTitle]}
                    type="secondary"
                    compact
                    onPress={() => {
                      deleteStakeholder();
                      stakeholderStore.clearBuffer();
                      stakeholderStore.clearErrors();
                    }}
                    iconName="delete"
                  >
                    Delete
                  </Button>
                  <View style={{ gap: 8, flexDirection: 'row' }}>
                    <Button
                      labelStyle={[
                        { textTransform: 'capitalize', color: colors.secondaryTextColor },
                        fonts.mediumTitle,
                      ]}
                      type="secondary"
                      compact
                      onPress={() => {
                        locals.setEditingStakeholderId(null);
                        stakeholderStore.clearBuffer();
                        stakeholderStore.clearErrors();
                      }}
                    >
                      Cancel
                    </Button>
                    <Button
                      labelStyle={[{ textTransform: 'capitalize' }, fonts.mediumTitle]}
                      type="primary"
                      getDisabled={() => stakeholderStore.hasErrors}
                      compact
                      onPress={async () => {
                        await updateStakeholder();
                        locals.setEditingStakeholderId(null);
                      }}
                    >
                      Save
                    </Button>
                  </View>
                </View>
              )}
            </View>
          </View>
        </View>
      );
    },
  ),
);
