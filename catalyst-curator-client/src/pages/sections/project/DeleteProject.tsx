import { observer, useLocalObservable } from 'mobx-react';
import { StyleProp, View, ViewStyle } from 'react-native';
import { Text, withTheme } from 'react-native-paper';
import { Button, Dialog, Hr, LabeledTextInput } from '../../../lib';
import { ClickableText } from '../../../lib/ui/atoms/ClickableText';
import { TitleBar } from '../../../lib/ui/molecules/TitleBar';
import { withAdminRights } from '../../../appComponents/hoc/withRestrictions';
import { ProjectStore } from '../../../stores/ProjectStore';

interface DeleteProjectProps {
  theme: ReactNativePaper.ThemeProp;
  projectStore: ProjectStore;
  style?: StyleProp<ViewStyle>;
  onDeleteProject?: () => void;
}

interface Locals {
  confirmDeleteVisible: boolean;
  deleteText: string;
  setConfirmDeleteVisible: (visible: boolean) => void;
  setDeleteText: (text: string) => void;
}

export const DeleteProject = withTheme(
  observer(({ style, theme, onDeleteProject = () => {} }: DeleteProjectProps) => {
    const { styles, colors, fontSizes } = theme;
    const { margins, paddings } = styles;
    const localStore = useLocalObservable(() => ({
      confirmDeleteVisible: false,
      deleteText: '',
      setConfirmDeleteVisible(visible: boolean) {
        localStore.confirmDeleteVisible = visible;
      },
      setDeleteText(text: string) {
        localStore.deleteText = text;
      },
    }));

    return (
      <>
        <View style={style}>
          <ClickableText
            style={[fontSizes.small, { color: colors.error }]}
            onPress={() => localStore.setConfirmDeleteVisible(true)}
          >
            DELETE PROJECT
          </ClickableText>
        </View>
        <Dialog
          getVisible={() => localStore.confirmDeleteVisible}
          onDismiss={() => handleCancelDeleteProject(localStore)}
        >
          <TitleBar title="Delete Project" onClickClose={() => handleCancelDeleteProject(localStore)} />
          <View style={[paddings.ML]}>
            <Text style={[{ color: colors.error }]}>This project will be permanently deleted.</Text>
            <Hr style={[margins.TopML, margins.BottomML]} />
            <LabeledTextInput
              labelText="Type 'DELETE'"
              textInputProps={{
                getValue: () => localStore.deleteText,
                setValue: (value) => localStore.setDeleteText(value),
              }}
            />
            {localStore.deleteText !== 'DELETE' ? (
              <Button
                compact={false}
                type="primary"
                style={[paddings.VerticalM, { backgroundColor: '#ECEDF5' }]}
                getDisabled={() => true}
                labelStyle={{ color: '#676D79', opacity: 0.4 }}
                onPress={() => {}}
              >
                Delete Project
              </Button>
            ) : (
              <Button
                compact={false}
                type="primary"
                style={[paddings.VerticalM, { backgroundColor: colors.error }]}
                onPress={() => handleConfirmDeleteProject(localStore, onDeleteProject)}
              >
                Delete Project
              </Button>
            )}
          </View>
        </Dialog>
      </>
    );
  }),
);

const handleConfirmDeleteProject = async (locals: Locals, onDeleteProject: () => void) => {
  locals.confirmDeleteVisible = false;
  onDeleteProject();
};

const handleCancelDeleteProject = (locals: Locals) => {
  locals.setConfirmDeleteVisible(false);
};

export const AdminDeleteProject = withAdminRights(DeleteProject);
