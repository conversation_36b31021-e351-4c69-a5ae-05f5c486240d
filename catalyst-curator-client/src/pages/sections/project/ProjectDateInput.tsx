import { observer } from 'mobx-react';
import React from 'react';
import { StyleProp, ViewStyle } from 'react-native';
import { withTheme } from 'react-native-paper';
import { HelperTextProps } from '../../../lib';
import { DateBuffer } from '../../../lib/DateBuffer';
import { DATE_PICKER_DATE_FORMAT } from '../../../lib/ui/molecules/DatePickerInput';
import { LabeledDatePickerInput } from '../../../lib/ui/molecules/LabeledDatePickerInput';
import { ProjectStore } from '../../../stores/ProjectStore';

interface ProjectDateInputProps {
  projectStore: ProjectStore;
  fieldName: string;
  labelText: string;
  style?: StyleProp<ViewStyle>;
  theme: ReactNativePaper.ThemeProp;
  onValidDate: (date: Date | undefined) => void;
}

export const ProjectDateInput = withTheme(
  observer(({ projectStore, fieldName, labelText, onValidDate, style, theme }: ProjectDateInputProps) => {
    const startDateBuffer = new DateBuffer(projectStore.getValue(fieldName), DATE_PICKER_DATE_FORMAT);
    const {
      styles: { margins, paddings, components },
    } = theme;
    return (
      <LabeledDatePickerInput
        style={[components.elementStyle, { flex: 1 }, style]}
        labelText={labelText}
        datePickerInputProps={{
          getDateBuffer: () => startDateBuffer,
          onDebounceDateChange: (dateBuffer) => !dateBuffer.hasError && onValidDate(dateBuffer.date),
        }}
        getHelperTextProps={() => getHelperTextProps(fieldName, projectStore, startDateBuffer)}
      />
    );
  }),
);

const getHelperTextProps = (name: string, projectStore: ProjectStore, startDateBuffer: DateBuffer): HelperTextProps => {
  if (startDateBuffer.hasError) {
    return {
      type: 'error',
      children: `Date format must be ${DATE_PICKER_DATE_FORMAT}`,
    };
  }
  return { type: 'error', children: projectStore.getPropertyError(name) };
};
