import { observer } from 'mobx-react';
import { StyleProp, StyleSheet, View, ViewStyle } from 'react-native';
import { withTheme } from 'react-native-paper';
import { ProjectFormat } from '../../../format/ProjectFormat';
import { UserFormat } from '../../../format/UserFormat';
import { Button, Dates, Hr, Title } from '../../../lib';
import { ClickableText } from '../../../lib/ui/atoms/ClickableText';
import { LabeledValue } from '../../../lib/ui/molecules/LabeledValue';
import { Router } from '../../../platform/Router';
import { MainStackParamList } from '../../../routing/screens';
import { User } from '../../../services/codegen/types';
import { ProjectStore } from '../../../stores/ProjectStore';
import { downloadManagerLocation, openWebFileStream } from '../../../utilities/File';

interface ProjectHeaderProps {
  style?: StyleProp<ViewStyle>;
  projectStore: ProjectStore;
  router: Router<MainStackParamList>;
  onDownload: () => void;
  theme: ReactNativePaper.ThemeProp;
}

export const ProjectHeader = withTheme(
  observer(({ projectStore, style, router, onDownload, theme }: ProjectHeaderProps) => {
    const projectUser: User = projectStore.project?.creator as User;
    const { project } = projectStore;
    if (!project) return null;
    const { colors, fonts, fontSizes, styles } = theme;
    const { margins, paddings, components } = styles;
    const [createdAtDate, createdAtTime] = Dates.asDateAndTimeStringTuple(project?.createdAt) || [];
    const [lastCuratedDate, lastCuratedTime] = Dates.asDateAndTimeStringTuple(project.curationInfo?.lastCurated) || [];

    return (
      <View
        style={[
          _styles.contentHeader,
          components.infoSectionStyle,
          { borderTopWidth: 0 },
          components.pageContainerConstraints,
          style,
        ]}
      >
        <View style={[{ flex: 1 }, components.globalPageConstraints]}>
          <ClickableText
            style={[
              paddings.LeftML,
              paddings.TopM,
              { color: colors.mediumGray, letterSpacing: 1 },
              fontSizes.small,
              fonts.regular,
            ]}
            onPress={() => router.goBack()}
          >
            {'<  Return to Dashboard'}
          </ClickableText>
          <View style={[paddings.HorizontalXL, paddings.BottomML]}>
            <View
              style={[
                { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', flexWrap: 'wrap' },
                margins.BottomM,
                margins.TopML,
              ]}
            >
              <View style={[{ flexDirection: 'row' }, margins.BottomM]}>
                <Title
                  style={[
                    fonts.mediumTitle,
                    {
                      alignSelf: 'center',
                      color: colors.text,
                      textAlign: 'center',
                    },
                  ]}
                >
                  Innovation Project Submission
                </Title>
              </View>
              <Button type="secondary" iconName="download" onPress={() => handleDownload(projectStore)}>
                Download
              </Button>
            </View>
            <Hr />
            <View style={[{ flexDirection: 'row', justifyContent: 'space-between' }, margins.VerticalS]}>
              {getHeaderLabelValue(theme, 'Creator:', () => UserFormat.formatUserLong(projectUser), { flex: 2 })}
              <View style={[{ flexDirection: 'row', flex: 1, justifyContent: 'flex-end', flexWrap: 'wrap' }]}>
                {getHeaderLabelValue(theme, 'Created:', () => createdAtDate || '')}
              </View>
            </View>
            <View style={[{ flexDirection: 'row', justifyContent: 'space-between' }, margins.VerticalS]}>
              {getHeaderLabelValue(theme, 'Editor(s):', () => ProjectFormat.formatProjectCurators(project!) || '', {
                flex: 2,
              })}
              <View style={[{ flexDirection: 'row', flex: 1, justifyContent: 'flex-end', flexWrap: 'wrap' }]}>
                {getHeaderLabelValue(theme, 'Last Changed:', () => lastCuratedDate || '', margins.LeftL)}
              </View>
            </View>
          </View>
        </View>
      </View>
    );
  }),
);

const handleDownload = async (projectStore: ProjectStore) => {
  const year = new Date().getFullYear();
  const month = new Date().getMonth() + 1;
  const day = new Date().getDate();
  const fileName: string = `${[year, month, day].join('-')}-project.xlsx`;
  try {
    await openWebFileStream(`${downloadManagerLocation}/project/${projectStore.project?.id}`, fileName);
  } catch (e: any) {
    projectStore.addError(e);
  }
};

const _styles = StyleSheet.create({
  contentHeader: {
    flexDirection: 'column',
  },
});

const getHeaderLabelValue = (
  theme: ReactNativePaper.ThemeProp,
  label: string,
  getValue: () => string,
  style?: StyleProp<ViewStyle>,
) => {
  const {
    fontSizes,
    fonts,
    styles: { margins },
  } = theme;
  return (
    <LabeledValue
      style={[style, { flexShrink: 1 }]}
      labelProps={{
        textStyle: { ...fontSizes.mediumSmall, ...fonts.medium, flexGrow: 0, flexWrap: 'nowrap', ...margins.BottomM },
      }}
      textValueProps={{ style: { ...fontSizes.small, flex: 1, flexWrap: 'wrap' } }}
      labelText={label}
      getValue={getValue}
    />
  );
};
