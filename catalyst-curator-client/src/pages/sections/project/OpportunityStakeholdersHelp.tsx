import { View } from 'react-native';
import Icons from '@expo/vector-icons/MaterialCommunityIcons';
import { CollapsibleView } from '../../../lib/ui/atoms/CollapsibleView';
import { Stakeholder } from '../../../services/codegen/types';
import { Text, withTheme } from 'react-native-paper';
import { AnimatedView } from '../../../lib/ui/atoms/AnimatedView';
import React from 'react';
import { observer } from 'mobx-react';

interface OpportunityStakeholdersHelpProps {
  getOpportunityStakeholders: () => Stakeholder[] | undefined;
  theme: ReactNativePaper.ThemeProp;
}

export const OpportunityStakeholdersHelp = withTheme(
  observer(({ getOpportunityStakeholders, theme }: OpportunityStakeholdersHelpProps) => {
    const stakeholders = getOpportunityStakeholders();
    const {
      styles: { margins, paddings, components, borders, defaultValues: defaults },
      fonts,
      colors,
    } = theme;
    return (
      <CollapsibleView
        expandIcon="chevron-down"
        collapseIcon="chevron-up"
        style={[{ alignItems: 'flex-end' }]}
        header={
          <View style={[{ flexDirection: 'row', alignItems: 'flex-end' }]}>
            <Icons name={'information'} style={[margins.RightS, { color: colors.buttonPrimary }]} size={18} />
            <Text style={[fonts.light, { color: colors.text }]} theme={theme}>
              View Opportunity Stakeholders
            </Text>
          </View>
        }
      >
        <AnimatedView
          style={[
            paddings.ML,
            margins.BottomML,
            {
              position: 'relative',
              alignItems: 'stretch',
              borderRadius: defaults.componentBorderRadius,
              backgroundColor: colors.lightGray,
            },
          ]}
        >
          {stakeholders?.length ? (
            stakeholders?.map((stakeholder) => (
              <View style={[{ flexDirection: 'row' }]} key={`${stakeholder.name}_${stakeholder.org}`}>
                <Text style={[fonts.regular, paddings.VerticalS, { color: colors.text }]} theme={theme}>
                  {'Name:'}
                </Text>
                <Text style={[fonts.light, paddings.VerticalS, paddings.LeftXS, { color: colors.text }]}>
                  {stakeholder.name}
                </Text>
                <Text style={[fonts.regular, paddings.VerticalS, paddings.LeftS, { color: colors.text }]}>
                  {'Org:'}
                </Text>
                <Text style={[fonts.light, paddings.VerticalS, paddings.LeftXS, { color: colors.text }]}>
                  {stakeholder.org}
                </Text>
              </View>
            ))
          ) : (
            <Text style={[fonts.regular]}>Opportunity has no Stakeholders</Text>
          )}
        </AnimatedView>
      </CollapsibleView>
    );
  }),
);
