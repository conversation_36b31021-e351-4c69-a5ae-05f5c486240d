import { observable } from 'mobx';
import { observer } from 'mobx-react';
import React, { useRef } from 'react';
import { View } from 'react-native';
import { withTheme } from 'react-native-paper';
import { UserLinks } from '../../../services/codegen/types';
import UserAdminStore from '../../../stores/UserAdminStore';
import { DeleteUser } from './DeleteUser';
import { EditUserBasic } from './EditUserBasic';

interface UserManagementEditUserProps {
  theme: ReactNativePaper.ThemeProp;
  userAdminStore: UserAdminStore;
  onUpdateUser: (userAdminStore: UserAdminStore, links?: UserLinks) => void;
  onDeleteUser?: (userAdminStore: UserAdminStore) => void;
}

export const UserManagementEditUser = withTheme(
  ({ userAdminStore, onUpdateUser, theme, onDeleteUser = () => {} }: UserManagementEditUserProps) => {
    const { styles } = theme;
    return (
      <View
        style={[styles.components.sectionStyle, styles.borders.primary, styles.margins.ML, { alignItems: 'stretch' }]}
      >
        <View style={[styles.paddings.HorizontalL, { alignItems: 'stretch' }]}>
          <EditUserBasic {...{ userAdminStore, onUpdateUser }} />
          <DeleteUser {...{ userAdminStore, onDeleteUser }} />
        </View>
      </View>
    );
  },
);
