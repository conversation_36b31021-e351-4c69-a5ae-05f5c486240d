import { observable } from 'mobx';
import { observer, useLocalObservable } from 'mobx-react';
import React, { useEffect, useRef } from 'react';
import { FlatList, View } from 'react-native';
import { withTheme } from 'react-native-paper';
import { User, UserLinks } from '../../../services/codegen/types';
import UserAdminStore from '../../../stores/UserAdminStore';
import { UsersAdminStore } from '../../../stores/UsersAdminStore';
import { UserManagementHeader } from './UserManagementHeader';
import { UserManagementUser } from './UserManagementUser';
import { UserManagementCreateUser } from './UserManangementCreateUser';
import { stripCountryCode } from '../../../lib/phoneNumberUtils';

interface UserManagementProps {
  usersAdminStore: UsersAdminStore;
  theme: ReactNativePaper.ThemeProp;
}

interface Locals {
  showCreateUser: boolean;
  setShowCreateUser: (value: boolean) => void;
}

export const UserManagement = withTheme(
  observer(({ usersAdminStore }: UserManagementProps) => {
    useEffect(() => {
      usersAdminStore.queryUsers();
    }, [usersAdminStore]);

    const localStore = useLocalObservable(() => ({
      showCreateUser: false,
      setShowCreateUser(value: boolean) {
        this.showCreateUser = value;
      },
    }));

    const { users } = usersAdminStore;
    const header = (
      <UserManagementHeader usersAdminStore={usersAdminStore} onClickNewUser={() => handleToggleCreate(localStore)} />
    );

    return localStore.showCreateUser ? (
      <UserManagementCreateUser
        userAdminStore={new UserAdminStore({} as User)}
        onUserCreated={() => handleUserCreated(localStore, usersAdminStore)}
        onCancel={() => handleToggleCreate(localStore)}
      />
    ) : (
      <FlatList
        style={{ flexGrow: 1 }}
        ListHeaderComponent={header}
        stickyHeaderIndices={[0]}
        data={users}
        renderItem={({ item }) => renderItem({ item, usersAdminStore })}
        keyExtractor={(item) => item.id}
      />
    );
  }),
);

const renderItem = ({ item: user, usersAdminStore }: { item: User; usersAdminStore: UsersAdminStore }) => {
  return (
    <UserManagementUser
      {...{
        userAdminStore: new UserAdminStore(user),
        onUpdateUser: (userAdminStore, links) => handleUpdateUser(userAdminStore, usersAdminStore, links),
        onDeleteUser: (userAdminStore) => handleDeleteUser(userAdminStore, usersAdminStore),
      }}
    />
  );
};

const handleUserCreated = (localStore: Locals, usersAdminStore: UsersAdminStore) => {
  usersAdminStore.queryUsers();
  localStore.setShowCreateUser(false);
};

const handleUpdateUser = async (
  userAdminStore: UserAdminStore,
  usersAdminStore: UsersAdminStore,
  links?: UserLinks,
) => {
  if (links) userAdminStore.pendingUserLinks = links;
  if (!userAdminStore.hasErrors) {
    await userAdminStore.saveUser();
    usersAdminStore.mergeUpdatedUser(userAdminStore.user);
  }
};

const handleDeleteUser = async (userAdminStore: UserAdminStore, usersAdminStore: UsersAdminStore) => {
  const id = userAdminStore.user.id;
  usersAdminStore.deleteUser(id);
};

const handleToggleCreate = (localStore: Locals) => {
  localStore.setShowCreateUser(!localStore.showCreateUser);
};
