import { observer, useLocalObservable } from 'mobx-react';
import { View } from 'react-native';
import { withTheme } from 'react-native-paper';
import { Button, Message, PasswordConfirmation, PasswordConfirmationState } from '../../../lib';
import { validatePassword } from '../../../lib/validatePasswords';

interface EditUserAdvancedProps {
  onUpdate: ({ values }: Record<string, any>) => void;
  theme: ReactNativePaper.ThemeProp;
}

interface Locals {
  editing: boolean;
  password: string;
  isValid: boolean;
  showSuccess: boolean;
  setEditing(editing: boolean): void;
  setPassword(password: string): void;
  setIsValid(isValid: boolean): void;
  setShowSuccess(showSuccess: boolean): void;
}

export const EditUserAdvanced = withTheme(
  observer(({ onUpdate, theme }: EditUserAdvancedProps) => {
    const { fontSizes, styles } = theme;
    const { margins, paddings } = styles;

    const localStore = useLocalObservable(() => ({
      editing: false,
      password: '',
      isValid: false,
      showSuccess: false,
      setEditing(editing: boolean) {
        this.editing = editing;
      },
      setPassword(password: string) {
        this.password = password;
      },
      setIsValid(isValid: boolean) {
        this.isValid = isValid;
      },
      setShowSuccess(showSuccess: boolean) {
        this.showSuccess = showSuccess;
      },
    }));

    return (
      <>
        <View style={{ flexDirection: 'row', alignSelf: 'flex-end' }}>
          {localStore.editing && (
            <>
              <Button type="secondary" style={margins.RightS} compact={true} onPress={() => handleCancel(localStore)}>
                Cancel
              </Button>
              <Button
                type="primary"
                getDisabled={() => !localStore.isValid}
                compact={true}
                onPress={() => handleUpdate(localStore, onUpdate)}
              >
                Update Password
              </Button>
            </>
          )}
          {!localStore.editing && (
            <Button type="secondary" compact={true} onPress={() => handleStartEditing(localStore)}>
              Change Password
            </Button>
          )}
        </View>
        <View style={[paddings.HorizontalXL]}>
          {localStore.showSuccess && <Message style={[fontSizes.mediumLarge]}>Password has been updated!</Message>}
          {!localStore.showSuccess && (
            <PasswordConfirmation
              editable={localStore.editing}
              onPasswordChange={(passwordState, requiredLength) =>
                handlePasswordChange(localStore, passwordState, requiredLength)
              }
            />
          )}
        </View>
      </>
    );
  }),
);

const handleStartEditing = (localStore: Locals) => {
  localStore.setShowSuccess(false);
  localStore.setEditing(!localStore.editing);
};

const handleUpdate = (localStore: Locals, onUpdate: (values: Record<string, any>) => void) => {
  if (localStore.isValid) {
    onUpdate({ password: localStore.password });
    localStore.setShowSuccess(true);
    localStore.setEditing(!localStore.editing);
  }
};

const handleCancel = (localStore: Locals) => {
  localStore.setEditing(!localStore.editing);
};

const handlePasswordChange = (
  localStore: Locals,
  passwordState: PasswordConfirmationState,
  requiredLength: number,
) => {
  const results = validatePassword({
    newPw: passwordState.newPw,
    oldPw: passwordState.oldPw,
    confirmPw: passwordState.confirmPw,
    requiredLength,
    showCurrent: true,
  });

  passwordState.validationResults = results;

  const valid = results.every((r) => r.passed);
  passwordState.setValid(valid ? 'yes' : 'no');
  localStore.setIsValid(valid);
  localStore.setPassword(passwordState.newPw);
};
