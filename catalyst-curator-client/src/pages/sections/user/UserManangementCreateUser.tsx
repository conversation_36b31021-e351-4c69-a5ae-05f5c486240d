import { observer, useLocalObservable } from 'mobx-react';
import { View } from 'react-native';
import { withTheme } from 'react-native-paper';
import { Button, PasswordConfirmationState } from '../../../lib';
import { Title } from '../../../lib';
import { ConfirmationDialog } from '../../../lib';
import { PasswordConfirmation } from '../../../lib';
import { UserLinks } from '../../../services/codegen/types';
import UserAdminStore from '../../../stores/UserAdminStore';
import { EditUserBasic } from './EditUserBasic';
import { validatePassword } from '../../../lib/validatePasswords';
import { stripCountryCode } from '../../../lib/phoneNumberUtils';

interface UserManagementCreateUserProps {
  userAdminStore: UserAdminStore;
  theme: ReactNativePaper.ThemeProp;
  onUserCreated: (userAdminStore: UserAdminStore) => void;
  onCancel: () => void;
}
interface Locals {
  password: string;
  isValidPassword: boolean;
  confirmUserCreatedVisible: boolean;
  setPassword: (value: string) => void;
  setIsValidPassword: (value: boolean) => void;
  setConfirmUserCreatedVisible: (value: boolean) => void;
}

export const UserManagementCreateUser = withTheme(
  observer((props: UserManagementCreateUserProps) => {
    const { userAdminStore, theme, onCancel } = props;
    const { styles, fonts, fontSizes } = theme;

    const localStore = useLocalObservable(() => ({
      password: '',
      isValidPassword: false,
      confirmUserCreatedVisible: false,
      setPassword(value: string) {
        this.password = value;
      },
      setIsValidPassword(value: boolean) {
        this.isValidPassword = value;
      },
      setConfirmUserCreatedVisible(value: boolean) {
        this.confirmUserCreatedVisible = value;
      },
    }));

    return (
      <View>
        <View
          style={[styles.components.sectionStyle, styles.borders.primary, styles.margins.ML, { alignItems: 'stretch' }]}
        >
          <View style={[styles.paddings.HorizontalL, { alignItems: 'stretch' }]}>
            <Title style={[fonts.mediumTitle, fontSizes.medium, styles.margins.BottomM]}>New User</Title>
            <EditUserBasic
              {...{
                theme,
                userAdminStore,
                includeUserId: true,
                onUpdateUser: (userAdminStore, links) => handleUpdateUser(userAdminStore, links),
              }}
            />
            <PasswordConfirmation
              editable={true}
              onPasswordChange={(passwordState, requiredLength) =>
                handlePasswordChange(localStore, passwordState, requiredLength)
              }
              showCurrent={false}
            />
            <View style={{ flexDirection: 'row', justifyContent: 'flex-end', marginTop: 16 }}>
              <Button type="secondary" style={styles.margins.RightS} compact={true} onPress={onCancel}>
                Cancel
              </Button>
              <Button
                type="primary"
                getDisabled={() => !localStore.isValidPassword || userAdminStore.hasErrors}
                compact={true}
                onPress={() => handleCreateUser(localStore, props)}
              >
                Create User
              </Button>
            </View>
          </View>
        </View>
        <ConfirmationDialog
          title="User Created"
          message={`New user ${userAdminStore.user.firstName} ${userAdminStore.user.lastName} has been created successfully!`}
          getVisible={() => localStore.confirmUserCreatedVisible}
          onConfirm={() => handleUserCreated(localStore, props)}
          onCancel={() => localStore.setConfirmUserCreatedVisible(false)}
        />
      </View>
    );
  }),
);

const handleUserCreated = (localsStore: Locals, props: UserManagementCreateUserProps) => {
  localsStore.setConfirmUserCreatedVisible(false);
  props.onUserCreated(props.userAdminStore);
};

const handleCreateUser = async (localsStore: Locals, props: UserManagementCreateUserProps) => {
  const { onUserCreated: onCreateUser, userAdminStore } = props;
  const phoneNumber = userAdminStore.getValue('phone');
  const phoneNumberNoCode = stripCountryCode(phoneNumber);
  if (phoneNumber && phoneNumberNoCode === '') {
    userAdminStore.setValue('phone', null);
  }
  if (localsStore.isValidPassword && userAdminStore.validateAll()) {
    userAdminStore.setValue('password', localsStore.password);
    await userAdminStore.createUser();
    localsStore.setConfirmUserCreatedVisible(true);
  }
};

const handleUpdateUser = async (userAdminStore: UserAdminStore, links?: UserLinks) => {
  if (links) userAdminStore.pendingUserLinks = links;
};

const handlePasswordChange = (localStore: Locals, passwordState: PasswordConfirmationState, requiredLength: number) => {
  const results = validatePassword({
    newPw: passwordState.newPw,
    oldPw: passwordState.oldPw,
    confirmPw: passwordState.confirmPw,
    requiredLength,
    showCurrent: false,
  });

  passwordState.validationResults = results;

  const valid = results.every((r) => r.passed);
  passwordState.setValid(valid ? 'yes' : 'no');
  localStore.setIsValidPassword(valid);
  localStore.setPassword(passwordState.newPw);
};
