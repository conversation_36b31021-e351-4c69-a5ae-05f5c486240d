import { observer, useLocalObservable } from 'mobx-react';
import { Pressable, Text, View } from 'react-native';
import { withTheme } from 'react-native-paper';
import { RolesInfo } from '../../../lib';
import { Button } from '../../../lib';
import { SearchInput } from '../../../lib';
import { Title } from '../../../lib';
import { FilterRoleMenu } from '../../../lib';
import { UsersAdminStore } from '../../../stores/UsersAdminStore';

interface UserManagementHeaderProps {
  usersAdminStore: UsersAdminStore;
  theme: ReactNativePaper.ThemeProp;
  onClickNewUser?: () => void;
}

interface Locals {
  roleValue: RolesInfo | 'Any';
  setRoleValue: (value: RolesInfo | 'Any') => void;
}

export const UserManagementHeader = withTheme(
  observer(({ usersAdminStore, theme, onClickNewUser }: UserManagementHeaderProps) => {
    const {
      styles: { paddings, margins, borders, defaultValues: defaults },
      colors,
      fonts,
      fontSizes,
    } = theme;
    const { users } = usersAdminStore;

    const localStore = useLocalObservable<Locals>(() => ({
      roleValue: 'Any',
      setRoleValue(value: RolesInfo | 'Any') {
        this.roleValue = value;
      },
    }));

    return (
      <View
        style={[
          borders.component,
          paddings.HorizontalL,
          paddings.TopML,
          {
            borderRightWidth: 0,
            borderLeftWidth: 0,
            borderTopWidth: 0,
            borderRadius: 0,
            borderTopRightRadius: defaults.primaryBorderRadius,
            backgroundColor: colors.surface,
          },
        ]}
      >
        <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
          <Title style={[fonts.mediumTitle, fontSizes.medium]}>Users</Title>
          <Button
            type={'secondary'}
            compact={true}
            style={[margins.LeftM]}
            onPress={() => handleClickNewUser(onClickNewUser)}
          >
            Create User
          </Button>
        </View>
        <View
          style={[{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'flex-end' }, margins.BottomML]}
        >
          <Text style={[{ flex: 1 }, fonts.medium, paddings.BottomM]}>{`${users.length || 0} Profiles Shown`}</Text>
          <FilterRoleMenu
            style={[{ flex: 2 }, margins.HorizontalML]}
            value={localStore.roleValue}
            labelText="Display Role:"
            onValueSelected={(menuItem) => handleChangeRole(localStore, usersAdminStore, menuItem.value)}
            dropdownMenuProps={{ getEditable: () => true }}
          />
          <SearchInput
            style={{ flex: 2 }}
            getValue={() => usersAdminStore.keywordSearchValue}
            setValue={(value) => {
              usersAdminStore.keywordSearchValue = value;
              if (!value) usersAdminStore.queryUsers();
            }}
            onSubmitEditing={() => usersAdminStore.queryUsers()}
            onIconPress={() => usersAdminStore.queryUsers()}
          />
        </View>
      </View>
    );
  }),
);

const handleChangeRole = (localStore: Locals, usersAdminStore: UsersAdminStore, value: RolesInfo) => {
  localStore.setRoleValue(value);
  usersAdminStore.roleFilter = value;
};

const handleClickNewUser = (onClickNewUser = () => {}) => {
  onClickNewUser && onClickNewUser();
};
