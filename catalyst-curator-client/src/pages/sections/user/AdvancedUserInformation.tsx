import { observer } from 'mobx-react-lite';
import React, { useRef } from 'react';
import { View } from 'react-native';
import { withTheme } from 'react-native-paper';
import { ConfirmationDialog } from '../../../lib';
import { ClickableText } from '../../../lib/ui/atoms/ClickableText';
import { UserStore } from '../../../stores';
import { EditUserAdvanced } from './EditUserAdvanced';
import { useLocalObservable } from 'mobx-react';

interface AdvancedUserInformationProps {
  userStore: UserStore;
  theme: ReactNativePaper.ThemeProp;
}

interface Locals {
  confirmDeleteVisible: boolean;
  setConfirmDeleteVisible(visible: boolean): void;
}

export const AdvancedUserInformation = withTheme(
  observer(({ userStore, theme }: AdvancedUserInformationProps) => {
    const { colors, styles, fontSizes } = theme;
    const { paddings, margins } = styles;
    const localStore = useLocalObservable(() => ({
      confirmDeleteVisible: false,
      setConfirmDeleteVisible(visible: boolean) {
        this.confirmDeleteVisible = visible;
      },
    }));

    return (
      <View style={{ flex: 1, flexDirection: 'column', justifyContent: 'space-between' }}>
        <View style={[styles.components.sectionStyle, { alignItems: 'stretch' }]}>
          <EditUserAdvanced onUpdate={(values) => handleUpdate(userStore, values)} />
        </View>
        <View style={[margins.BottomL, { alignSelf: 'center' }]}>
          <ClickableText
            style={[fontSizes.small, { color: colors.error }]}
            onPress={() => localStore.setConfirmDeleteVisible(true)}
          >
            DELETE ACCOUNT
          </ClickableText>
        </View>
        <ConfirmationDialog
          title="Delete Current User Account"
          titleStyle={[fontSizes.medium]}
          message={`Are you sure you want to delete your current user account?\nThis cannot be undone!`}
          getVisible={() => localStore.confirmDeleteVisible}
          onConfirm={() => handleConfirmDeleteAccount(localStore, userStore)}
          onCancel={() => handleCancelDeleteAccount(localStore)}
        />
      </View>
    );
  }),
);

const handleConfirmDeleteAccount = (localStore: Locals, userStore: UserStore) => {
  localStore.setConfirmDeleteVisible(false);
  userStore.deleteCurrentUser();
};

const handleCancelDeleteAccount = (localStore: Locals) => {
  localStore.setConfirmDeleteVisible(false);
};

const handleUpdate = async (userStore: UserStore, values: Record<string, any>) => {
  try {
    userStore.setValues(values);
    await userStore.saveCurrentUser();
  } catch (e) {
    console.error(e);
  }
};
