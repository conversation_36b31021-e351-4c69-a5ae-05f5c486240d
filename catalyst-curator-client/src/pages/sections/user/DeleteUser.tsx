import { observable } from 'mobx';
import { observer, useLocalStore } from 'mobx-react';
import React, { useRef } from 'react';
import { View } from 'react-native';
import { withTheme } from 'react-native-paper';
import { Button, ConfirmationDialog } from '../../../lib';
import { ClickableText } from '../../../lib/ui/atoms/ClickableText';
import UserAdminStore from '../../../stores/UserAdminStore';

interface DeleteUserProps {
  theme: ReactNativePaper.ThemeProp;
  userAdminStore: UserAdminStore;
  onDeleteUser?: (userAdminStore: UserAdminStore) => void;
}

interface Locals {
  confirmDeleteVisible: boolean;
  setConfirmDeleteVisible(value: boolean): void;
}

export const DeleteUser = withTheme(
  observer(({ userAdminStore, theme, onDeleteUser = () => {} }: DeleteUserProps) => {
    const { styles, colors, fontSizes } = theme;
    const { margins, paddings } = styles;
    const localStore = useLocalStore(() => ({
      confirmDeleteVisible: false,
      setConfirmDeleteVisible(value: boolean) {
        this.confirmDeleteVisible = value;
      },
    }));

    return (
      <>
        <View style={[margins.TopL, { alignSelf: 'center' }]}>
          <ClickableText
            style={[fontSizes.small, { color: colors.error }]}
            onPress={() => localStore.setConfirmDeleteVisible(true)}
          >
            DELETE ACCOUNT
          </ClickableText>
        </View>
        <ConfirmationDialog
          title={`Delete user account`}
          titleStyle={[fontSizes.medium]}
          message={`Are you sure you want to delete the user account for ${getValue(
            userAdminStore,
            'firstName',
          )} ${getValue(userAdminStore, 'lastName')}?\nThis cannot be undone!`}
          getVisible={() => localStore.confirmDeleteVisible}
          onConfirm={() => handleConfirmDeleteAccount(localStore, userAdminStore, onDeleteUser)}
          onCancel={() => handleCancelDeleteAccount(localStore)}
        />
      </>
    );
  }),
);

const handleConfirmDeleteAccount = async (
  localStore: Locals,
  userAdminStore: UserAdminStore,
  onDeleteUser: (userAdminStore: UserAdminStore) => void,
) => {
  localStore.setConfirmDeleteVisible(false);
  onDeleteUser(userAdminStore);
};

const handleCancelDeleteAccount = (localStore: Locals) => {
  localStore.setConfirmDeleteVisible(false);
};

const getValue = (userAdminStore: UserAdminStore, name: string) => {
  return userAdminStore.getValue(name, '');
};
