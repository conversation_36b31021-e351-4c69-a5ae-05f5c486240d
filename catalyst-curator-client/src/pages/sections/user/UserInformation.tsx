import { observable, runInAction, set } from 'mobx';
import { observer, useLocalObservable } from 'mobx-react';
import React, { useRef } from 'react';
import { View } from 'react-native';
import { withTheme } from 'react-native-paper';
import { Button } from '../../../lib';
import { HelperTextProps } from '../../../lib';
import { Hr } from '../../../lib';
import { LabeledPhoneInput } from '../../../lib';
import { LabeledTextInput } from '../../../lib';
import { DependentDropdown } from '../../../lib';
import { CuratorStores } from '../../../platform/initializers';
import { StoresProvider } from '../../../lib/stores/StoresProvider';
import { UserStore } from '../../../stores';
import {
  getCountryCodeItem,
  getCountryCodeItems,
  onSelectCountryCode,
  stripCountryCode,
} from '../../../lib/phoneNumberUtils';

interface UserInformationProps {
  userStore: UserStore;
  theme: ReactNativePaper.ThemeProp;
}

interface Locals {
  editing: boolean;
  setEditing: (value: boolean) => void;
}

export const UserInformation = withTheme(
  observer(({ userStore, theme }: UserInformationProps) => {
    const { colors, fonts, styles } = theme;
    const { components, borders, margins, paddings } = styles;

    const localStore = useLocalObservable(() => ({
      editing: false,
      setEditing(value: boolean) {
        this.editing = value;
      },
    }));

    const { editing } = localStore;

    const organizationStore = StoresProvider.get<CuratorStores>().getStore('OrganizationStore');

    return (
      <>
        <View style={[styles.components.sectionStyle, { alignItems: 'stretch' }]}>
          <View style={{ flexDirection: 'row', alignSelf: 'flex-end' }}>
            <Button
              iconName={editing ? 'check' : 'pencil'}
              type={editing ? 'primary' : 'secondary'}
              onPress={() => onClickEdit(localStore, userStore)}
            >
              {editing ? 'Finish Editing' : 'Edit Information'}
            </Button>
          </View>
          <View style={[paddings.HorizontalXL]}>
            <View style={{ flexDirection: 'row' }}>
              <LabeledTextInput
                textInputProps={{
                  editable: editing,
                  multiline: false,
                  spellcheck: false,
                  onDebounceValue: (value) => handleValueChange(userStore),
                  getValue: () => getValue(userStore, 'firstName'),
                  setValue: (value) => setValue(userStore, 'firstName', value),
                }}
                labelText={'First name'}
                getHelperTextProps={() => getHelperTextProps('firstName', userStore)}
              />
            </View>
            <Hr style={[{ alignSelf: 'stretch' }, margins.VerticalML]} />
            <View style={{ flexDirection: 'row' }}>
              <LabeledTextInput
                textInputProps={{
                  editable: editing,
                  multiline: false,
                  spellcheck: false,
                  onDebounceValue: (value) => handleValueChange(userStore),
                  getValue: () => getValue(userStore, 'lastName'),
                  setValue: (value) => setValue(userStore, 'lastName', value),
                }}
                labelText={'Last name'}
                getHelperTextProps={() => getHelperTextProps('lastName', userStore)}
              />
            </View>
            {organizationStore.organizations.length > 0 &&
              (() => {
                const orgValue1 = getValue(userStore, 'org1');
                const orgValue2 = getValue(userStore, 'org2');
                const orgValue3 = getValue(userStore, 'org3');
                const orgValue4 = getValue(userStore, 'org4');
                return (
                  <>
                    <View style={[{ flexDirection: 'column', alignItems: 'stretch' }, margins.BottomL]}>
                      <Hr style={[{ alignSelf: 'stretch' }, margins.VerticalML]} />
                      <DependentDropdown
                        defaultValues={[
                          {
                            label: 'Team',
                            value: orgValue1 && { label: orgValue1, value: orgValue1 },
                            fieldName: 'org1',
                          },
                          {
                            label: undefined,
                            value: orgValue2 && { label: orgValue2, value: orgValue2 },
                            fieldName: 'org2',
                          },
                          {
                            label: undefined,
                            value: orgValue3 && { label: orgValue3, value: orgValue3 },
                            fieldName: 'org3',
                          },
                          {
                            label: undefined,
                            value: orgValue4 && { label: orgValue4, value: orgValue4 },
                            fieldName: 'org4',
                          },
                        ]}
                        labeledDropdownMenuProps={{
                          dropdownMenuProps: { getEditable: () => editing },
                          getHelperTextProps: () => getHelperTextProps('org1', userStore),
                        }}
                        getMenuGroups={() => organizationStore.orgsToMenuGroups()}
                        onItemSelected={(item, fieldName) => {
                          setValue(userStore, fieldName, item?.label);
                          handleValueChange(userStore);
                        }}
                      />
                    </View>
                  </>
                );
              })()}
            <Hr style={[{ alignSelf: 'stretch' }, margins.VerticalML]} />
            <View style={{ flexDirection: 'row' }}>
              <LabeledPhoneInput
                countryCodeMenuProps={{
                  getMenuItems: () => getCountryCodeItems(),
                  getValue: () => getCountryCodeItem(userStore.getValue('phone')),
                  onItemSelected: (item) => {
                    onSelectCountryCode(item, userStore.setValue.bind(userStore), userStore.getValue('phone'));
                    const phoneNumber = userStore.getValue('phone');
                    const phoneNumberNoCode = stripCountryCode(phoneNumber);
                    if (phoneNumberNoCode !== '') {
                      handleValueChange(userStore);
                    }
                  },
                  getEditable: () => editing,
                  anchorStyle: { height: 41, maxHeight: 41 },
                }}
                textInputProps={{
                  editable: editing,
                  multiline: false,
                  spellcheck: false,
                  onDebounceValue: (value) => handleValueChange(userStore),
                  getValue: () => getValue(userStore, 'phone'),
                  setValue: (value) => setValue(userStore, 'phone', value),
                }}
                labelText={'Phone'}
                getHelperTextProps={() => getHelperTextProps('phone', userStore)}
              />
            </View>
          </View>
        </View>
      </>
    );
  }),
);

const getHelperTextProps = (name: string, userStore: UserStore): HelperTextProps => {
  return { type: 'error', children: userStore.getPropertyError(name) };
};

const handleValueChange = (userStore: UserStore) => {
  userStore.saveCurrentUser();
};

const getValue = (userStore: UserStore, name: string) => {
  return userStore.getValue(name, '');
};

const setValue = (userStore: UserStore, name: string, value: any) => {
  if (userStore.user) userStore.setValue(name, value);
};

const onClickEdit = (localStore: Locals, userStore: UserStore) => {
  runInAction(() => {
    localStore.setEditing(!localStore.editing);
    if (!localStore.editing) userStore.clearBuffer();
  });
};
