import { observable, runInAction, set } from 'mobx';
import { observer, useLocalObservable } from 'mobx-react';
import React, { useRef } from 'react';
import { StyleProp, TextStyle, View } from 'react-native';
import { withTheme } from 'react-native-paper';
import { RolesInfo } from '../../../lib';
import { Button } from '../../../lib';
import { Label } from '../../../lib';
import { UserProfileBadge } from '../../../lib';
import { AnimatedView } from '../../../lib/ui/atoms/AnimatedView';
import { RoleNames, UserLinks, VerifiedStatus } from '../../../services/codegen/types';
import UserAdminStore from '../../../stores/UserAdminStore';
import { UserManagementEditUser } from './UserManagementEditUser';

interface UserManagementProps {
  userAdminStore: UserAdminStore;
  onUpdateUser: (userAdminStore: UserAdminStore, links?: UserLinks) => void;
  onDeleteUser: (userAdminStore: UserAdminStore) => void;
  theme: ReactNativePaper.ThemeProp;
}

interface Locals {
  editing: boolean;
  setEditing: (value: boolean) => void;
}

export const UserManagementUser = withTheme(
  observer(({ userAdminStore, onUpdateUser, onDeleteUser, theme }: UserManagementProps) => {
    const { styles, colors, fontSizes } = theme;
    const { components, margins, paddings } = styles;

    const localStore = useLocalObservable(() => ({
      editing: false,
      setEditing(value: boolean) {
        this.editing = value;
      },
    }));

    const { editing } = localStore;
    const { user, rolesInfo } = userAdminStore;

    // @TODO
    // this needs to be moved out to configuration per tenant
    const registeredStatus = user.status === VerifiedStatus.Unverified ? 'Unverified' : undefined;
    return (
      <View>
        <View
          style={[
            paddings.HorizontalML,
            paddings.VerticalM,
            { flexDirection: 'row', flexGrow: 1, justifyContent: 'space-between', alignItems: 'center' },
          ]}
        >
          <UserProfileBadge {...{ firstName: user.firstName, lastName: user.lastName }} style={{ flex: 1 }} />
          <View style={{ flexDirection: 'row', justifyContent: 'center', flex: 1, alignItems: 'flex-end' }}>
            <Label textStyle={getRoleLabelStyle(theme, rolesInfo)}>{rolesInfo.displayName}</Label>
            {registeredStatus && (
              <Label textStyle={[fontSizes.xSmall, margins.LeftS]}>{` (${registeredStatus})`}</Label>
            )}
          </View>
          <View style={{ flex: 1, alignItems: 'flex-end' }}>
            <Button
              iconName={localStore.editing ? 'check' : 'pencil'}
              type={localStore.editing ? 'primary' : 'secondary'}
              onPress={() => onClickEdit(localStore, userAdminStore)}
            >
              {editing ? 'Finish Editing' : 'Edit Information'}
            </Button>
          </View>
        </View>
        {editing && (
          <AnimatedView>
            <UserManagementEditUser
              userAdminStore={userAdminStore}
              onUpdateUser={onUpdateUser}
              onDeleteUser={onDeleteUser}
            />
          </AnimatedView>
        )}
      </View>
    );
  }),
);

const getRoleLabelStyle = (theme: ReactNativePaper.ThemeProp, rolesInfo: RolesInfo): StyleProp<TextStyle> => {
  const style: StyleProp<TextStyle> = theme.fontSizes.mediumSmall;
  if (rolesInfo.effectiveRoleName === RoleNames.Admin) {
    return [style, { color: theme.colors.error }];
  } else if (rolesInfo.effectiveRoleName === RoleNames.Curator) {
    return [style, { color: theme.colors.info }];
  }
  return style;
};

const onClickEdit = (localStore: Locals, userAdminStore: UserAdminStore) => {
  runInAction(() => {
    localStore.setEditing(!localStore.editing)
    if (!localStore.editing) userAdminStore.clearBuffer();
  });
};
