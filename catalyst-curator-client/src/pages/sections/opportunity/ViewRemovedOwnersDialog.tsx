import { withTheme } from 'react-native-paper';
import { ContentDialog } from '../../../lib/ui/molecules/ContentDialog';
import { DialogProps, Title } from '../../../lib';
import { ScrollView, View } from 'react-native';
import { Text } from '../../../lib/ui/atoms/Text';
import { observer } from 'mobx-react';
import { OpportunityStore } from '../../../stores';
import { OpportunityOwnerCard } from './OpportunityOwnerCard';

import { OpportunityOwnerListStore } from '../../../stores/OpportunityOwnerListStore';

interface ViewRemovedOwnersDialogProps extends DialogProps {
  onConfirm: () => void;
  theme: ReactNativePaper.ThemeProp;
  opportunityStore: OpportunityStore;
  opportunityOwnerListStore: OpportunityOwnerListStore;
}
export const ViewRemovedOwnersDialog = withTheme(
  observer(
    ({
      theme,
      onConfirm,
      getVisible,
      opportunityStore,
      opportunityOwnerListStore,
      onDismiss,
    }: ViewRemovedOwnersDialogProps) => {
      const {
        styles: { components },
      } = theme;

      const removedOwners = opportunityOwnerListStore.removedOwners;

      return (
        <ContentDialog
          {...{ onConfirm, onDismiss, theme }}
          noTitleBar={true}
          style={{ borderRadius: 16 }}
          contentStyle={[{ borderRadius: 16, width: 840, maxHeight: 800, backgroundColor: theme.colors.background }]}
          getVisible={getVisible}
          showClose={true}
          onDismiss={onDismiss}
        >
          <Title style={[{ alignSelf: 'flex-start' }]}>Removed Owners</Title>
          <Text style={[components.messageInfoStyle, { marginTop: 16 }]}>
            These Opportunity Owners have been manually removed from the Owner History of this Opportunity. You can
            expand any user card to restore them back to the Owner History.
          </Text>
          <ScrollView
            contentContainerStyle={[{ marginTop: 32, flex: 1, gap: 16 }]}
            style={{ flex: 1, width: '100%' }}
            keyboardShouldPersistTaps="handled"
            contentInsetAdjustmentBehavior="always"
          >
            {removedOwners.map((owner) => {
              return (
                <OpportunityOwnerCard
                  opportunityOwner={owner}
                  opportunityOwnerListStore={opportunityOwnerListStore}
                  opportunityStore={opportunityStore}
                />
              );
            })}

            {removedOwners.length === 0 && (
              <View style={{ alignItems: 'center', justifyContent: 'center' }}>
                <Text>No Opporunity Owners have been removed.</Text>
              </View>
            )}
          </ScrollView>
        </ContentDialog>
      );
    },
  ),
);
