import { observer } from 'mobx-react';
import React from 'react';
import { withTheme } from 'react-native-paper';
import { Text } from '../../../lib/ui/atoms/Text';
import { LinkedItem } from '../../../lib/ui/molecules/LinkedItem';
import { OpportunityStore } from '../../../stores';

interface ProjectPrimersProps {
  opportunityStore: OpportunityStore;
  onPressProject: (id: string) => void;
  theme: ReactNativePaper.ThemeProp;
}

export const ProjectPrimers = withTheme(
  observer(({ opportunityStore, onPressProject, theme }: ProjectPrimersProps) => {
    const {
      styles: { margins },
    } = theme;
    return (
      <>
        {opportunityStore.opportunity?.projects.map((project) => (
          <LinkedItem
            getLinkText={() => project.title}
            style={[margins.M]}
            onPress={() => onPressProject(project.id)}
            key={project.id}
          />
        ))}
        {!opportunityStore.opportunity?.projects?.length && <Text>No Project Primers</Text>}
      </>
    );
  }),
);
