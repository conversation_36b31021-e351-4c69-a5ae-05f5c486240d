import { Linking, View } from 'react-native';
import { Button, Label, TextInput, urlValidator } from '../../../lib';
import { withTheme } from 'react-native-paper';
import { observer, useLocalObservable } from 'mobx-react';
import { OpportunityStore, UserStore } from '../../../stores';
import { InfoCard } from '../../../lib/ui/molecules/InfoCard';
import { LinkDialog } from './LinkDialog';

export type LinkLocals = {
  url: string;
  name: string;
  isDialogOpen: boolean;
  notes: string;
  editingLinkId: string | null;
  setUrl(url: string): void;
  setName(name: string): void;
  setNotes(notes: string): void;
  setIsDialogOpen(value: boolean): void;
  setEditingLinkId(id: string | null): void;
  urlErrorMessage: string;
  setUrlErrorMessage(value: string): void;
};

type LinksProps = {
  theme: ReactNativePaper.ThemeProp;
  opportunityStore: OpportunityStore;
  editable: boolean;
  userStore: UserStore;
};
export const Links = withTheme(
  observer(({ theme, opportunityStore, editable, userStore }: LinksProps) => {
    const { colors } = theme;

    const locals = useLocalObservable<LinkLocals>(() => ({
      url: '',
      name: '',
      notes: '',
      isDialogOpen: false,
      editingLinkId: null,
      setIsDialogOpen(value: boolean) {
        this.isDialogOpen = value;
      },
      setEditingLinkId(id: string | null) {
        this.editingLinkId = id;
      },
      setUrl(url: string) {
        this.url = url;
      },
      setName(name: string) {
        this.name = name;
      },
      setNotes(notes: string) {
        this.notes = notes;
      },
      urlErrorMessage: '',
      setUrlErrorMessage(value: string) {
        this.urlErrorMessage = value;
      },
    }));

    return (
      <>
        <View style={{ marginBottom: 32 }}>
          <Label textStyle={{ color: colors.disclaimerSecondary }}>Destination URL</Label>
          {editable ? (
            <View style={{ flexDirection: 'row', gap: 4 }}>
              <TextInput
                getValue={() => locals.url}
                setValue={(value) => locals.setUrl(value)}
                containerStyle={{ backgroundColor: colors.background, flex: 1 }}
                placeholder="https://example.com"
                iconName="link"
                multiline={false}
              />
              <Button
                type="primary"
                onPress={() => locals.setIsDialogOpen(true)}
                labelStyle={{ textTransform: 'capitalize' }}
                getDisabled={() => !locals.url}
              >
                Add Link
              </Button>
            </View>
          ) : null}
          <View style={{ gap: 8, marginTop: 16 }}>
            {opportunityStore.opportunity?.links?.map((link) => {
              return (
                <InfoCard
                  key={link.id}
                  id={link.id}
                  title={link.name || link.url}
                  subtitle={link.name ? link.url : undefined}
                  iconName="link"
                  iconColor={colors.secondary}
                  notes={link.notes || undefined}
                  onPress={() => {
                    Linking.openURL(link.url);
                  }}
                  onEdit={
                    editable
                      ? () => {
                          locals.setUrl(link.url);
                          locals.setName(link.name);
                          locals.setNotes(link.notes || '');
                          locals.setEditingLinkId(link.id);
                          locals.setIsDialogOpen(true);
                        }
                      : undefined
                  }
                  theme={theme}
                  createdBy={link.createdBy || undefined}
                  createdAt={link.createdAt}
                />
              );
            })}
          </View>
        </View>
        <LinkDialog
          getVisible={() => locals.isDialogOpen}
          dismissable={true}
          onConfirm={async () => {
            if (!locals.url) {
              locals.setUrlErrorMessage('URL is required');
              return;
            }
            if (!urlValidator(locals.url)) {
              locals.setUrlErrorMessage('Invalid URL');
              return;
            }

            if (locals.editingLinkId) {
              await opportunityStore.updateLink(locals.editingLinkId, locals.url, locals.name, locals.notes);
            } else {
              await opportunityStore.addLink(locals.url, locals.name, locals.notes);
            }
            locals.setIsDialogOpen(false);
            locals.setEditingLinkId(null);
            locals.setUrl('');
            locals.setName('');
            locals.setNotes('');
            locals.setUrlErrorMessage('');
          }}
          onDelete={async () => {
            if (locals.editingLinkId) {
              await opportunityStore.deleteLink(locals.editingLinkId);
              locals.setIsDialogOpen(false);
              locals.setEditingLinkId(null);
              locals.setUrl('');
              locals.setName('');
              locals.setNotes('');
              locals.setUrlErrorMessage('');
            }
          }}
          onDismiss={() => {
            locals.setIsDialogOpen(false);
            locals.setEditingLinkId(null);
            locals.setUrl('');
            locals.setName('');
            locals.setNotes('');
            locals.setUrlErrorMessage('');
          }}
          locals={locals}
          userStore={userStore}
        />
      </>
    );
  }),
);
