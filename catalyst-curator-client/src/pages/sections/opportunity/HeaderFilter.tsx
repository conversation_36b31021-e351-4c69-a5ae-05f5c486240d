import { withTheme } from 'react-native-paper';
import { PopupMenu } from '../../../lib/ui/atoms/PopupMenu';
import { observer, useLocalObservable } from 'mobx-react';
import { Icon } from '../../../lib/ui/atoms/Icon';
import { Checkbox } from '../../../lib/ui/atoms/Checkbox';
import { Button, Hr, SearchInput } from '../../../lib';
import { Store } from '../../../appComponents/GenericGrid';
import { SearchField, SearchOperator } from '../../../services/codegen/types';
import { FieldLabelValue, FilterInfoStore, GroupLabelValue } from '../../../stores/FilterInfoStore';
import { View } from 'react-native';

interface Locals {
  visible: boolean;
  setVisible(visible: boolean): void;
}
interface HeaderFilterProps<T> {
  filterInfoStore: FilterInfoStore;
  store: Store<T>;
  theme: ReactNativePaper.ThemeProp;
}
export const HeaderFilter = withTheme(
  observer(<T,>({ filterInfoStore, theme, store }: HeaderFilterProps<T>) => {
    const localStore = useLocalObservable<Locals>(() => ({
      visible: false,
      setVisible(visible: boolean) {
        this.visible = visible;
      },
    }));

    const {
      styles: { margins, paddings },
      colors,
    } = theme;

    function getFilterIconColor() {
      const isAllSelected = getIsAllSelected(store, filterInfoStore);
      if (isAllSelected === true || isAllSelected === 'indeterminate') {
        return colors.buttonPrimary;
      } else return undefined;
    }

    return (
      <PopupMenu
        anchor={<Icon name="filter-variant" size={20} color={getFilterIconColor()} />}
        visible={localStore.visible}
        onDismiss={() => localStore.setVisible(false)}
        onClickAnchor={() => localStore.setVisible(true)}
        contentStyle={[{ minWidth: 175 }, paddings.RightM]}
      >
        {filterInfoStore.useSearchBar && (
          <SearchInput
            getValue={() => filterInfoStore.searchTerm}
            setValue={(v) => {
              filterInfoStore.searchTerm = v;
            }}
            style={[margins.BottomM]}
          />
        )}
        <Checkbox
          label={'(Select All)'}
          getChecked={() => getIsAllSelected(store, filterInfoStore)}
          onChecked={() => handleCheckAll(store, filterInfoStore)}
          onUnchecked={() => handleUncheckAll(store, filterInfoStore)}
        />
        <Hr style={[{ alignSelf: 'stretch' }, margins.VerticalS, margins.RightM]} />
        <View style={{ gap: 8 }}>
          {filterInfoStore.fieldOrGroup === 'FIELD'
            ? filterInfoStore.filteredInfo.map((checkbox) => {
                const fieldCheckbox = checkbox as FieldLabelValue;
                if (fieldCheckbox.label === null) return null;
                return (
                  <Checkbox
                    key={fieldCheckbox.label}
                    label={fieldCheckbox.label}
                    getChecked={() => {
                      const searchFieldsArray = store.searchFields.getSearchFieldsFor(filterInfoStore.groupName) || [];
                      return filterInfoStore.getSelectedFields(searchFieldsArray, fieldCheckbox.value);
                    }}
                    onChecked={() => handleCheckedFields(store, filterInfoStore.groupName, fieldCheckbox.value)}
                    onUnchecked={() => handleUncheckedFields(store, filterInfoStore.groupName, fieldCheckbox.value)}
                  />
                );
              })
            : filterInfoStore.filteredInfo.map((checkbox) => {
                const groupCheckbox = checkbox as GroupLabelValue;
                if (groupCheckbox.label === null) return null;
                return (
                  <Checkbox
                    key={groupCheckbox.label}
                    label={groupCheckbox.label}
                    getChecked={() => {
                      const searchGroup = store.searchGroups.getSearchGroupFor(filterInfoStore.groupName);
                      if (!searchGroup) return false;
                      return filterInfoStore.getSelectedGroups(searchGroup, groupCheckbox.searchField);
                    }}
                    onChecked={() => handleCheckedGroups(store, filterInfoStore.groupName, groupCheckbox.searchField)}
                    onUnchecked={() =>
                      handleUncheckedGroups(store, filterInfoStore.groupName, groupCheckbox.searchField)
                    }
                  />
                );
              })}
        </View>
        <View style={[margins.TopM, { flexDirection: 'row', justifyContent: 'center' }]}>
          <Button
            type={'primary'}
            compact={true}
            onPress={() => applyFilters(store, localStore)}
            style={{ width: '100%' }}
          >
            {'Apply'}
          </Button>
        </View>
      </PopupMenu>
    );
  }),
);

const getIsAllSelected = <T,>(store: Store<T>, filterInfoStore: FilterInfoStore) => {
  if (filterInfoStore.fieldOrGroup === 'FIELD') {
    const searchFieldsArray = store.searchFields.getSearchFieldsFor(filterInfoStore.groupName) || [];
    const isAllChecked = filterInfoStore.info?.every((checkbox) => {
      const fieldCheckbox = checkbox as FieldLabelValue;
      return filterInfoStore.getSelectedFields(searchFieldsArray, fieldCheckbox.value);
    });
    const isAllUnchecked = filterInfoStore.info?.every((checkbox) => {
      const fieldCheckbox = checkbox as FieldLabelValue;
      return !filterInfoStore.getSelectedFields(searchFieldsArray, fieldCheckbox.value);
    });
    if (isAllChecked) return true;
    if (isAllUnchecked) return false;
    return 'indeterminate';
  }
  if (filterInfoStore.fieldOrGroup === 'GROUP') {
    const searchGroup = store.searchGroups.getSearchGroupFor(filterInfoStore.groupName);
    if (!searchGroup) return false;
    const isAllChecked = filterInfoStore.info?.every((checkbox) => {
      const groupCheckbox = checkbox as GroupLabelValue;
      return filterInfoStore.getSelectedGroups(searchGroup, groupCheckbox.searchField);
    });
    const isAllUnchecked = filterInfoStore.info?.every((checkbox) => {
      const groupCheckbox = checkbox as GroupLabelValue;
      return !filterInfoStore.getSelectedGroups(searchGroup, groupCheckbox.searchField);
    });
    if (isAllChecked) return true;
    if (isAllUnchecked) return false;
    return 'indeterminate';
  }
  return 'indeterminate';
};

const handleCheckedGroups = <T,>(store: Store<T>, groupName: string, searchField: SearchField) => {
  const searchGroup = store.searchGroups.getSearchGroupFor(groupName);
  if (!searchGroup) {
    store.searchGroups.newSearchGroup(groupName, 'or', searchField);
  } else {
    store.searchGroups.addToSearchGroup(groupName, searchField);
  }
};

const handleUncheckedGroups = <T,>(store: Store<T>, groupName: string, searchField: SearchField) => {
  const searchGroup = store.searchGroups.getSearchGroupFor(groupName);
  if (searchGroup) {
    store.searchGroups.deleteFromSearchGroup(groupName, searchField);
  }
};

const handleCheckedFields = <T,>(store: Store<T>, name: string, value: any, operator = SearchOperator.In) => {
  const searchFields = store.searchFields.getSearchFieldsFor(name);
  if (searchFields?.length) {
    // TODO: This should be fixed. We shouldn't update an observable when it is not wrapped in an action.
    (searchFields[0].searchValue as Array<any>).push(value);
  } else {
    store.searchFields.addSearchFieldFor(name, {
      fieldNames: [name],
      operator,
      searchValue: [value],
    });
  }
};

// Remove the value from the fields '$in' array
const handleUncheckedFields = <T,>(store: Store<T>, name: string, value: any) => {
  const searchFields = store.searchFields.getSearchFieldsFor(name);
  if (searchFields?.length) {
    const searchValue = searchFields[0].searchValue;
    // determine whether searchValue is an array or not
    if (Array.isArray(searchValue)) {
      const index = searchValue.indexOf(value);
      if (index > -1) {
        searchValue.splice(index, 1);
        if (!searchValue.length) store.searchFields.deleteAllSearchFieldsFor(name);
      }
    } else {
      store.searchFields.deleteAllSearchFieldsFor(name);
    }
  }
  return;
};

const handleCheckAll = <T,>(store: Store<T>, filterInfoStore: FilterInfoStore) => {
  if (filterInfoStore.fieldOrGroup === 'FIELD') {
    filterInfoStore.info?.forEach((checkbox) => {
      const fieldCheckbox = checkbox as FieldLabelValue;
      const searchFieldsArray = store.searchFields.getSearchFieldsFor(filterInfoStore.groupName) || [];
      if (!filterInfoStore.getSelectedFields(searchFieldsArray, fieldCheckbox.value)) {
        handleCheckedFields(store, filterInfoStore.groupName, fieldCheckbox.value);
      }
    });
  } else {
    filterInfoStore.info?.forEach((checkbox) => {
      const groupCheckbox = checkbox as GroupLabelValue;
      const searchGroup = store.searchGroups.getSearchGroupFor(filterInfoStore.groupName);
      if (searchGroup && !filterInfoStore.getSelectedGroups(searchGroup, groupCheckbox.searchField)) {
        handleCheckedGroups(store, filterInfoStore.groupName, groupCheckbox.searchField);
      }
    });
  }
};

const handleUncheckAll = <T,>(store: Store<T>, filterInfoStore: FilterInfoStore) => {
  if (filterInfoStore.fieldOrGroup === 'FIELD') {
    filterInfoStore.info?.forEach((checkbox) => {
      const fieldCheckbox = checkbox as FieldLabelValue;
      handleUncheckedFields(store, filterInfoStore.groupName, fieldCheckbox.value);
    });
  } else {
    filterInfoStore.info?.forEach((checkbox) => {
      const groupCheckbox = checkbox as GroupLabelValue;
      handleUncheckedGroups(store, filterInfoStore.groupName, groupCheckbox.searchField);
    });
  }
};

const applyFilters = <T,>(store: Store<T>, localStore: Locals) => {
  store.queryItems();
  localStore.setVisible(false);
};
