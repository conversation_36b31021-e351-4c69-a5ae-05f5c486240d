import { observer, useLocalObservable } from 'mobx-react';
import { View } from 'react-native';
import { withTheme } from 'react-native-paper';
import { Button, Dialog } from '../../../lib';
import { RadioButton } from '../../../lib/ui/atoms/RadioButton';
import { TitleBar } from '../../../lib/ui/molecules/TitleBar';
interface DownloadOpportunityDialogProps {
  getVisible: () => boolean;
  onChooseDownload: (options: { value: 'all' | 'view' }) => void;
  onDismiss?: () => void;
  theme: ReactNativePaper.ThemeProp;
}

interface Locals {
  value: 'all' | 'view';
  setValue(newValue: 'all' | 'view'): void;
}

export const DownloadOpportunityDialog = withTheme(
  observer(({ getVisible, onDismiss, onChooseDownload, theme }: DownloadOpportunityDialogProps) => {
    const {
      styles: { paddings, margins },
    } = theme;

    const localStore = useLocalObservable<Locals>(() => ({
      value: 'view',
      setValue(newValue: 'all' | 'view') {
        this.value = newValue;
      },
    }));

    const content = (
      <View style={{ flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center' }}>
        <TitleBar
          title="Download Table"
          onClickClose={() => {
            initLocals(localStore);
            onDismiss && onDismiss();
          }}
        />
        <View style={[paddings.HorizontalL, paddings.VerticalML]}>
          <RadioButton
            label={'Download Current View'}
            getValue={() => 'view'}
            getChecked={() => localStore.value === 'view'}
            onChecked={() => localStore.setValue('view')}
          />
          <RadioButton
            label={'Download All Fields'}
            getValue={() => 'all'}
            getChecked={() => localStore.value === 'all'}
            onChecked={() => localStore.setValue('all')}
          />
          <Button
            type={'primary'}
            style={[margins.TopML]}
            compact={true}
            onPress={() => onChooseDownload({ value: localStore.value })}
          >
            DOWNLOAD TABLE
          </Button>
        </View>
      </View>
    );

    return (
      <Dialog
        getVisible={getVisible}
        onDismiss={() => {
          initLocals(localStore);
          onDismiss && onDismiss();
        }}
      >
        {content}
      </Dialog>
    );
  }),
);

const initLocals = (localStore: Locals) => {
  localStore.value = 'view';
};
