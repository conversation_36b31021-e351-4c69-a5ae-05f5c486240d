import { observer } from 'mobx-react';
import { StyleProp, TextStyle, View, ViewStyle } from 'react-native';
import { withTheme } from 'react-native-paper';
import { Label } from '../../../lib';
import { LinkedItem } from '../../../lib/ui/molecules/LinkedItem';
import { Opportunity, RelatedOpportunityType, UpdateOperator } from '../../../services/codegen/types';
import { OpportunityStore } from '../../../stores';
import { RelatedOpportunitySelectionType } from '../../../constants/RelatedOpportunity';
import OpportunitySearchResultStore from '../../../stores/OpportunitySearchResultStore';

interface RelatedOpportunitiesLayoutProps {
  style?: StyleProp<ViewStyle>;
  titleStyle?: StyleProp<TextStyle>;
  getOpportunityStore: () => OpportunityStore;
  onUpdateOpportunity: (
    operator: UpdateOperator,
    opportunity: Partial<Opportunity>,
    type: RelatedOpportunitySelectionType,
  ) => Promise<void>;
  onPressOpportunity: (id: string) => void;
  getEditable?: () => boolean;
  theme: ReactNativePaper.ThemeProp;
}

interface Locals {
  selectedOpportunityStore?: OpportunitySearchResultStore;
}

export const RelatedOpportunitiesLayout = withTheme(
  observer(
    ({
      getOpportunityStore,
      onUpdateOpportunity,
      onPressOpportunity,
      getEditable = () => true,
      style,
      titleStyle,
      theme,
    }: RelatedOpportunitiesLayoutProps) => {
      const {
        styles: { margins, defaultValues },
      } = theme;
      const opportunityStore = getOpportunityStore();
      const onDelete = (opportunity: Opportunity, type: RelatedOpportunitySelectionType) =>
        handleOnRemoveOpportunity(onUpdateOpportunity, opportunity, type);
      return (
        <View style={[style]}>
          {opportunityStore.opportunity?.relatedOpportunityCount ? (
            <>
              <RelatedOpportunityGroup
                getOpportunities={() =>
                  opportunityStore.opportunity?.owningOpportunities
                    .filter((relatedOp) => relatedOp.type === RelatedOpportunityType.Child)
                    .map((relatedOp) => relatedOp.source)
                }
                groupLabel="Parent"
                onPressOpportunity={onPressOpportunity}
                onDelete={onDelete}
                getEditable={getEditable}
                titleStyle={titleStyle}
                type={RelatedOpportunitySelectionType.Parent}
              />
              <RelatedOpportunityGroup
                getOpportunities={() =>
                  opportunityStore.opportunity?.ownedOpportunities.reduce((prev, relatedOp) => {
                    if (relatedOp.type === RelatedOpportunityType.Child) prev.push(relatedOp.target);
                    return prev;
                  }, [] as Opportunity[])
                }
                groupLabel="Children"
                onPressOpportunity={onPressOpportunity}
                onDelete={onDelete}
                getEditable={getEditable}
                titleStyle={titleStyle}
                type={RelatedOpportunitySelectionType.Child}
              />
              <RelatedOpportunityGroup
                getOpportunities={() => [
                  ...opportunityStore
                    .opportunity!.ownedOpportunities.filter(
                      (relatedOp) => relatedOp.type === RelatedOpportunityType.Linked,
                    )
                    .map((relatedOp) => relatedOp.target),
                ]}
                groupLabel="Linked"
                onPressOpportunity={onPressOpportunity}
                onDelete={onDelete}
                getEditable={getEditable}
                titleStyle={titleStyle}
                type={RelatedOpportunitySelectionType.Linked}
              />
              <RelatedOpportunityGroup
                getOpportunities={() => [
                  ...opportunityStore
                    .opportunity!.owningOpportunities.filter(
                      (relatedOp) => relatedOp.type === RelatedOpportunityType.Linked,
                    )
                    .map((relatedOp) => relatedOp.source),
                ]}
                groupLabel={opportunityStore.hasAnyOwnedLinks() ? '' : 'Linked'}
                onPressOpportunity={onPressOpportunity}
                onDelete={onDelete}
                getEditable={getEditable}
                titleStyle={titleStyle}
                type={RelatedOpportunitySelectionType.LinkedNotOwner}
              />
            </>
          ) : (
            <Label>There are no related Opportunities</Label>
          )}
        </View>
      );
    },
  ),
);

const RelatedOpportunityGroup = withTheme(
  observer(
    ({
      getOpportunities,
      groupLabel,
      onPressOpportunity,
      onDelete,
      getEditable = () => true,
      titleStyle,
      type,
      theme,
    }: {
      // can be observable
      getOpportunities: () => Opportunity[] | undefined;
      groupLabel?: string;
      onPressOpportunity: (id: string) => void;
      onDelete: (opportunity: Opportunity, type: RelatedOpportunitySelectionType) => void;
      getEditable?: () => boolean;
      titleStyle?: StyleProp<TextStyle>;
      type: RelatedOpportunitySelectionType;
      theme: ReactNativePaper.ThemeProp;
    }) => {
      const {
        styles: { margins, components },
      } = theme;
      const opportunities = getOpportunities();
      return (
        <>
          {opportunities?.length && groupLabel ? (
            <Label textStyle={[margins.BottomS, titleStyle]}>{groupLabel}</Label>
          ) : (
            ''
          )}
          {!!opportunities?.length &&
            opportunities?.map((opportunity: Opportunity) => {
              return (
                <View key={`relatedopp-${opportunity.id}`} style={{ flexDirection: 'row' }}>
                  <LinkedItem
                    getLinkText={() => opportunity.title}
                    style={[margins.VerticalM, components.linkedItemCompactStyle]}
                    onPress={() => onPressOpportunity(opportunity.id)}
                    key={opportunity.id}
                    onDelete={() => onDelete(opportunity, type)}
                    getEditable={getEditable}
                  />
                </View>
              );
            })}
        </>
      );
    },
  ),
);

/*componentWillUnmount() {
    const { getOpportunityStore } = this.props;
    getOpportunityStore().clearAll()this.handleOnOpportunityChanged;
  }*/

const handleOnRemoveOpportunity = async (
  onUpdateOpportunity: (
    operator: UpdateOperator,
    opportunity: Partial<Opportunity>,
    type: RelatedOpportunitySelectionType,
  ) => Promise<void>,
  opportunity: Partial<Opportunity>,
  type: RelatedOpportunitySelectionType,
) => {
  onUpdateOpportunity(UpdateOperator.Rm, opportunity, type);
};
