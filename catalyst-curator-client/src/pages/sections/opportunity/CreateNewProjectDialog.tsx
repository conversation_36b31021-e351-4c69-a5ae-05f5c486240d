import { observer, useLocalObservable } from 'mobx-react';
import { View } from 'react-native';
import { withTheme } from 'react-native-paper';
import { Button, Dialog, Label, LabeledTextInput } from '../../../lib';
import { Checkbox } from '../../../lib/ui/atoms/Checkbox';
import { TitleBar } from '../../../lib/ui/molecules/TitleBar';
import { CreateProjectFromOpportunityInput } from '../../../services/codegen/types';
interface CreateNewProjectDialogProps {
  getVisible: () => boolean;
  onCreateNewProject: (input: Partial<CreateProjectFromOpportunityInput>) => void;
  onDismiss?: () => void;
  theme: ReactNativePaper.ThemeProp;
}

interface Locals {
  title?: string;
  includeAttachments?: boolean;
  includeCategories?: boolean;
  includeStakeholders?: boolean;
  includeProblemSolution?: boolean;
  errors: Record<string, string>;
  setErrors(errors: Record<string, string>): void;
  setTitle(title: string): void;
  setIncludeAttachments(includeAttachments: boolean): void;
  setIncludeCategories(includeCategories: boolean): void;
  setIncludeStakeholders(includeStakeholders: boolean): void;
  setIncludeProblemSolution(includeProblemSolution: boolean): void;
  //TODO: Come back and address this linting issue.
  // eslint-disable-next-line @typescript-eslint/adjacent-overload-signatures
  setErrors(errors: Record<string, string>): void;
}

const initValues: Locals = {
  errors: {},
  title: undefined,
  includeProblemSolution: true,
  includeCategories: true,
  includeStakeholders: true,
  includeAttachments: false,
  setErrors(errors: Record<string, string>) {
    this.errors = errors;
  },
  setTitle(title: string) {
    this.title = title;
  },
  setIncludeAttachments(includeAttachments: boolean) {
    this.includeAttachments = includeAttachments;
  },
  setIncludeCategories(includeCategories: boolean) {
    this.includeCategories = includeCategories;
  },
  setIncludeStakeholders(includeStakeholders: boolean) {
    this.includeStakeholders = includeStakeholders;
  },
  setIncludeProblemSolution(includeProblemSolution: boolean) {
    this.includeProblemSolution = includeProblemSolution;
  },
};

export const CreateNewProjectDialog = withTheme(
  observer(({ getVisible, onDismiss, onCreateNewProject, theme }: CreateNewProjectDialogProps) => {
    const {
      styles: { paddings, margins },
      colors,
      fontSizes,
      fonts,
    } = theme;

    const localStore = useLocalObservable(() => initValues);

    const content = (
      <View style={{ flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', marginTop: 0 }}>
        <TitleBar
          title="Create Project Primer"
          onClickClose={() => {
            initLocals(localStore);
            onDismiss && onDismiss();
          }}
        />
        <View style={[paddings.HorizontalL, paddings.VerticalML]}>
          <LabeledTextInput
            textInputProps={{
              containerStyle: { flex: 1, alignSelf: 'stretch' },
              multiline: false,
              spellcheck: false,
              numberOfLines: 1,
              getValue: () => localStore.title,
              setValue: (value) => localStore.setTitle(value),
              onDebounceValue: () => {},
              placeholder: 'Project title goes here',
            }}
            style={[margins.BottomS]}
            labelText="Project Title"
            getHelperTextProps={() => ({ type: 'error', children: localStore.errors.title })}
          />
          <Label textStyle={paddings.BottomS}>Check the fields to copy to the Project Primer:</Label>
          <View style={{ gap: 8 }}>
            <Checkbox
              label={'Curated Problem & Solution Statement'}
              getChecked={() => !!localStore.includeProblemSolution}
              onChecked={() => localStore.setIncludeProblemSolution(true)}
              onUnchecked={() => localStore.setIncludeProblemSolution(false)}
              getDisabled={() => true}
            />
            <Checkbox
              label={'Categories'}
              getChecked={() => !!localStore.includeCategories}
              onChecked={() => localStore.setIncludeCategories(true)}
              onUnchecked={() => localStore.setIncludeCategories(false)}
              getDisabled={() => true}
            />
            <Checkbox
              label={'Stakeholders (available for assignment)'}
              getChecked={() => !!localStore.includeStakeholders}
              onChecked={() => localStore.setIncludeStakeholders(true)}
              onUnchecked={() => localStore.setIncludeStakeholders(false)}
              getDisabled={() => true}
            />
            <Checkbox
              label={'Attachments'}
              getChecked={() => !!localStore.includeAttachments}
              onChecked={() => localStore.setIncludeAttachments(true)}
              onUnchecked={() => localStore.setIncludeAttachments(false)}
            />
          </View>
          <Button
            type={'primary'}
            style={[margins.TopML]}
            compact={true}
            onPress={() => handleCreateNewProject(localStore, onCreateNewProject)}
          >
            Create Project Primer
          </Button>
        </View>
      </View>
    );

    return (
      <Dialog
        getVisible={getVisible}
        onDismiss={() => {
          initLocals(localStore);
          onDismiss && onDismiss();
        }}
      >
        {content}
      </Dialog>
    );
  }),
);

const handleCreateNewProject = (
  localStore: Locals,
  onCreateNewProject: (input: Partial<CreateProjectFromOpportunityInput>) => void,
) => {
  if (!localStore.title) {
    const errors = { ...localStore.errors, title: 'Title is required' };
    localStore.setErrors(errors);
  } else {
    onCreateNewProject({
      title: localStore.title,
      includeProblemSolution: localStore.includeProblemSolution,
      includeCategories: localStore.includeCategories,
      includeAttachments: localStore.includeAttachments,
    });
    initLocals(localStore);
  }
};

const initLocals = (localStore: Locals) => {
  localStore.title = initValues.title;
  localStore.includeProblemSolution = initValues.includeProblemSolution;
  localStore.includeCategories = initValues.includeCategories;
  localStore.includeStakeholders = initValues.includeStakeholders;
  localStore.includeAttachments = initValues.includeAttachments;
  localStore.errors = initValues.errors;
};
