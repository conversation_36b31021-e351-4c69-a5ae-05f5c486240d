import { StyleProp, View, ViewStyle } from 'react-native';
import { withTheme } from 'react-native-paper';
import { Button } from '../../../lib';
import { CreateProjectFromOpportunityInput } from '../../../services/codegen/types';
import { OpportunityStore } from '../../../stores';
import { CreateNewProjectDialog } from './CreateNewProjectDialog';
import { useLocalObservable } from 'mobx-react';

interface CreateNewProjectProps {
  opportunityStore: OpportunityStore;
  onProjectCreated?: (projectId: string) => void;
  style?: StyleProp<ViewStyle>;
  buttonStyle?: StyleProp<ViewStyle>;
  theme: ReactNativePaper.ThemeProp;
}

interface Locals {
  showDialog: boolean;
  setShowDialog(value: boolean): void;
}

export const CreateNewProject = withTheme(
  ({ style, buttonStyle, opportunityStore, theme, onProjectCreated = () => {} }: CreateNewProjectProps) => {
    const {
      styles: { margins, paddings },
    } = theme;

    const localStore = useLocalObservable(() => ({
      showDialog: false,
      setShowDialog(value: boolean) {
        this.showDialog = value;
      },
    }));
    return (
      <View {...{ style }}>
        <Button type="secondary" iconName="view-grid-plus" onPress={() => localStore.setShowDialog(true)}>
          PROJECT PRIMER
        </Button>
        {opportunityStore.opportunity && (
          <CreateNewProjectDialog
            onCreateNewProject={(input) =>
              handleCreateNewProject(opportunityStore, input, onProjectCreated, localStore)
            }
            getVisible={() => localStore.showDialog}
            onDismiss={() => localStore.setShowDialog(false)}
          />
        )}
      </View>
    );
  },
);

const handleCreateNewProject = async (
  opportunityStore: OpportunityStore,
  input: Partial<CreateProjectFromOpportunityInput>,
  onProjectCreated: (projectId: string) => void,
  localStore: Locals,
) => {
  try {
    localStore.setShowDialog(false);
    const projectStore = await opportunityStore.createProjectFromOpportunity(input);
    onProjectCreated(projectStore.project!.id);
  } catch (e) {
    // error handled by the store
  }
};
