import { withTheme } from 'react-native-paper';
import { ContentDialog } from '../../../lib/ui/molecules/ContentDialog';
import { Button, DialogProps, Label, TextInput, Title } from '../../../lib';
import { View } from 'react-native';
import { Text } from '../../../lib/ui/atoms/Text';
import { InfoCard } from '../../../lib/ui/molecules/InfoCard';
import { UserStore } from '../../../stores';
import { observer } from 'mobx-react';

export type AttachmentLocals = {
  displayName: string;
  notes: string;
  fileName: string;
  mimetype?: string;
  isDialogOpen: boolean;
  editingAttachmentId: string | null;
  setDisplayName(displayName: string): void;
  setNotes(notes: string): void;
  setFileName(fileName: string): void;
  setMimetype(mimetype: string): void;
  setIsDialogOpen(value: boolean): void;
  setEditingAttachmentId(id: string | null): void;
};

interface AttachmentDialogProps extends DialogProps {
  onConfirm: () => void;
  onDelete?: () => void;
  theme: ReactNativePaper.ThemeProp;
  locals: AttachmentLocals;
  userStore: UserStore;
}

export const AttachmentDialog = withTheme(
  observer(({ theme, onConfirm, onDelete, getVisible, locals, userStore }: AttachmentDialogProps) => {
    const {
      styles: { margins, components, fontSizes, fonts },
      colors,
    } = theme;

    const getIconForMimeType = (mimetype?: string) => {
      let iconName = 'file-document';
      if (mimetype?.startsWith('image/')) {
        iconName = 'file-image';
      } else if (mimetype?.startsWith('video/')) {
        iconName = 'video-image';
      } else if (mimetype?.startsWith('audio/')) {
        iconName = 'volume-low';
      } else if (
        mimetype?.startsWith('text/csv') ||
        mimetype?.includes('ms-excel') ||
        mimetype?.includes('spreadsheetml')
      ) {
        iconName = 'microsoft-excel';
      } else if (mimetype?.startsWith('application/pdf')) {
        iconName = 'file-pdf-box';
      } else if (mimetype?.startsWith('application/zip')) {
        iconName = 'folder-zip';
      }
      return iconName;
    };

    return (
      <ContentDialog
        {...{ onConfirm, theme }}
        noTitleBar={true}
        style={{ borderRadius: 16 }}
        contentStyle={[{ borderRadius: 8, width: 840, height: 370, backgroundColor: colors.paper, paddingTop: 0 }]}
        getVisible={getVisible}
        showClose={true}
      >
        <Title style={[{ alignSelf: 'flex-start' }, margins.TopL]}>
          {locals.editingAttachmentId ? 'Edit Attachment' : 'Add Attachment'}
        </Title>
        <Text style={{ alignSelf: 'flex-start' }}>Do not include CUI data. Fill in the details.</Text>
        <View style={[margins.TopML, { flexDirection: 'row', flex: 1, width: '100%' }]}>
          <View style={{ flex: 1, gap: 20 }}>
            <View>
              <Label textStyle={{ color: colors.disclaimerSecondary }}>Display Name</Label>
              <TextInput
                getValue={() => locals.displayName}
                setValue={(value) => locals.setDisplayName(value)}
                containerStyle={{ backgroundColor: colors.paper, flex: 1 }}
                placeholder="Optional display name"
              />
            </View>
            <View>
              <Label textStyle={{ color: colors.disclaimerSecondary }}>Notes</Label>
              <TextInput
                getValue={() => locals.notes}
                setValue={(value) => locals.setNotes(value)}
                containerStyle={{ backgroundColor: colors.paper, flex: 1 }}
                placeholder="Optional notes about this file"
                multiline={true}
              />
            </View>
          </View>
          <View style={{ marginLeft: 16, flex: 1 }}>
            <InfoCard
              title={locals.displayName || locals.fileName || 'Attachment Preview'}
              subtitle={locals.displayName ? locals.fileName : undefined}
              createdAt={new Date().toISOString()}
              createdBy={userStore.user}
              iconName={getIconForMimeType(locals.mimetype)}
              id="preview"
              notes={locals.notes}
            />
          </View>
        </View>
        <View
          style={{
            width: '100%',
            flexDirection: 'row',
            gap: 8,
          }}
        >
          {locals.editingAttachmentId && onDelete ? (
            <Button
              type="secondary"
              labelStyle={{ textTransform: 'capitalize', color: colors.negativeColor }}
              style={{ borderWidth: 0, flex: 1 }}
              contentStyle={{ justifyContent: 'flex-end' }}
              onPress={onDelete}
            >
              Delete
            </Button>
          ) : (
            <View style={{ flex: 1 }} />
          )}
          <View style={{ flexDirection: 'row', gap: 8, flex: 1, justifyContent: 'flex-end' }}>
            <Button
              type="secondary"
              labelStyle={{ textTransform: 'capitalize', color: colors.secondaryTextColor }}
              style={{ borderWidth: 0 }}
              onPress={() => locals.setIsDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button type="primary" labelStyle={{ textTransform: 'capitalize' }} onPress={onConfirm}>
              Confirm
            </Button>
          </View>
        </View>
      </ContentDialog>
    );
  }),
);
