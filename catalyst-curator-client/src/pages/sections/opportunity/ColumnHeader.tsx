import { withTheme } from 'react-native-paper';
import React from 'react';
import { SortIndicator } from '../../../lib';
import { GridSortIndicator } from '../../../lib/ui/molecules/GridSortIndicator';
import { StyleSheet, Pressable, View } from 'react-native';

import { Store } from '../../../appComponents/GenericGrid';
import { HeaderFilter } from './HeaderFilter';
import { FilterInfoStore } from '../../../stores/FilterInfoStore';
import { observer } from 'mobx-react';

interface ColumnHeaderProps<T> extends React.PropsWithChildren {
  theme: ReactNativePaper.ThemeProp;
  sortableCols: string[];
  getSortIndicator: (columnId: string) => SortIndicator;
  onColumnPress?: (columnId: string) => void;
  fieldName: string;
  filterInfo?: FilterInfoStore;
  store: Store<T>;
}

export const ColumnHeader = withTheme(
  observer(
    <T,>({
      theme,
      sortableCols,
      getSortIndicator,
      onColumnPress,
      fieldName,
      filterInfo,
      children,
      store,
    }: ColumnHeaderProps<T>) => {
      const {
        styles: { components },
      } = theme;
      const styleSheet = StyleSheet.create({
        containerStyle: { flex: 1, flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' },
      });

      return sortableCols.includes(fieldName) ? (
        <Pressable style={[styleSheet.containerStyle]} onPress={() => onColumnPress && onColumnPress(fieldName)}>
          <View style={{ flexDirection: 'row', alignItems: 'center', gap: 4 }}>
            {children}
            <GridSortIndicator getSortIndicator={() => getSortIndicator(fieldName)} />
          </View>
          {filterInfo && filterInfo.info.length > 0 ? (
            <HeaderFilter store={store} filterInfoStore={filterInfo} />
          ) : null}
        </Pressable>
      ) : (
        <View style={styleSheet.containerStyle}>
          <View style={{ flexDirection: 'row', alignItems: 'center', gap: 4 }}>
            {children}
            <GridSortIndicator getSortIndicator={() => getSortIndicator(fieldName)} />
          </View>
          {filterInfo && filterInfo.info.length > 0 ? (
            <HeaderFilter store={store} filterInfoStore={filterInfo} />
          ) : null}
        </View>
      );
    },
  ),
);
