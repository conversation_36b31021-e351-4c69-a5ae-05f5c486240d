import { observer } from 'mobx-react';
import { View } from 'react-native';
import { withTheme } from 'react-native-paper';
import { Button } from '../../../lib';
import { Spinner } from '../../../lib/ui/atoms/Spinner';
import { OpportunityStore } from '../../../stores';
import { QueryObserver } from '../../../lib/stores/Store';

interface OpportunityFabProps {
  queryObserver: QueryObserver;
  opportunityStore: OpportunityStore;
  theme: ReactNativePaper.ThemeProp;
}

export const OpportunityFab = withTheme(
  observer(({ queryObserver, opportunityStore, theme }: OpportunityFabProps) => {
    const {
      styles: { components, paddings, margins, fontSizes },
      colors,
    } = theme;

    return (
      <View style={[margins.M]}>
        {queryObserver.anyQueryInProgress ? (
          <Button type="primary" labelStyle={[fontSizes.medium]}>
            <Spinner color={colors.surface} size={14} style={[margins.RightMS, { alignSelf: 'center' }]} />
            WORKING..
          </Button>
        ) : opportunityStore.editable ? (
          <Button
            type="primary"
            iconName="lock"
            labelStyle={[fontSizes.medium]}
            onPress={() => (opportunityStore.editable = false)}
          >
            LOCK
          </Button>
        ) : (
          <Button
            type="primary"
            iconName="lock-open-outline"
            labelStyle={[fontSizes.medium]}
            onPress={() => (opportunityStore.editable = true)}
          >
            UNLOCK
          </Button>
        )}
      </View>
    );
  }),
);
