import { observer, useLocalObservable } from 'mobx-react';
import { StyleProp, View, ViewStyle } from 'react-native';
import { Text, withTheme } from 'react-native-paper';
import { Button, Dialog, Hr, LabeledTextInput } from '../../../lib';
import { ClickableText } from '../../../lib/ui/atoms/ClickableText';
import { TitleBar } from '../../../lib/ui/molecules/TitleBar';
import OpportunityStore from '../../../stores/OpportunityStore';
import { withAdminRights } from '../../../appComponents/hoc/withRestrictions';

interface DeleteOpportunityProps {
  theme: ReactNativePaper.ThemeProp;
  opportunityStore: OpportunityStore;
  style?: StyleProp<ViewStyle>;
  onDeleteOpportunity?: () => void;
}

interface Locals {
  confirmDeleteVisible: boolean;
  deleteText: string;
  setConfirmDeleteVisible: (visible: boolean) => void;
  setDeleteText: (text: string) => void;
}

export const DeleteOpportunity = withTheme(
  observer(({ style, theme, onDeleteOpportunity = () => {} }: DeleteOpportunityProps) => {
    const { styles, colors, fontSizes } = theme;
    const { margins, paddings } = styles;
    const localStore = useLocalObservable(() => ({
      confirmDeleteVisible: false,
      deleteText: '',
      setConfirmDeleteVisible(visible: boolean) {
        localStore.confirmDeleteVisible = visible;
      },
      setDeleteText(text: string) {
        localStore.deleteText = text;
      },
    }));

    return (
      <>
        <View style={style}>
          <ClickableText
            style={[fontSizes.small, { color: colors.error }]}
            onPress={() => localStore.setConfirmDeleteVisible(true)}
          >
            DELETE OPPORTUNITY
          </ClickableText>
        </View>
        <Dialog
          getVisible={() => localStore.confirmDeleteVisible}
          onDismiss={() => handleCancelDeleteOpportunity(localStore)}
        >
          <TitleBar title="Delete Opportunity" onClickClose={() => handleCancelDeleteOpportunity(localStore)} />
          <View style={[paddings.ML]}>
            <Text style={[{ color: colors.error }]}>This opportunity will be permanently deleted.</Text>
            <Hr style={[margins.TopML, margins.BottomML]} />
            <LabeledTextInput
              labelText="Type 'DELETE'"
              textInputProps={{
                getValue: () => localStore.deleteText,
                setValue: (value) => localStore.setDeleteText(value),
              }}
            />
            {localStore.deleteText !== 'DELETE' ? (
              <Button
                compact={false}
                type="primary"
                style={[paddings.VerticalM, { backgroundColor: '#ECEDF5' }]}
                getDisabled={() => true}
                labelStyle={{ color: '#676D79', opacity: 0.4 }}
                onPress={() => {}}
              >
                Delete Opportunity
              </Button>
            ) : (
              <Button
                compact={false}
                type="primary"
                style={[paddings.VerticalM, { backgroundColor: colors.error }]}
                onPress={() => handleConfirmDeleteOpportunity(localStore, onDeleteOpportunity)}
              >
                Delete Opportunity
              </Button>
            )}
          </View>
        </Dialog>
      </>
    );
  }),
);

const handleConfirmDeleteOpportunity = async (locals: Locals, onDeleteOpportunity: () => void) => {
  locals.confirmDeleteVisible = false;
  onDeleteOpportunity();
};

const handleCancelDeleteOpportunity = (locals: Locals) => {
  locals.setConfirmDeleteVisible(false);
};

export const AdminDeleteOpportunity = withAdminRights(DeleteOpportunity);
