import { withTheme } from 'react-native-paper';
import { ContentDialog } from '../../../lib/ui/molecules/ContentDialog';
import { DialogProps, Message, Title } from '../../../lib';
import { Linking, ScrollView, View } from 'react-native';
import { Text } from '../../../lib/ui/atoms/Text';

interface ArmyModDialogProps extends DialogProps {
  onConfirm: () => void;
  theme: ReactNativePaper.ThemeProp;
}
export const ArmyModDialog = withTheme(({ theme, onConfirm, getVisible }: ArmyModDialogProps) => {
  const {
    styles: { margins, paddings, components, fontSizes, fonts },
    colors,
  } = theme;

  return (
    <ContentDialog
      {...{ onConfirm, theme }}
      noTitleBar={true}
      style={{ borderRadius: 16 }}
      contentStyle={[{ borderRadius: 16, width: 660, height: 270, backgroundColor: theme.colors.background }]}
      getVisible={getVisible}
      showClose={true}
    >
      <Title style={[{ alignSelf: 'flex-start' }, margins.TopL]}>Army Modernization Priorities</Title>
      <ScrollView
        style={[margins.TopML]}
        contentContainerStyle={[{}]}
        keyboardShouldPersistTaps="handled"
        contentInsetAdjustmentBehavior="always"
      >
        <View style={[{ gap: 8 }]}>
          <Text style={components.messageInfoStyle}>
            This field requires the curation authority to identify which of the Army’s Modernization Priorities the
            proposed solution aligns with and explain why. If the proposed solution does not align with any of the
            Army’s Modernization Priorities, the curation authority must explain why the solution should still be
            pursued.
          </Text>
          <View style={{ flexDirection: 'row' }}>
            <Text>A descriptive list of Army Modernization Priorities can be found </Text>
            <Text
              style={{ color: 'blue', textDecorationLine: 'underline' }}
              onPress={() => Linking.openURL('https://dac.devcom.army.mil/what-we-do/army-modernization-priorities/')}
            >
              HERE.
            </Text>
          </View>
        </View>
      </ScrollView>
    </ContentDialog>
  );
});
