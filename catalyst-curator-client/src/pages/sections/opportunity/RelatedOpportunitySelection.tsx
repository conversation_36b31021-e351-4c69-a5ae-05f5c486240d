import React, { useRef } from 'react';
import { StyleProp, TextStyle, View, ViewStyle } from 'react-native';
import { withTheme } from 'react-native-paper';
import { DebounceFn, Debouncer, Label } from '../../../lib';
import { TypeAheadInput } from '../../../lib/ui/molecules/TypeAheadInput';
import { Opportunity } from '../../../services/codegen/types';
import { OpportunityStore } from '../../../stores';
import { OpportunitySearchStore } from '../../../stores/OpportunitySearchStore';
import { observable } from 'mobx';
import { observer } from 'mobx-react';
import { LabeledValue } from '../../../lib/ui/molecules/LabeledValue';
import { LinkedItem } from '../../../lib/ui/molecules/LinkedItem';

export const RelatedOpportunitySelection = withTheme(
  observer(
    ({
      getOpportunityStore,
      getSelectedOpportunity,
      titleSearchStore,
      onItemSelected,
      onPressOpportunity,
      style,
      titleStyle,
      theme,
    }: {
      getOpportunityStore: () => OpportunityStore;
      getSelectedOpportunity: () => Partial<Opportunity> | undefined;
      titleSearchStore: OpportunitySearchStore;
      onPressOpportunity: (id: string) => void;
      onItemSelected: (item?: { label: string; value: Partial<Opportunity> }) => void;
      style?: StyleProp<ViewStyle>;
      titleStyle?: StyleProp<TextStyle>;
      theme: ReactNativePaper.ThemeProp;
    }) => {
      const debouncerRef = useRef(Debouncer()).current;
      const {
        styles: { margins, components },
      } = theme;

      const selectedOp = getSelectedOpportunity();
      return (
        <View style={[margins.BottomML, style]}>
          <Label textStyle={[margins.BottomS, titleStyle]}>{'Select Opportunity:'}</Label>
          {selectedOp ? (
            <LinkedItem
              getLinkText={() => selectedOp.title!}
              getEditable={() => true}
              onPress={() => onPressOpportunity(selectedOp.id!)}
              onDelete={() => {
                onItemSelected(undefined);
              }}
              style={[components.linkedItemCompactStyle]}
            />
          ) : (
            <TypeAheadInput
              style={[{ zIndex: 5 }]}
              getValue={() => titleSearchStore.getSearchValue('title') || ''}
              onValueChange={(value) => handleOnSearchValueChange(debouncerRef, titleSearchStore, value)}
              onItemSelected={(item) => {
                titleSearchStore.setFieldSearchValue({ fieldName: 'title', value: '' });
                onItemSelected(item);
              }}
              onSubmit={() => {}}
              getDropdownItems={() => {
                return titleSearchStore.matchedOpportunities
                  .filter(
                    (opportunity: Partial<Opportunity>) => opportunity.id !== getOpportunityStore().opportunity?.id,
                  )
                  .map((opportunity: Partial<Opportunity>) => ({
                    label: opportunity.title!,
                    value: opportunity!,
                  }));
              }}
              placeholder="Search for an oppportunity by title"
            />
          )}
        </View>
      );
    },
  ),
);

const handleOnSearchValueChange = (debouncer: DebounceFn, titleSearchStore: OpportunitySearchStore, value: string) => {
  titleSearchStore.setFieldSearchValue({ fieldName: 'title', value });
  debouncer(() => {
    titleSearchStore.queryOpportunities();
  }, 500);
};
