/* eslint-disable @typescript-eslint/no-non-null-asserted-optional-chain */
// TODO: COME BACK TO THE ABOVE LINTING ISSUE AND ADDRESS IT
import { StyleProp, TextStyle, View } from 'react-native';
import { withTheme } from 'react-native-paper';
import { Button, ConfirmationDialog, DropdownMenu, Label, MenuItem } from '../../../lib';
import { Opportunity, UpdateOperator } from '../../../services/codegen/types';
import { OpportunityStore } from '../../../stores';
import { OpportunitySearchStore } from '../../../stores/OpportunitySearchStore';
import { observer, useLocalObservable } from 'mobx-react';
import { RelatedOpportunitySelectionType } from '../../../constants/RelatedOpportunity';
import { LabeledValue } from '../../../lib/ui/molecules/LabeledValue';
import { RelatedOpportunitySelection } from './RelatedOpportunitySelection';
import OpportunitySearchResultStore from '../../../stores/OpportunitySearchResultStore';

export const RELATED_OPTYPE_LABELS = {
  [RelatedOpportunitySelectionType.Child]: 'A child of this Opportunity',
  [RelatedOpportunitySelectionType.Linked]: 'Linked to this Opportunity',
  [RelatedOpportunitySelectionType.Parent]: 'The parent of this Opportunity',
};

enum ExceptionType {
  ALREADY_EXISTS,
  TARGET_HAS_CHILDREN,
  TARGET_HAS_PARENT,
  CURRENT_HAS_PARENT,
  CURRENT_HAS_CHILDREN,
}

interface ValidationState {
  valid?: boolean;
  operationType?: RelatedOpportunitySelectionType;
  exceptionType?: ExceptionType;
  message?: string;
}

interface Locals {
  selectedType?: MenuItem;
  validationState?: ValidationState;
  errorVisible?: boolean;
  setSelectedType(type?: MenuItem): void;
  setValidationState(validationState?: ValidationState): void;
  setErrorVisible(visible?: boolean): void;
}

interface RelatedOpportunityInputProps {
  getOpportunityStore: () => OpportunityStore;
  titleSearchStore: OpportunitySearchStore;
  selectedOpportunityStore?: OpportunitySearchResultStore;
  titleStyle?: StyleProp<TextStyle>;
  onPressOpportunity: (id: string) => void;
  onUpdateOpportunity: (
    operator: UpdateOperator,
    opportunity: Partial<Opportunity>,
    type: RelatedOpportunitySelectionType,
  ) => Promise<void>;
  onSelectOpportunity: (selectedOpportunityStore: OpportunitySearchResultStore | undefined) => void;
  theme: ReactNativePaper.ThemeProp;
}

export const RelatedOpportunityInput = withTheme(
  observer((props: RelatedOpportunityInputProps) => {
    const {
      getOpportunityStore,
      titleSearchStore,
      selectedOpportunityStore,
      titleStyle,
      onPressOpportunity,
      onSelectOpportunity,
      theme,
    } = props;

    const localStore = useLocalObservable<Locals>(() => ({
      selectedType: undefined,
      validationState: undefined,
      errorVisible: undefined,
      setSelectedType(type?: MenuItem) {
        this.selectedType = type;
      },
      setValidationState(validationState?: ValidationState) {
        this.validationState = validationState;
      },
      setErrorVisible(visible?: boolean) {
        this.errorVisible = visible;
      },
    }));

    const {
      styles: { margins, borders, fontSizes },
    } = theme;
    return (
      <View>
        <LabeledValue
          getValue={() => getOpportunityStore().opportunity?.title}
          labelText="This Opportunity"
          style={[{ flexDirection: 'column' }, margins.BottomML]}
        />
        <RelatedOpportunitySelection
          {...{
            getOpportunityStore,
            onPressOpportunity,
            titleSearchStore,
            titleStyle,
            style: [{ zIndex: 5 }],
            getSelectedOpportunity: () => selectedOpportunityStore?.opportunity,
            onItemSelected: (item) => {
              const store = item?.value ? new OpportunitySearchResultStore(item?.value) : undefined;
              if (store) store.initialize();
              onSelectOpportunity(store);
            },
          }}
        />
        <Label textStyle={[margins.BottomMS]}>The selected Opportunity will be:</Label>
        <DropdownMenu
          style={[{ zIndex: 3 }, margins.BottomML]}
          getMenuItems={() => [
            {
              label: RELATED_OPTYPE_LABELS[RelatedOpportunitySelectionType.Child],
              value: RelatedOpportunitySelectionType.Child,
            },
            {
              label: RELATED_OPTYPE_LABELS[RelatedOpportunitySelectionType.Parent],
              value: RelatedOpportunitySelectionType.Parent,
            },
            {
              label: RELATED_OPTYPE_LABELS[RelatedOpportunitySelectionType.Linked],
              value: RelatedOpportunitySelectionType.Linked,
            },
          ]}
          onItemSelected={(item) => localStore.setSelectedType(item)}
          getValue={() => localStore.selectedType}
          getEditable={() => true}
        />
        <Button
          style={[{ alignSelf: 'flex-start' }, margins.TopL]}
          getDisabled={() => getOpportunityStore().queryInProgress}
          type="primary"
          labelStyle={[fontSizes.medium]}
          onPress={() => {
            handleOnCreateRelationship(props, localStore);
          }}
        >
          Create Relationship
        </Button>
        <ValidationException {...{ props, localStore }} />
      </View>
    );
  }),
);

const isOverrideException = (validationState?: ValidationState) => {
  // turn off override for now per Kirk 11/01/23
  return false;
  /*
  if (!validationState) return false;
  const { valid, message, operationType, exceptionType } = validationState;
  return (
    (operationType === RelatedOpportunitySelectionType.Child && exceptionType === ExceptionType.TARGET_HAS_PARENT) ||
    (operationType === RelatedOpportunitySelectionType.Parent && exceptionType === ExceptionType.CURRENT_HAS_PARENT)
  );
  */
};

const ValidationException = observer(
  ({ props, localStore }: { props: RelatedOpportunityInputProps; localStore: Locals }) => {
    const { theme, selectedOpportunityStore, getOpportunityStore } = props;
    const {
      styles: { margins, paddings, defaultValues },
    } = theme;
    const { validationState, errorVisible } = localStore;
    if (!validationState) return null;
    const { valid, message, operationType, exceptionType } = validationState;
    const opportunityStore = getOpportunityStore();
    const onCancel = () => localStore.setErrorVisible(false);
    const onConfirm = isOverrideException(validationState)
      ? async () => {
          if (
            operationType === RelatedOpportunitySelectionType.Child &&
            exceptionType === ExceptionType.TARGET_HAS_PARENT
          ) {
            await selectedOpportunityStore?.removeParentRelationship();
          } else if (
            operationType === RelatedOpportunitySelectionType.Parent &&
            exceptionType === ExceptionType.CURRENT_HAS_PARENT
          ) {
            await opportunityStore.removeParentRelationship();
          }
          await handleOnCreateRelationship(props, localStore);
          localStore.setErrorVisible(false);
        }
      : onCancel;

    const confirmLabel = isOverrideException(validationState) ? 'Change Relaltionship' : 'OK';

    return (
      <ConfirmationDialog
        confirmLabel={confirmLabel}
        style={[{ maxWidth: defaultValues.defaultComponentWidth }]}
        messageProps={{ type: 'error' }}
        getVisible={() => !!errorVisible}
        dismissable={true}
        onConfirm={onConfirm}
        onCancel={onCancel}
        showCancelButton={isOverrideException(validationState)}
        message={message}
        title="Invalid Relationship Request"
      />
    );
  },
);

const validate = (
  opportunityStore: OpportunityStore,
  selectedOpportunityStore: OpportunitySearchResultStore,
  type: RelatedOpportunitySelectionType,
): ValidationState => {
  //the valildation order matters here as the overridable conditions should be last...
  if (type === RelatedOpportunitySelectionType.Parent) {
    // when adding a parent check to see:
    // if this opportunity is already the parent (do nothing)
    if (opportunityStore.hasParentRelationship(selectedOpportunityStore.opportunity?.id!)) {
      return {
        exceptionType: ExceptionType.ALREADY_EXISTS,
        message: 'Relationship already exists',
        operationType: type,
      };
    }
    // if the current opportunity already is already a parent (has children) (message)
    if (opportunityStore.hasAnyChildren()) {
      return {
        exceptionType: ExceptionType.CURRENT_HAS_CHILDREN,
        message:
          'Unable to make the selected opportunity a parent as the current opportunity is already assigned as a parent. Please review and adjust relevant relationships. ',
        operationType: type,
      };
    }
    // if the selected opportunity already has a parent (is a child) (link instead)
    if (selectedOpportunityStore.hasAnyParent()) {
      return {
        exceptionType: ExceptionType.TARGET_HAS_PARENT,
        message:
          'Unable to make the selected opportunity a parent as it is already assigned as a child. Please review and adjust relevant relationships.',
        operationType: type,
      };
    }
    // if this current opportunity already has a parent (override?)
    if (opportunityStore.hasAnyParent()) {
      return {
        exceptionType: ExceptionType.CURRENT_HAS_PARENT,
        message:
          'Unable to make the selected opportunity a parent as the current opportunity is already assigned as a child. Please review and adjust relevant relationships.',
        operationType: type,
      };
    }
  } else {
    // if it's already a child or link of/to this opportunity (do nothing)
    if (opportunityStore.hasOwnedRelationship(selectedOpportunityStore.opportunity?.id!, type)) {
      return {
        exceptionType: ExceptionType.ALREADY_EXISTS,
        message: 'Relationship already exists',
        operationType: type,
      };
    }
    if (type === RelatedOpportunitySelectionType.Linked) {
      // if it's already linked from the other direction
      if (opportunityStore.isLinkedToBy(selectedOpportunityStore.opportunity?.id!)) {
        return {
          exceptionType: ExceptionType.ALREADY_EXISTS,
          message: 'Relationship already exists',
          operationType: type,
        };
      }
    }
    if (type === RelatedOpportunitySelectionType.Child) {
      // if current opportunity already has a parent (is a child) (message)
      if (opportunityStore.hasAnyParent()) {
        return {
          exceptionType: ExceptionType.CURRENT_HAS_PARENT,
          message:
            'Unable to make the selected opportunity a child as the current opportunity is already assigned as a child. Please review and adjust relevant relationships.',
          operationType: type,
        };
      }
      // if the child is a parent itself (link instead)
      if (selectedOpportunityStore.hasAnyChildren()) {
        return {
          exceptionType: ExceptionType.TARGET_HAS_CHILDREN,
          message:
            'Unable to make the selected opportunity a child as it is already assigned as a parent.  Please review and adjust relevant relationships.',
          operationType: type,
        };
      }
      // if the child already has a parent (override?)
      if (selectedOpportunityStore.hasAnyParent()) {
        return {
          exceptionType: ExceptionType.TARGET_HAS_PARENT,
          message:
            'Unable to make the selected opportunity a child as it is already assigned as a child. Please review and adjust relevant relationships.',
          operationType: type,
        };
      }
    }
  }
  return { valid: true };
};

const handleOnCreateRelationship = async (props: RelatedOpportunityInputProps, localStore: Locals) => {
  const { getOpportunityStore, titleSearchStore, selectedOpportunityStore, onUpdateOpportunity, onSelectOpportunity } =
    props;
  if (selectedOpportunityStore && localStore.selectedType) {
    await selectedOpportunityStore.refresh();
    const validationState = validate(getOpportunityStore(), selectedOpportunityStore, localStore.selectedType.value);
    if (validationState.valid) {
      await onUpdateOpportunity(
        UpdateOperator.Add,
        selectedOpportunityStore.opportunity as Partial<Opportunity>,
        localStore.selectedType.value as RelatedOpportunitySelectionType,
      );
      onSelectOpportunity(undefined);
      localStore.setSelectedType(undefined);
      titleSearchStore.clearAll();
    } else {
      localStore.setValidationState(validationState);
      localStore.setErrorVisible(true);
    }
  }
};
