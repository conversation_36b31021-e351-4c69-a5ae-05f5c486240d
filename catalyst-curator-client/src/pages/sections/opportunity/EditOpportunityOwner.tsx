import { observer } from 'mobx-react';
import { withTheme } from 'react-native-paper';
import { Button, DependentDropdown, HelperTextProps, LabeledPhoneInput, LabeledTextInput } from '../../../lib';
import { View } from 'react-native';
import { OpportunityOwnerStore } from '../../../stores/OpportunityOwnerStore';
import {
  getCountryCodeItem,
  getCountryCodeItems,
  onSelectCountryCode,
  setPhoneNumber,
  stripCountryCode,
} from '../../../lib/phoneNumberUtils';
import { StoresProvider } from '../../../lib/stores/StoresProvider';
import { CuratorStores } from '../../../platform/initializers';
import { ClickableText } from '../../../lib/ui/atoms/ClickableText';
import { OpportunityStore, UserStore } from '../../../stores';
import { OpportunityOwnersLocals } from './curated-opportunity-groups/OpportunityOwners';
import { OpportunityOwnerListStore } from '../../../stores/OpportunityOwnerListStore';
import { OwnershipStatus } from '../../../services/codegen/types';

type EditOpportunityOwnerProps = {
  theme: ReactNativePaper.ThemeProp;
  opportunityOwnersStore: OpportunityOwnerStore;
  opportunityOwnerListStore: OpportunityOwnerListStore;
  opportunityStore: OpportunityStore;
  locals: OpportunityOwnersLocals;
  userStore: UserStore;
};
export const EditOpportunityOwner = withTheme(
  observer(
    ({ opportunityOwnersStore, opportunityOwnerListStore, locals, theme, userStore }: EditOpportunityOwnerProps) => {
      const { colors, fonts } = theme;

      const organizationStore = StoresProvider.get<CuratorStores>().getStore('OrganizationStore');
      const isAdminUser = userStore.isAdminUser;

      const doesOwnerHaveId = opportunityOwnersStore.getValue('userId');
      const canEdit = isAdminUser || !doesOwnerHaveId;
      return (
        <View
          style={{
            alignItems: 'stretch',
            borderWidth: 1,
            borderColor: colors.border,
            padding: 16,
            borderRadius: 4,
            backgroundColor: colors.greyBackgroundColor,
          }}
        >
          <View style={{ flexDirection: 'row', gap: 8 }}>
            <LabeledTextInput
              style={{ flex: 1 }}
              labelText="First Name"
              textInputProps={{
                getValue: () => opportunityOwnersStore.getValue('firstName'),
                setValue: (value) => {
                  opportunityOwnersStore.setValue('firstName', value);
                },
                editable: canEdit,
              }}
              getHelperTextProps={() => getHelperTextProps('firstName', opportunityOwnersStore)}
            />
            <LabeledTextInput
              style={{ flex: 1 }}
              labelText="Last Name"
              textInputProps={{
                getValue: () => opportunityOwnersStore.getValue('lastName'),
                setValue: (value) => {
                  opportunityOwnersStore.setValue('lastName', value);
                },
                editable: canEdit,
              }}
              getHelperTextProps={() => getHelperTextProps('lastName', opportunityOwnersStore)}
            />
          </View>
          <View style={{ flexDirection: 'row', gap: 8 }}>
            <LabeledTextInput
              style={{ flex: 1 }}
              labelText="Email Address"
              textInputProps={{
                getValue: () => opportunityOwnersStore.getValue('emailAddress'),
                setValue: (value) => {
                  opportunityOwnersStore.setValue('emailAddress', value);
                },
                editable: !doesOwnerHaveId,
              }}
              getHelperTextProps={() => getHelperTextProps('emailAddress', opportunityOwnersStore)}
            />
            <LabeledTextInput
              style={{ flex: 1 }}
              labelText="Alternative Email (Optional)"
              textInputProps={{
                getValue: () => opportunityOwnersStore.getValue('altContact'),
                setValue: (value) => {
                  opportunityOwnersStore.setValue('altContact', value);
                },
                editable: canEdit,
              }}
            />
          </View>
          <View style={{ flexDirection: 'row', gap: 8 }}>
            <LabeledPhoneInput
              style={{ flex: 1 }}
              countryCodeMenuProps={{
                getMenuItems: () => getCountryCodeItems(),
                getValue: () => getCountryCodeItem(opportunityOwnersStore.getValue('phone')),
                onItemSelected: (item) => {
                  onSelectCountryCode(
                    item,
                    opportunityOwnersStore.setValue.bind(opportunityOwnersStore),
                    opportunityOwnersStore.getValue('phone'),
                  );
                },
                getEditable: () => true,
                anchorStyle: { height: 41, maxHeight: 41 },
              }}
              textInputProps={{
                editable: canEdit,
                multiline: false,
                spellcheck: false,
                getValue: () => stripCountryCode(opportunityOwnersStore.getValue('phone')),
                setValue: (value) =>
                  setPhoneNumber(
                    value,
                    opportunityOwnersStore.setValue.bind(opportunityOwnersStore),
                    opportunityOwnersStore.getValue('phone'),
                  ),
                placeholder: '(*************',
                autoComplete: 'new-password',
              }}
              labelText="Phone"
            />
            <LabeledTextInput
              style={{ flex: 1 }}
              labelText="Organization Role (Optional)"
              textInputProps={{
                getValue: () => opportunityOwnersStore.getValue('organizationRole'),
                setValue: (value) => {
                  opportunityOwnersStore.setValue('organizationRole', value);
                },
                editable: canEdit,
              }}
            />
          </View>
          {organizationStore.organizations.length > 0 &&
            (() => {
              const orgValue1 = opportunityOwnersStore.getValue('org1');
              const orgValue2 = opportunityOwnersStore.getValue('org2');
              const orgValue3 = opportunityOwnersStore.getValue('org3');
              const orgValue4 = opportunityOwnersStore.getValue('org4');
              return (
                <>
                  <View style={[{ flexDirection: 'column' }]}>
                    <DependentDropdown
                      defaultValues={[
                        {
                          label: 'Organization (Optional)',
                          value: orgValue1 && { label: orgValue1, value: orgValue1 },
                          fieldName: 'org1',
                        },
                        {
                          label: undefined,
                          value: orgValue2 && { label: orgValue2, value: orgValue2 },
                          fieldName: 'org2',
                        },
                        {
                          label: undefined,
                          value: orgValue3 && { label: orgValue3, value: orgValue3 },
                          fieldName: 'org3',
                        },
                        {
                          label: undefined,
                          value: orgValue4 && { label: orgValue4, value: orgValue4 },
                          fieldName: 'org4',
                        },
                      ]}
                      labeledDropdownMenuProps={{
                        dropdownMenuProps: {
                          getEditable: () => canEdit,
                          anchorStyle: { maxWidth: null },
                        },
                      }}
                      getMenuGroups={() => organizationStore.orgsToMenuGroups()}
                      onItemSelected={(item, fieldName) => {
                        opportunityOwnersStore.setValue(fieldName, item?.label);
                      }}
                    />
                  </View>
                </>
              );
            })()}
          <View style={{ flexDirection: 'row', gap: 16, justifyContent: 'flex-end', alignItems: 'center' }}>
            <ClickableText
              style={[fonts.regularTitle, { color: colors.negativeColor, fontSize: 16 }]}
              onPress={async () => {
                locals.toggleShowInputs();
                await opportunityOwnersStore.localClearAll();
              }}
            >
              Cancel
            </ClickableText>
            <Button
              type="primary"
              labelStyle={[fonts.regularTitle, { textTransform: 'capitalize', fontSize: 16 }]}
              onPress={async () => {
                if (opportunityOwnersStore.validateAll()) {
                  await opportunityOwnersStore.addOpportunityOwner(opportunityOwnerListStore.getOpportunityId());
                  if (opportunityOwnerListStore.currentOwner)
                    await opportunityOwnerListStore.updateOpportunityOwner({
                      id: opportunityOwnerListStore.currentOwner.id,
                      input: {
                        status: OwnershipStatus.Previous,
                      },
                    });
                  await opportunityOwnerListStore.refresh();
                  locals.toggleShowInputs();
                }
              }}
            >
              Save
            </Button>
          </View>
        </View>
      );
    },
  ),
);

const getHelperTextProps = (name: string, opportunityOwnersStore: OpportunityOwnerStore): HelperTextProps => {
  return { type: 'error', children: opportunityOwnersStore.getPropertyError(name) };
};
