import { observer, useLocalObservable } from 'mobx-react';
import { withTheme } from 'react-native-paper';
import { OpportunityStore } from '../../../../stores';
import { Pressable, View } from 'react-native';
import { Target } from '@nandorojo/anchor';
import { LabeledDropdownMenu, MenuItem } from '../../../../lib';
import { Icon } from '../../../../lib/ui/atoms/Icon';
import { EchelonAppDialog } from '../EchelonAppDialog';

interface EchelonApplicabilityProps {
  editable: boolean;
  theme: ReactNativePaper.ThemeProp;
  opportunityStore: OpportunityStore;
  handleDebounce: (opportunityStore: OpportunityStore) => void;
}
export const EchelonApplicabilityItems = [
  { label: 'Army', value: 'Army' },
  { label: 'Corps', value: 'Corps' },
  { label: 'Division', value: 'Division' },
  { label: 'Brigade / Regiment', value: 'Brigade / Regiment' },
  { label: 'Battalion', value: 'Battalion' },
  { label: 'Company / Battery / Troop', value: 'Company / Battery / Troop' },
  { label: 'Platoon', value: 'Platoon' },
];
export const EchelonApplicability = withTheme(
  observer(({ editable, handleDebounce, opportunityStore, theme }: EchelonApplicabilityProps) => {
    const {
      styles: { components },
    } = theme;

    const localStore = useLocalObservable(() => ({
      isEchelonAppDialogOpen: false,
      setIsEchelonAppDialogOpen(value: boolean) {
        this.isEchelonAppDialogOpen = value;
      },
    }));

    return (
      <>
        <View style={[components.rowStyle]}>
          <Target name={'EchelonApplicability'}>
            <LabeledDropdownMenu
              dropdownMenuProps={{
                getEditable: () => editable,
                getMenuItems: () => EchelonApplicabilityItems,
                onItemSelected: (item: MenuItem) => {
                  opportunityStore.setValue('echelonApplicability', item.value);
                  handleDebounce(opportunityStore);
                },
                getValue: () =>
                  EchelonApplicabilityItems.find(
                    (item) => item.value === opportunityStore.opportunity?.echelonApplicability,
                  ) || { label: 'Unassigned', value: 'unassigned' },
              }}
              labelText={'Echelon Applicability'}
              icon={
                <Pressable onPress={() => localStore.setIsEchelonAppDialogOpen(true)}>
                  <Icon name="help-circle" color="#676D79" size={24} iconStyle={{ pointerEvents: 'box-only' }} />
                </Pressable>
              }
              labelProps={{ textStyle: { gap: 8, display: 'flex', alignItems: 'center' } }}
            />
          </Target>
        </View>
        <EchelonAppDialog
          getVisible={() => {
            return localStore.isEchelonAppDialogOpen;
          }}
          onConfirm={() => {
            localStore.setIsEchelonAppDialogOpen(false);
          }}
          style={{}}
        />
      </>
    );
  }),
);
