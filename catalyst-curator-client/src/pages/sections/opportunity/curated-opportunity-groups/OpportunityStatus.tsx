import { observer } from 'mobx-react';
import { withTheme } from 'react-native-paper';
import { Group } from '../../../../lib/ui/atoms/Group';
import { CuratedOpportunityGroupProps, LabeledTextInputProps } from '../CuratedOpportunity';
import { View } from 'react-native';
import { Target } from '@nandorojo/anchor';
import { LabeledDropdownMenu, MenuItem } from '../../../../lib';
import { CollapsibleView } from '../../../../lib/ui/atoms/CollapsibleView';
import { Text } from '../../../../lib/ui/atoms/Text';
import { OpportunityStatus as OpportunityStatusCodeGen } from '../../../../services/codegen/types';
import { OpportunityStore } from '../../../../stores';

interface OpportunityStatusProps extends CuratedOpportunityGroupProps {
  getLabeledTextInput: (props: LabeledTextInputProps) => JSX.Element;
  handleDebounce: (opportunityStore: OpportunityStore) => void;
}

const StatusItems = [
  {
    label: OpportunityStatusCodeGen.Approved,
    value: OpportunityStatusCodeGen.Approved,
  },
  {
    label: OpportunityStatusCodeGen.Pending,
    value: OpportunityStatusCodeGen.Pending,
  },
  {
    label: OpportunityStatusCodeGen.Archived,
    value: OpportunityStatusCodeGen.Archived,
  },
];

export const OpportunityStatus = withTheme(
  observer(
    ({ editable, theme, getLabeledTextInput, opportunityStore, label, handleDebounce }: OpportunityStatusProps) => {
      const {
        styles: { components, fonts, fontSizes, margins },
        colors,
      } = theme;

      return (
        <Group title="Opportunity Status" style={{ zIndex: 2 }} description={label}>
          <View style={[components.rowStyle]}>
            <Target name={'status'}>
              <LabeledDropdownMenu
                dropdownMenuProps={{
                  getEditable: () => editable,
                  getMenuItems: () => StatusItems,
                  onItemSelected: (item: MenuItem) => {
                    opportunityStore.setValue('status', item.value);
                    handleDebounce(opportunityStore);
                  },
                  getValue: () =>
                    opportunityStore.opportunity?.status
                      ? { label: opportunityStore.opportunity.status, value: opportunityStore.opportunity.status }
                      : undefined,
                }}
                labelText={'Status'}
              />
            </Target>
          </View>
          {
            // show in readonly mode if it's not empty
            !editable &&
              opportunityStore.getValue('statusNotes') &&
              getLabeledTextInput({
                opportunityStore,
                fieldName: 'statusNotes',
                labelText: 'Status Notes',
                textInputProps: {
                  multiline: true,
                  placeholder: 'Additional notes about Status',
                },
                style: [components.rowStyle],
                editable,
              })
          }
          {
            // show in edit mode, expanded if data present
            editable && (
              <CollapsibleView
                header={
                  <Text style={[fontSizes.mediumSmall, fonts.medium, { color: colors.primary }]}>ADDITIONAL NOTES</Text>
                }
                contentStyle={[margins.VerticalM]}
                defaultOpen={!!opportunityStore.getValue('statusNotes')}
              >
                {getLabeledTextInput({
                  opportunityStore,
                  fieldName: 'statusNotes',
                  labelText: 'Status Notes',
                  textInputProps: {
                    multiline: true,
                    placeholder: 'Additional notes about Status',
                  },
                  style: [components.rowStyle],
                  editable,
                })}
              </CollapsibleView>
            )
          }
        </Group>
      );
    },
  ),
);
