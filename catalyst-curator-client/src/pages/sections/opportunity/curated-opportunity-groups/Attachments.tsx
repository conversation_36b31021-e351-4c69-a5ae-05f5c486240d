import { observer, useLocalObservable } from 'mobx-react';
import { withTheme } from 'react-native-paper';
import { Group } from '../../../../lib/ui/atoms/Group';
import { CuratedOpportunityGroupProps, LabeledTextInputProps } from '../CuratedOpportunity';
import { CollapsibleView } from '../../../../lib/ui/atoms/CollapsibleView';
import { Text } from '../../../../lib/ui/atoms/Text';
import { OpportunityStore, UserStore } from '../../../../stores';
import { AttachmentInfo, FileAttachments } from '../../../../lib/ui/molecules/FileAttachments';
import { Attachment, Location } from '../../../../services/codegen/types';
import { openWebFileLink } from '../../../../utilities/File';
import { Label } from '../../../../lib';
import { Links } from '../Links';
import { AttachmentDialog, AttachmentLocals } from '../AttachmentDialog';

interface AttachmentsProps extends CuratedOpportunityGroupProps {
  getLabeledTextInput: (props: LabeledTextInputProps) => JSX.Element;
  userStore: UserStore;
}

export const Attachments = withTheme(
  observer(({ editable, theme, opportunityStore, label, getLabeledTextInput, userStore }: AttachmentsProps) => {
    const {
      styles: { components, margins, fontSizes, fonts },
      colors,
    } = theme;

    const locals = useLocalObservable<AttachmentLocals & { pendingFile: File | null; pendingUri: string }>(() => ({
      displayName: '',
      notes: '',
      fileName: '',
      mimetype: '',
      isDialogOpen: false,
      editingAttachmentId: null,
      pendingFile: null,
      pendingUri: '',
      setDisplayName(displayName: string) {
        this.displayName = displayName;
      },
      setNotes(notes: string) {
        this.notes = notes;
      },
      setFileName(fileName: string) {
        this.fileName = fileName;
      },
      setMimetype(mimetype: string) {
        this.mimetype = mimetype;
      },
      setIsDialogOpen(value: boolean) {
        this.isDialogOpen = value;
      },
      setEditingAttachmentId(id: string | null) {
        this.editingAttachmentId = id;
      },
    }));

    return (
      <Group title="Attachments" style={[{ zIndex: 2 }]} description={label}>
        <Label textStyle={{ color: colors.secondaryTextColor }}>Files</Label>
        <FileAttachments
          style={[margins.BottomL, { marginTop: 8 }]}
          getAttachments={() => opportunityStore.opportunity?.attachments as AttachmentInfo[]}
          onPress={(id) => handleOnRetrieveDocument(opportunityStore, id)}
          onDelete={(id) => handleOnDeleteDocument(opportunityStore, id)}
          getIsInProgress={() => opportunityStore.fileUploadInProgress}
          onAddAttachment={async (result) => await handleOnAddDocument(result, opportunityStore)}
          onEditAttachment={(attachmentId) => {
            // Populate the form with existing attachment data
            const attachment = opportunityStore.opportunity?.attachments.find((a: Attachment) => a.id === attachmentId);
            if (attachment) {
              locals.setFileName(attachment.name);
              locals.setDisplayName(attachment.displayName || '');
              locals.setNotes(attachment.notes || '');
              locals.setMimetype(attachment.mimetype || '');
              locals.setEditingAttachmentId(attachmentId);
              locals.setIsDialogOpen(true);
            }
          }}
          onShowAttachmentDialog={(file, uri) => {
            // Show dialog for new file upload
            locals.pendingFile = file;
            locals.pendingUri = uri;
            locals.setFileName(file.name);
            locals.setDisplayName('');
            locals.setNotes('');
            locals.setMimetype(file.type);
            locals.setEditingAttachmentId(null);
            locals.setIsDialogOpen(true);
          }}
          getEditable={() => editable}
          iconSize={32}
        />
        <Label textStyle={{ color: colors.secondaryTextColor, marginBottom: 8 }}>Links</Label>
        <Links opportunityStore={opportunityStore} editable={editable} userStore={userStore} />
        {
          // show in readonly mode if it's not empty
          !editable &&
            opportunityStore.getValue('attachmentNotes') &&
            getLabeledTextInput({
              opportunityStore,
              fieldName: 'attachmentNotes',
              labelText: 'Notes About Attached Files',
              textInputProps: {
                multiline: true,
                placeholder: 'Additional information about attached files',
              },
              editable,
              style: [components.rowStyle],
            })
        }
        {
          // show in edit mode, expanded if data present
          editable && (
            <CollapsibleView
              header={
                <Text style={[fontSizes.mediumSmall, fonts.medium, { color: colors.primary }]}>ATTACHMENT NOTES</Text>
              }
              contentStyle={[margins.VerticalM]}
              defaultOpen={!!opportunityStore.getValue('attachmentNotes')}
            >
              {getLabeledTextInput({
                opportunityStore,
                fieldName: 'attachmentNotes',
                labelText: 'Notes About Attached Files',
                textInputProps: {
                  multiline: true,
                  placeholder: 'Additional information about attached files',
                },
                editable,
                style: [components.rowStyle],
              })}
            </CollapsibleView>
          )
        }
        <AttachmentDialog
          getVisible={() => locals.isDialogOpen}
          onConfirm={async () => {
            if (locals.editingAttachmentId) {
              // Update existing attachment
              await opportunityStore.updateAttachment(
                locals.editingAttachmentId,
                locals.displayName || undefined,
                locals.notes || undefined,
              );
            } else if (locals.pendingFile && locals.pendingUri) {
              // Upload new attachment with metadata
              await opportunityStore.addAttachment({
                file: locals.pendingFile,
                uri: locals.pendingUri,
                displayName: locals.displayName || undefined,
                notes: locals.notes || undefined,
              });
            }
            // Reset form and close dialog
            locals.setIsDialogOpen(false);
            locals.setEditingAttachmentId(null);
            locals.setDisplayName('');
            locals.setNotes('');
            locals.setFileName('');
            locals.setMimetype('');
            locals.pendingFile = null;
            locals.pendingUri = '';
          }}
          onDelete={async () => {
            if (locals.editingAttachmentId) {
              await opportunityStore.deleteAttachment(locals.editingAttachmentId);
              locals.setIsDialogOpen(false);
              locals.setEditingAttachmentId(null);
              locals.setDisplayName('');
              locals.setNotes('');
              locals.setFileName('');
              locals.setMimetype('');
              locals.pendingFile = null;
              locals.pendingUri = '';
            }
          }}
          onDismiss={() => {
            locals.setIsDialogOpen(false);
            locals.setEditingAttachmentId(null);
            locals.setDisplayName('');
            locals.setNotes('');
            locals.setFileName('');
            locals.setMimetype('');
            locals.pendingFile = null;
            locals.pendingUri = '';
          }}
          locals={locals}
          userStore={userStore}
        />
      </Group>
    );
  }),
);

const handleOnRetrieveDocument = (opportunityStore: OpportunityStore, attachmentId: string): void => {
  const attachment = opportunityStore.opportunity?.attachments.find((a: Attachment) => a.id === attachmentId);
  opportunityStore.getAttachment(attachmentId).then((loc: Location) => {
    return openWebFileLink(loc.location, attachment?.name || '', attachment?.mimetype || '');
  });
};

const handleOnAddDocument = async (
  result: { file: File; uri: string },
  opportunityStore: OpportunityStore,
): Promise<void> => {
  await opportunityStore.addAttachment(result);
};

const handleOnDeleteDocument = (opportunityStore: OpportunityStore, attachmentId: string): void => {
  opportunityStore.deleteAttachment(attachmentId);
};
