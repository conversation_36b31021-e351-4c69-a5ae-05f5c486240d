import { observer } from 'mobx-react';
import { withTheme } from 'react-native-paper';
import { Group } from '../../../../lib/ui/atoms/Group';
import { CuratedOpportunityGroupProps, LabeledTextInputProps } from '../CuratedOpportunity';
import { View } from 'react-native';

interface CustomFieldsProps extends CuratedOpportunityGroupProps {
  getLabeledTextInput: (props: LabeledTextInputProps) => JSX.Element;
}
export const CustomFields = withTheme(
  observer(({ editable, theme, getLabeledTextInput, opportunityStore, label }: CustomFieldsProps) => {
    const {
      styles: { components },
    } = theme;

    return (
      <Group title="Custom Fields" description={label}>
        <View style={[components.rowStyle]}>
          {getLabeledTextInput({
            opportunityStore,
            fieldName: 'initiatives',
            labelText: 'Initiative',
            textInputProps: {
              multiline: false,
              placeholder: 'Associated initiative or Line Of Effort',
            },
            style: [components.rowStyle],
            editable,
          })}
        </View>
        <View style={[components.rowStyle]}>
          {getLabeledTextInput({
            opportunityStore,
            fieldName: 'endorsements',
            labelText: 'Endorsed/Approved by',
            textInputProps: {
              multiline: false,
              placeholder: 'Name of endorser/approver',
            },
            style: [components.rowStyle],
            editable,
          })}
        </View>
      </Group>
    );
  }),
);
