import { observer } from 'mobx-react';
import { withTheme } from 'react-native-paper';
import { Group } from '../../../../lib/ui/atoms/Group';
import { CuratedOpportunityGroupProps, LabeledTextInputProps } from '../CuratedOpportunity';
import { View } from 'react-native';
import { CollapsibleView } from '../../../../lib/ui/atoms/CollapsibleView';
import { Text } from '../../../../lib/ui/atoms/Text';
import { LabeledDropdownMenu, LabelValue, MenuItem } from '../../../../lib';
import { OpportunityStore } from '../../../../stores';

interface CampaignProps extends CuratedOpportunityGroupProps {
  getLabeledTextInput: (props: LabeledTextInputProps) => JSX.Element;
  fieldLabel?: string;
  campaigns: LabelValue[] | undefined;
  handleDebounce: (opportunityStore: OpportunityStore) => void;
}
export const Campaign = withTheme(
  observer(
    ({
      editable,
      theme,
      getLabeledTextInput,
      opportunityStore,
      label,
      fieldLabel,
      campaigns,
      handleDebounce,
    }: CampaignProps) => {
      const {
        styles: { components, fontSizes, fonts, margins },
        colors,
      } = theme;
      if (!fieldLabel) return null;
      return (
        <Group title={fieldLabel} description={label}>
          <View style={[components.rowStyle]}>
            <LabeledDropdownMenu
              dropdownMenuProps={{
                getEditable: () => editable,
                getMenuItems: () =>
                  campaigns?.map((item) => ({
                    label: item.label,
                    value: item.value || item.label,
                  })),
                onItemSelected: (item: MenuItem) => {
                  opportunityStore.setValue('campaign', item.value);
                  handleDebounce(opportunityStore);
                },
                getValue: () => ({
                  label: opportunityStore.getValue('campaign') || 'None',
                  value: opportunityStore.getValue('campaign'),
                }),
              }}
              labelText={fieldLabel}
            />
          </View>
          {
            // show in readonly mode if it's not empty
            !editable &&
              opportunityStore.getValue('campaignNotes') &&
              getLabeledTextInput({
                opportunityStore,
                fieldName: 'campaignNotes',
                labelText: `${fieldLabel} Notes`,
                textInputProps: {
                  multiline: true,
                  placeholder: `Additional notes about ${fieldLabel}`,
                },
                style: [components.rowStyle],
                editable,
              })
          }
          {
            // show in edit mode, expanded if data present
            editable && (
              <CollapsibleView
                header={
                  <Text style={[fontSizes.mediumSmall, fonts.medium, { color: colors.primary }]}>ADDITIONAL NOTES</Text>
                }
                contentStyle={[margins.VerticalM]}
                defaultOpen={!!opportunityStore.getValue('campaignNotes')}
              >
                {getLabeledTextInput({
                  opportunityStore,
                  fieldName: 'campaignNotes',
                  labelText: `${fieldLabel} Notes`,
                  textInputProps: {
                    multiline: true,
                    placeholder: `Additional notes about ${fieldLabel}`,
                  },
                  style: [components.rowStyle],
                  editable,
                })}
              </CollapsibleView>
            )
          }
        </Group>
      );
    },
  ),
);
