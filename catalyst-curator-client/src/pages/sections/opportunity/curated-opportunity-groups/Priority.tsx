import { observer } from 'mobx-react';
import { withTheme } from 'react-native-paper';
import { Group } from '../../../../lib/ui/atoms/Group';
import { CuratedOpportunityGroupProps, LabeledTextInputProps } from '../CuratedOpportunity';
import { Target } from '@nandorojo/anchor';
import OpportunityPriority from '../OpportunityPriority';
import { OpportunityStore } from '../../../../stores';
import { CollapsibleView } from '../../../../lib/ui/atoms/CollapsibleView';
import { Text } from '../../../../lib/ui/atoms/Text';

interface PriorityProps extends CuratedOpportunityGroupProps {
  getLabeledTextInput: (props: LabeledTextInputProps) => JSX.Element;
  handleDebounce: (opportunityStore: OpportunityStore) => void;
}
export const Priority = withTheme(
  observer(({ editable, theme, getLabeledTextInput, opportunityStore, label, handleDebounce }: PriorityProps) => {
    const {
      styles: { components, fontSizes, margins, fonts },
      colors,
    } = theme;

    return (
      <Group title="Priority" style={{ zIndex: 2 }} description={label}>
        <Target name={'priority'}>
          <OpportunityPriority
            opportunityStore={opportunityStore}
            style={[components.rowStyle]}
            onPropertyChanged={(name, value) => {
              opportunityStore.setValue('priority', value);
              handleDebounce(opportunityStore);
            }}
            getEditable={() => editable}
          />
        </Target>
        {
          // show in readonly mode if it's not empty
          !editable &&
            opportunityStore.getValue('priorityNotes') &&
            getLabeledTextInput({
              opportunityStore,
              fieldName: 'priorityNotes',
              labelText: 'Priority Notes',
              textInputProps: {
                multiline: true,
                placeholder: 'Additional notes about Priority',
              },
              style: [components.rowStyle],
              editable,
            })
        }
        {
          // show in edit mode, expanded if data present
          editable && (
            <CollapsibleView
              header={
                <Text style={[fontSizes.mediumSmall, fonts.medium, { color: colors.primary }]}>ADDITIONAL NOTES</Text>
              }
              contentStyle={[margins.VerticalM]}
              defaultOpen={!!opportunityStore.getValue('priorityNotes')}
            >
              {getLabeledTextInput({
                opportunityStore,
                fieldName: 'priorityNotes',
                labelText: 'Priority Notes',
                textInputProps: {
                  multiline: true,
                  placeholder: 'Additional notes about Priority',
                },
                style: [components.rowStyle],
                editable,
              })}
            </CollapsibleView>
          )
        }
      </Group>
    );
  }),
);
