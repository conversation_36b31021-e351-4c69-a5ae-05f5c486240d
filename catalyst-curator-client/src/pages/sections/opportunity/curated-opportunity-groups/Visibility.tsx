import { observer } from 'mobx-react';
import { withTheme } from 'react-native-paper';
import { Group } from '../../../../lib/ui/atoms/Group';
import { CuratedOpportunityGroupProps, LabeledTextInputProps } from '../CuratedOpportunity';
import { Checkbox } from '../../../../lib/ui/atoms/Checkbox';
import { Target } from '@nandorojo/anchor';
import { OpportunityStore } from '../../../../stores';
import { OpportunityVisibility } from '../../../../services/codegen/types';

interface VisibilityProps extends CuratedOpportunityGroupProps {
  handleDebounce: (opportunityStore: OpportunityStore) => void;
}
export const Visibility = withTheme(
  observer(({ editable, theme, opportunityStore, label, handleDebounce }: VisibilityProps) => {
    const {
      styles: { components },
    } = theme;

    return (
      <Group title="Mark As Private" description={label}>
        <Target name={'visibility'}>
          <Checkbox
            getChecked={() => opportunityStore.opportunity?.visibility === OpportunityVisibility.Private}
            label={'Private'}
            onChecked={() => {
              opportunityStore.setValue('visibility', OpportunityVisibility.Private);
              handleDebounce(opportunityStore);
            }}
            onUnchecked={() => {
              opportunityStore.setValue('visibility', OpportunityVisibility.All);
              handleDebounce(opportunityStore);
            }}
            getDisabled={() => !editable}
          />
        </Target>
      </Group>
    );
  }),
);
