import { observer } from 'mobx-react';
import { withTheme } from 'react-native-paper';
import { Group } from '../../../../lib/ui/atoms/Group';
import { CuratedOpportunityGroupProps, LabeledTextInputProps } from '../CuratedOpportunity';

interface LegacyContentProps extends CuratedOpportunityGroupProps {
  getLabeledTextInput: (props: LabeledTextInputProps) => JSX.Element;
}
export const LegacyContent = withTheme(
  observer(({ editable, theme, getLabeledTextInput, opportunityStore, label }: LegacyContentProps) => {
    const {
      styles: { components },
    } = theme;

    return (
      <Group title="Legacy Content" description={label}>
        {getLabeledTextInput({
          opportunityStore,
          fieldName: 'solutionPathway',
          labelText: 'Solution Pathway Working Title',
          textInputProps: {
            multiline: false,
            placeholder: 'Working title for the potential solution pathway',
          },
          style: [components.rowStyle],
          editable,
        })}
        {getLabeledTextInput({
          opportunityStore,
          fieldName: 'solutionPathwayDetails',
          labelText: 'Solution Pathway Details',
          textInputProps: { multiline: true },
          style: [components.rowStyle],
          editable,
        })}
      </Group>
    );
  }),
);
