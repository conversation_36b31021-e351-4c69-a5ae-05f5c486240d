import { observer, useLocalObservable } from 'mobx-react';
import { withTheme } from 'react-native-paper';
import { CuratedOpportunityGroupProps } from '../CuratedOpportunity';
import { OpportunityStore } from '../../../../stores';
import { Pressable, View } from 'react-native';
import { Target } from '@nandorojo/anchor';
import { LabeledDropdownMenu, MenuItem } from '../../../../lib';
import { Icon } from '../../../../lib/ui/atoms/Icon';
import { ArmyModDialog } from '../ArmyModDialog';

interface ArmyModernizationPrioritiesProps {
  editable: boolean;
  theme: ReactNativePaper.ThemeProp;
  opportunityStore: OpportunityStore;
  handleDebounce: (opportunityStore: OpportunityStore) => void;
}
export const ArmyModernizationPrioritiesItems = [
  { label: 'Air and Missile Defense (AMD)', value: 'Air and Missile Defense (AMD)' },
  { label: 'All-Domain Sensing (ADS)', value: 'All-Domain Sensing (ADS)' },
  { label: 'Command and Control (C2)', value: 'Command and Control (C2)' },
  { label: 'Contested Logistics (CL)', value: 'Contested Logistics (CL)' },
  { label: 'Future Vertical Lift (FVL)', value: 'Future Vertical Lift (FVL)' },
  { label: 'Long Range Precision Fires (LRPF)', value: 'Long Range Precision Fires (LRPF)' },
  { label: 'Next Generation Combat Vehicles (NGCV)', value: 'Next Generation Combat Vehicles (NGCV)' },
  { label: 'Soldier Lethality (SL)', value: 'Soldier Lethality (SL)' },
  { label: 'Synthetic Training Environment (STE)', value: 'Synthetic Training Environment (STE)' },
];
export const ArmyModernizationPriorities = withTheme(
  observer(({ editable, handleDebounce, opportunityStore, theme }: ArmyModernizationPrioritiesProps) => {
    const {
      styles: { components },
    } = theme;

    const localStore = useLocalObservable(() => ({
      isArmyModDialogOpen: false,
      setIsArmyModDialogOpen(value: boolean) {
        this.isArmyModDialogOpen = value;
      },
    }));

    return (
      <>
        <View style={[components.rowStyle]}>
          <Target name={'armyModernizationPriorities'}>
            <LabeledDropdownMenu
              dropdownMenuProps={{
                getEditable: () => editable,
                getMenuItems: () => ArmyModernizationPrioritiesItems,
                onItemSelected: (item: MenuItem) => {
                  opportunityStore.setValue('armyModernizationPriority', item.value);
                  handleDebounce(opportunityStore);
                },
                getValue: () =>
                  ArmyModernizationPrioritiesItems.find(
                    (item) => item.value === opportunityStore.opportunity?.armyModernizationPriority,
                  ) || { label: 'Unassigned', value: 'unassigned' },
              }}
              labelText={'Army Modernization Priorities'}
              icon={
                <Pressable onPress={() => localStore.setIsArmyModDialogOpen(true)}>
                  <Icon name="help-circle" color="#676D79" size={24} iconStyle={{ pointerEvents: 'box-only' }} />
                </Pressable>
              }
              labelProps={{ textStyle: { gap: 8, display: 'flex', alignItems: 'center' } }}
            />
          </Target>
        </View>
        <ArmyModDialog
          getVisible={() => {
            return localStore.isArmyModDialogOpen;
          }}
          onConfirm={() => {
            localStore.setIsArmyModDialogOpen(false);
          }}
          style={{}}
        />
      </>
    );
  }),
);
