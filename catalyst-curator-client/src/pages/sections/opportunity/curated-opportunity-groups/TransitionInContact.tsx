import { observer } from 'mobx-react';
import { withTheme } from 'react-native-paper';
import { OpportunityStore } from '../../../../stores';
import { View } from 'react-native';
import { Target } from '@nandorojo/anchor';
import { Label, LabeledInput, Text } from '../../../../lib';
import { RadioButton } from '../../../../lib/ui/atoms/RadioButton';
import { TransitionInContactDropdown } from '../TransitionInContactDropdown';

interface TransitionInContactProps {
  editable: boolean;
  theme: ReactNativePaper.ThemeProp;
  opportunityStore: OpportunityStore;
  handleDebounce: (opportunityStore: OpportunityStore) => void;
}

export const TransitionInContact = withTheme(
  observer(({ editable, handleDebounce, opportunityStore, theme }: TransitionInContactProps) => {
    const {
      styles: { components, fonts, fontSizes, paddings },
    } = theme;

    return (
      <>
        <View style={[components.rowStyle]}>
          <Target name={'IsTransitionInContact'}>
            <Label>Is this a Transformation in Contact (TiC) Line of Effort?</Label>
            <LabeledInput>
              {editable ? (
                <View style={{ flexDirection: 'row' }}>
                  <RadioButton
                    label="No"
                    getChecked={() => !opportunityStore.opportunity?.isTiCLOE || false}
                    getValue={() => 'No'}
                    onChecked={() => {
                      opportunityStore.setValue('isTiCLOE', false);
                      opportunityStore.setValue('transitionInContactLineOfEffort', undefined);
                      opportunityStore.setValue('operationalRoles', undefined);
                      opportunityStore.setValue('capabilityArea', undefined);
                      handleDebounce(opportunityStore);
                    }}
                  />
                  <RadioButton
                    label="Yes"
                    getChecked={() => opportunityStore.opportunity?.isTiCLOE || false}
                    getValue={() => 'Yes'}
                    onChecked={() => {
                      opportunityStore.setValue('isTiCLOE', true);
                      handleDebounce(opportunityStore);
                    }}
                  />
                </View>
              ) : (
                <Text style={[fonts.regular, fontSizes.small, paddings.XS]}>
                  {opportunityStore.opportunity?.isTiCLOE ? 'Yes' : 'No'}
                </Text>
              )}
            </LabeledInput>
          </Target>
        </View>
        {opportunityStore.opportunity?.isTiCLOE && (
          <TransitionInContactDropdown
            editable={editable}
            handleDebounce={handleDebounce}
            opportunityStore={opportunityStore}
          />
        )}
      </>
    );
  }),
);
