import { observer, useLocalObservable } from 'mobx-react';
import { withTheme } from 'react-native-paper';
import { CuratedOpportunityGroupProps } from '../CuratedOpportunity';
import { Pressable, View } from 'react-native';
import { Target } from '@nandorojo/anchor';
import { LabelValue, LabeledDropdownMenu, MenuItem } from '../../../../lib';
import { Icon } from '../../../../lib/ui/atoms/Icon';
import { WarfightingFunctionsDialog } from '../WarfightingFunctionsDialog';
import { OpportunityStore } from '../../../../stores';

interface WarfightingFunctionProps extends CuratedOpportunityGroupProps {
  handleDebounce: (opportunityStore: OpportunityStore) => void;
  fieldLabel?: string;
  functions?: LabelValue[];
}

export const WarfightingFunction = withTheme(
  observer(
    ({ editable, theme, opportunityStore, label, handleDebounce, functions, fieldLabel }: WarfightingFunctionProps) => {
      const {
        styles: { components },
      } = theme;

      const localStore = useLocalObservable(() => ({
        isWarfighterDialogOpen: false,
        setIsWarfighterDialogOpen(value: boolean) {
          this.isWarfighterDialogOpen = value;
        },
      }));

      const functionItems = functions?.map((item) => {
        return {
          label: item.label,
          value: item.label,
        };
      });

      return (
        <>
          <View style={[components.rowStyle]}>
            <Target name={'function'}>
              <LabeledDropdownMenu
                dropdownMenuProps={{
                  getEditable: () => editable,
                  getMenuItems: () => functionItems,
                  onItemSelected: (item: MenuItem) => {
                    opportunityStore.setValue('function', item.value);
                    handleDebounce(opportunityStore);
                  },
                  getValue: () =>
                    opportunityStore.opportunity?.function
                      ? { label: opportunityStore.opportunity.function, value: opportunityStore.opportunity.function }
                      : { label: 'Unassigned', value: 'unassigned' },
                }}
                labelText={fieldLabel}
                icon={
                  <Pressable onPress={() => localStore.setIsWarfighterDialogOpen(true)}>
                    <Icon name="help-circle" color="#676D79" size={24} iconStyle={{ pointerEvents: 'box-only' }} />
                  </Pressable>
                }
                labelProps={{ textStyle: { gap: 8, display: 'flex', alignItems: 'center' } }}
              />
            </Target>
          </View>
          <WarfightingFunctionsDialog
            getVisible={() => {
              return localStore.isWarfighterDialogOpen;
            }}
            onConfirm={() => {
              localStore.setIsWarfighterDialogOpen(false);
            }}
          />
        </>
      );
    },
  ),
);
