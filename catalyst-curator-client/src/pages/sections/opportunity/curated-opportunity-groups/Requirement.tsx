import { observer, useLocalObservable } from 'mobx-react';
import { Linking, Pressable, View } from 'react-native';
import { withTheme } from 'react-native-paper';

import { Button, Label, LabeledDropdownMenu, MenuItem, Text } from '../../../../lib';
import { Group } from '../../../../lib/ui/atoms/Group';
import { Icon } from '../../../../lib/ui/atoms/Icon';
import { OpportunityStore } from '../../../../stores';
import { CuratedOpportunityGroupProps } from '../CuratedOpportunity';
import { RequirementItem } from '../RequirementItem';
import { AddRequirement } from '../AddRequirement';
import { ACMDropdown } from '../ACMDropdown';
import { CDIDDialog } from '../CDIDDialog';
import { Checkbox } from '../../../../lib/ui/atoms/Checkbox';

const EXTERNAL_LINKS = {
  FORGE_FUTURES_LIBRARY: 'https://forge.futures.army.mil/requirements/LiteratureLibrary',
  AFC_FCC: 'https://www.dla.mil/Information-Operations/Services/Applications/WebFLIS/',
} as const;

interface RequirementProps extends CuratedOpportunityGroupProps {
  handleDebounce: (opportunityStore: OpportunityStore) => void;
}

const CDID_ITEMS = [
  { label: 'Aviation CDID', value: 'Aviation CDID' },
  { label: 'Chaplain CDID', value: 'Chaplain CDID' },
  { label: 'Cyber CDID', value: 'Cyber CDID' },
  { label: 'Directorate of Concepts', value: 'Directorate of Concepts' },
  { label: 'FCC Futures Integration Directorate', value: 'FCC Futures Integration Directorate' },
  { label: 'Fires CDID', value: 'Fires CDID' },
  { label: 'Intelligence CDID', value: 'Intelligence CDID' },
  { label: 'Joint Modernization Command (JMC)', value: 'Joint Modernization Command (JMC)' },
  { label: 'Maneuver CDID', value: 'Maneuver CDID' },
  { label: 'Maneuver Support CDID', value: 'Maneuver Support CDID' },
  { label: 'Medical CDID', value: 'Medical CDID' },
  { label: 'Mission Command CDID', value: 'Mission Command CDID' },
  { label: 'Sustainment CDID', value: 'Sustainment CDID' },
];

export const Requirement = withTheme(
  observer(({ editable, opportunityStore, label, handleDebounce, theme }: RequirementProps) => {
    const localStore = useLocalObservable(() => ({
      isDialogOpen: false,
      setIsDialogOpen(value: boolean) {
        this.isDialogOpen = value;
      },
      isNewRequirementOpen: false,
      setIsNewRequirementOpen(value: boolean) {
        this.isNewRequirementOpen = value;
      },
      editingRequirementId: null as string | null,
      setEditingRequirementId(id: string | null) {
        this.editingRequirementId = id;
      },
      isCDIDDialogOpen: false,
      setIsCDIDDialogOpen(value: boolean) {
        this.isCDIDDialogOpen = value;
      },
    }));

    const { colors, fonts } = theme;

    return (
      <Group
        title="Army Capability Requirement Alignment"
        style={[{ zIndex: 3 }]}
        contentStyle={{ gap: 16 }}
        description={label}
      >
        <View style={{ gap: 16 }}>
          <Label>Existing Requirements</Label>
          <Text>
            Search for existing Requirements in the{' '}
            <Text
              style={{ color: 'blue', textDecorationLine: 'underline' }}
              onPress={() => Linking.openURL(EXTERNAL_LINKS.FORGE_FUTURES_LIBRARY)}
            >
              Forge Futures Library
            </Text>{' '}
            or in the{' '}
            <Text
              style={{ color: 'blue', textDecorationLine: 'underline' }}
              onPress={() => Linking.openURL(EXTERNAL_LINKS.AFC_FCC)}
            >
              AFC FCC
            </Text>
            . CAC enabled
          </Text>
          {localStore.isNewRequirementOpen ? (
            <AddRequirement
              setIsOpen={(value) => localStore.setIsNewRequirementOpen(value)}
              opportunityStore={opportunityStore}
            />
          ) : (
            editable && (
              <View style={{ flexDirection: 'row' }}>
                <Button
                  labelStyle={[{ textTransform: 'capitalize', color: colors.secondaryTextColor }, fonts.mediumTitle]}
                  type="secondary"
                  compact
                  onPress={() => localStore.setIsNewRequirementOpen(true)}
                  iconName="plus-box-outline"
                  iconRight
                >
                  Add Requirement
                </Button>
              </View>
            )
          )}
          {opportunityStore.opportunity?.requirements.map((requirement) => (
            <RequirementItem
              key={requirement.id}
              requirement={requirement}
              isEditing={localStore.editingRequirementId === requirement.id}
              editable={editable}
              opportunityStore={opportunityStore}
              onEdit={(requirement) => localStore.setEditingRequirementId(requirement)}
              onCancelEdit={() => localStore.setEditingRequirementId(null)}
            />
          ))}
        </View>
        <LabeledDropdownMenu
          dropdownMenuProps={{
            getEditable: () => editable,
            getMenuItems: () => CDID_ITEMS,
            onItemSelected: (item: MenuItem) => {
              opportunityStore.setValue('capabilitySponsor', item.value);
              // Clear ACM selection when CDID changes
              opportunityStore.setValue('armyCapabilityManager', undefined);
              handleDebounce(opportunityStore);
            },

            getValue: () =>
              CDID_ITEMS.find((item) => item.value === opportunityStore.opportunity?.capabilitySponsor) || {
                label: 'Unassigned',
                value: 'Unassigned',
              },
          }}
          labelText={'Capability Sponsor'}
          icon={
            <Pressable onPress={() => localStore.setIsCDIDDialogOpen(true)}>
              <Icon name="help-circle" color="#676D79" size={24} iconStyle={{ pointerEvents: 'box-only' }} />
            </Pressable>
          }
          labelProps={{ textStyle: { gap: 8, display: 'flex', alignItems: 'center' } }}
        />
        <ACMDropdown editable={editable} opportunityStore={opportunityStore} handleDebounce={handleDebounce} />
        <Label>Existing Army Requirement</Label>
        <Checkbox
          getChecked={() => opportunityStore.opportunity?.existingArmyRequirement || false}
          label={'No Existing Army Capability Requirement Identified'}
          onChecked={() => {
            opportunityStore.setValue('existingArmyRequirement', true);
            handleDebounce(opportunityStore);
          }}
          onUnchecked={() => {
            opportunityStore.setValue('existingArmyRequirement', false);
            handleDebounce(opportunityStore);
          }}
          getDisabled={() => !editable}
        />
        <CDIDDialog
          getVisible={() => localStore.isCDIDDialogOpen}
          onConfirm={() => localStore.setIsCDIDDialogOpen(false)}
          style={{}}
        />
      </Group>
    );
  }),
);
