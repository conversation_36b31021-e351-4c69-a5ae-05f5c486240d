import { observer, useLocalObservable } from 'mobx-react';
import { Group } from '../../../../lib/ui/atoms/Group';
import { withTheme } from 'react-native-paper';
import { CuratedOpportunityGroupProps } from '../CuratedOpportunity';
import { View } from 'react-native';
import { <PERSON><PERSON>, Text } from '../../../../lib';
import { OpportunityOwnerStore } from '../../../../stores/OpportunityOwnerStore';
import { OpportunityOwnerListStore } from '../../../../stores/OpportunityOwnerListStore';
import { EditOpportunityOwner } from '../EditOpportunityOwner';
import { OpportunityOwnerCard } from '../OpportunityOwnerCard';
import { OpportunityOwnerTypeAheadInput } from '../OpportunityOwnerTypeAheadInput';
import { runInAction } from 'mobx';
import { ViewRemovedOwnersDialog } from '../ViewRemovedOwnersDialog';
import { UserStore } from '../../../../stores';

interface OpportunityOwnersProps extends CuratedOpportunityGroupProps {
  opportunityOwnerStore: OpportunityOwnerStore;
  userStore: UserStore;
  ownerListStore: OpportunityOwnerListStore;
}

export interface OpportunityOwnersLocals {
  showSearch: boolean;
  toggleSearch: () => void;
  showInputs: boolean;
  toggleShowInputs: () => void;
  showDialog: boolean;
  setShowDialog: (value: boolean) => void;
}

export const OpportunityOwners = withTheme(
  observer(
    ({
      editable,
      label,
      opportunityStore,
      opportunityOwnerStore,
      ownerListStore,
      theme,
      userStore,
    }: OpportunityOwnersProps) => {
      const { fonts, colors, fontSizes } = theme;

      const localStore = useLocalObservable<OpportunityOwnersLocals>(() => ({
        showSearch: false,
        toggleSearch: () => {
          localStore.showSearch = !localStore.showSearch;
        },
        showInputs: false,
        toggleShowInputs: () => {
          localStore.showInputs = !localStore.showInputs;
        },
        showDialog: false,
        setShowDialog: (value: boolean) => {
          localStore.showDialog = value;
        },
      }));

      const initialOwner = ownerListStore.initialOwner;
      const currentOwner = ownerListStore.currentOwner;
      const previousOwners = ownerListStore.previousOwners;
      const shouldShowAddNew = !localStore.showSearch && !localStore.showInputs;
      const areThereRemovedOwners = ownerListStore.hasRemovedOwners;
      const areTherePreviousOwners = ownerListStore.hasPreviousOwners;
      return (
        <>
          <Group
            title="Opportunity Owners"
            style={{ zIndex: 2 }}
            contentStyle={{ gap: 32 }}
            description={label}
            noBorder={true}
          >
            {currentOwner ? (
              <View style={{ gap: 16 }}>
                <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                  <Text style={{ color: colors.secondaryTextColor }}>Opportunity Owner</Text>
                  {editable ? (
                    <Button
                      labelStyle={[
                        { color: colors.secondaryTextColor, textTransform: 'capitalize' },
                        fonts.mediumTitle,
                        fontSizes.xSmall,
                      ]}
                      style={{ height: 24 }}
                      type="secondary"
                      onPress={() => onCreateNewButtonPress(localStore, opportunityOwnerStore)}
                    >
                      {shouldShowAddNew ? 'Add New Owner' : 'Cancel'}
                    </Button>
                  ) : null}
                </View>
                <View>
                  {localStore.showSearch && editable ? (
                    <OpportunityOwnerTypeAheadInput
                      opportunityOwnersStore={opportunityOwnerStore}
                      locals={localStore}
                    />
                  ) : null}
                  {localStore.showInputs ? (
                    <EditOpportunityOwner
                      opportunityOwnersStore={opportunityOwnerStore}
                      opportunityStore={opportunityStore}
                      locals={localStore}
                      userStore={userStore}
                      opportunityOwnerListStore={ownerListStore}
                    />
                  ) : null}
                </View>
                <OpportunityOwnerCard
                  opportunityOwnerListStore={ownerListStore}
                  opportunityStore={opportunityStore}
                  theme={theme}
                  opportunityOwner={currentOwner}
                />
              </View>
            ) : null}
            {areTherePreviousOwners || areThereRemovedOwners ? (
              <View style={{ gap: 4, zIndex: -1 }}>
                <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                  <Text style={{ color: colors.secondaryTextColor }}>Previous Opportunity Owners</Text>
                  {editable && areThereRemovedOwners ? (
                    <Button
                      labelStyle={[
                        { color: colors.secondaryTextColor, textTransform: 'capitalize' },
                        fonts.mediumTitle,
                        fontSizes.xSmall,
                      ]}
                      style={{ height: 24 }}
                      type="secondary"
                      onPress={() => {
                        localStore.setShowDialog(true);
                      }}
                    >
                      View Removed
                    </Button>
                  ) : null}
                </View>
                <View style={{ gap: 16 }}>
                  {previousOwners.map((owner) => (
                    <OpportunityOwnerCard
                      opportunityOwnerListStore={ownerListStore}
                      opportunityStore={opportunityStore}
                      key={owner.id}
                      theme={theme}
                      opportunityOwner={owner}
                    />
                  ))}
                </View>
              </View>
            ) : null}
            <View style={{ gap: currentOwner ? 4 : 16, zIndex: -1 }}>
              {!currentOwner ? (
                <View style={{ gap: 16 }}>
                  <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                    <Text style={{ color: colors.secondaryTextColor }}>Original Submitter</Text>
                    {editable ? (
                      <Button
                        labelStyle={[
                          { color: colors.secondaryTextColor, textTransform: 'capitalize' },
                          fonts.mediumTitle,
                          fontSizes.xSmall,
                        ]}
                        style={{ height: 24 }}
                        type="secondary"
                        onPress={() => {
                          onCreateNewButtonPress(localStore, opportunityOwnerStore);
                        }}
                      >
                        {shouldShowAddNew ? 'Add New Owner' : 'Cancel'}
                      </Button>
                    ) : null}
                  </View>
                  <View>
                    {localStore.showSearch && editable ? (
                      <OpportunityOwnerTypeAheadInput
                        opportunityOwnersStore={opportunityOwnerStore}
                        locals={localStore}
                      />
                    ) : null}
                    {localStore.showInputs ? (
                      <EditOpportunityOwner
                        opportunityOwnersStore={opportunityOwnerStore}
                        opportunityStore={opportunityStore}
                        locals={localStore}
                        userStore={userStore}
                        opportunityOwnerListStore={ownerListStore}
                      />
                    ) : null}
                  </View>
                </View>
              ) : (
                <Text style={{ color: colors.secondaryTextColor }}>Original Submitter</Text>
              )}
              {initialOwner ? (
                <OpportunityOwnerCard
                  opportunityOwnerListStore={ownerListStore}
                  opportunityStore={opportunityStore}
                  theme={theme}
                  opportunityOwner={initialOwner}
                />
              ) : null}
            </View>
          </Group>
          <ViewRemovedOwnersDialog
            opportunityOwnerListStore={ownerListStore}
            opportunityStore={opportunityStore}
            onConfirm={() => {
              localStore.setShowDialog(false);
            }}
            getVisible={() => localStore.showDialog}
            onDismiss={() => localStore.setShowDialog(false)}
          />
        </>
      );
    },
  ),
);

const onCreateNewButtonPress = (localStore: OpportunityOwnersLocals, opportunityOwnersStore: OpportunityOwnerStore) => {
  localStore.toggleSearch();
  runInAction(() => {
    opportunityOwnersStore.keywordSearchValue = '';
    opportunityOwnersStore.clearAll();
  });
  if (localStore.showInputs) {
    localStore.toggleShowInputs();
  }
};
