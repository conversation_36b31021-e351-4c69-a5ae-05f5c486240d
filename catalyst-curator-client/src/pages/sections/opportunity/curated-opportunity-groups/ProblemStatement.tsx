import { observer } from 'mobx-react';
import { withTheme } from 'react-native-paper';
import { Group } from '../../../../lib/ui/atoms/Group';
import { Switch } from '../../../../lib/ui/atoms/Switch';
import { CuratedOpportunityGroupProps, LabeledTextInputProps } from '../CuratedOpportunity';
import { CollapsibleView } from '../../../../lib/ui/atoms/CollapsibleView';
import { Text } from '../../../../lib/ui/atoms/Text';

interface ProblemStatementProps extends CuratedOpportunityGroupProps {
  getLabeledTextInput: (props: LabeledTextInputProps) => JSX.Element;
  onSubmissionToggle: () => void;
}
export const ProblemStatement = withTheme(
  observer(
    ({ editable, theme, onSubmissionToggle, getLabeledTextInput, opportunityStore, label }: ProblemStatementProps) => {
      const {
        styles: { margins, components },
        fontSizes,
        fonts,
        colors,
      } = theme;

      return (
        <Group
          title="Problem Statement"
          description={label}
          additionalDescriptionContent={
            <Switch
              style={[margins.TopML]}
              label={'Toggle original submission'}
              getOn={() => false}
              onOff={() => {}}
              onOn={() => onSubmissionToggle()}
              textStyle={[fontSizes.small, fonts.medium]}
            />
          }
        >
          {getLabeledTextInput({
            opportunityStore,
            fieldName: 'title',
            labelText: 'Proposed Problem Title',
            textInputProps: { multiline: false, maxLength: 255 },
            style: [components.rowStyle],
            editable,
          })}
          {getLabeledTextInput({
            opportunityStore,
            fieldName: 'statement',
            labelText: 'Problem Statement',
            textInputProps: { multiline: false },
            style: [components.rowStyle],
            editable,
          })}
          {getLabeledTextInput({
            opportunityStore,
            fieldName: 'context',
            labelText: 'Problem Context (impact, costs, constraints)',
            textInputProps: { multiline: true },
            style: [components.rowStyle],
            editable,
          })}
          {getLabeledTextInput({
            opportunityStore,
            fieldName: 'benefits',
            labelText: 'Benefits',
            textInputProps: { multiline: true },
            style: [components.rowStyle],
            editable,
          })}
          {getLabeledTextInput({
            opportunityStore,
            fieldName: 'solutionConcepts',
            labelText: 'Solution Concepts',
            textInputProps: { multiline: true },
            style: [components.rowStyle],
            editable,
          })}
          {
            // show in readonly mode if it's not empty
            !editable &&
              opportunityStore.getValue('additionalNotes') &&
              getLabeledTextInput({
                opportunityStore,
                fieldName: 'additionalNotes',
                labelText: 'Additional Notes About Problem',
                textInputProps: { multiline: true },
                style: [components.rowStyle],
                editable,
              })
          }
          {
            // show in edit mode, expanded if data present
            editable && (
              <CollapsibleView
                header={
                  <Text style={[fontSizes.mediumSmall, fonts.medium, { color: colors.primary }]}>ADDITIONAL NOTES</Text>
                }
                contentStyle={[margins.VerticalM]}
                defaultOpen={!!opportunityStore.getValue('additionalNotes')}
              >
                {getLabeledTextInput({
                  opportunityStore,
                  fieldName: 'additionalNotes',
                  labelText: 'Additional Notes About Problem',
                  textInputProps: { multiline: true, placeholder: 'Additional notes about the problem' },
                  style: [components.rowStyle],
                  editable,
                })}
              </CollapsibleView>
            )
          }
        </Group>
      );
    },
  ),
);
