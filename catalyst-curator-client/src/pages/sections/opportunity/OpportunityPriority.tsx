import { observer } from 'mobx-react';
import React, { Component } from 'react';
import { StyleProp, ViewStyle } from 'react-native';
import { withTheme } from 'react-native-paper';
import { Priority } from '../../../lib/Priority';
import { Checkbox } from '../../../lib/ui/atoms/Checkbox';
import { PriorityMenu } from '../../../lib/ui/organisms/PriorityMenu';
import OpportunityStore from '../../../stores/OpportunityStore';
interface OpportunityPriorityProps {
  getEditable?: () => boolean;
  style?: StyleProp<ViewStyle>;
  opportunityStore: OpportunityStore;
  onPropertyChanged: (name: string, value: string | number) => void;
  theme: ReactNativePaper.ThemeProp;
}

@observer
class OpportunityPriority extends Component<OpportunityPriorityProps> {
  render() {
    const { getEditable, style, theme, opportunityStore, onPropertyChanged } = this.props;
    const { fonts } = theme;
    const priority = opportunityStore.opportunity?.priority || Priority.NONE;
    return (
      <PriorityMenu
        labelText="Priority"
        value={priority}
        onValueSelected={(priority) => onPropertyChanged('priority', priority)}
        getEditable={getEditable}
      />
    );
  }
}

export default withTheme(OpportunityPriority);
