import { observer, useLocalObservable } from 'mobx-react';
import React, { useRef } from 'react';
import { StyleProp, TextStyle, View, ViewStyle } from 'react-native';
import { withTheme } from 'react-native-paper';
import { Opportunity, UpdateOperator } from '../../../services/codegen/types';
import { OpportunityStore } from '../../../stores';
import { Vr } from '../../../lib/ui/atoms/Vr';
import { RelatedOpportunityInput } from './RelatedOpportunityInput';
import { RelatedOpportunitySelectionType } from '../../../constants/RelatedOpportunity';
import { observable, set } from 'mobx';
import OpportunitySearchResultStore from '../../../stores/OpportunitySearchResultStore';
import { RelatedOpportunitiesLayout } from './RelatedOpportunitiesLayout';

/*
  Overview
  Types of Opportunity Relationships

*/

interface RelatedOpportunitiesProps {
  style?: StyleProp<ViewStyle>;
  titleStyle?: StyleProp<TextStyle>;
  getOpportunityStore: () => OpportunityStore;
  onUpdateOpportunity: (
    operator: UpdateOperator,
    opportunity: Partial<Opportunity>,
    type: RelatedOpportunitySelectionType,
  ) => Promise<void>;
  onPressOpportunity: (id: string) => void;
  getEditable?: () => boolean;
  theme: ReactNativePaper.ThemeProp;
}

interface Locals {
  selectedOpportunityStore?: OpportunitySearchResultStore;
  setSelectedOpportunityStore: (selectedOpportunityStore?: OpportunitySearchResultStore) => void;
}

export const RelatedOpportunities = withTheme(
  observer(
    ({
      getOpportunityStore,
      onUpdateOpportunity,
      onPressOpportunity,
      getEditable = () => true,
      style,
      titleStyle,
      theme,
    }: RelatedOpportunitiesProps) => {
      const localStore = useLocalObservable<Locals>(() => ({
        selectedOpportunityStore: undefined,
        setSelectedOpportunityStore(selectedOpportunityStore?: OpportunitySearchResultStore) {
          this.selectedOpportunityStore = selectedOpportunityStore;
        },
      }));
      const {
        styles: { margins, defaultValues },
      } = theme;
      const titleSearchStore = getOpportunityStore().getOpportunitySearchStore('opp_title');
      return (
        <View style={[{ zIndex: 10, flexDirection: 'row', alignItems: 'flex-start' }, style]}>
          {getEditable() && (
            <>
              <View style={[{ width: defaultValues.defaultComponentWidth }]}>
                <RelatedOpportunityInput
                  {...{
                    getOpportunityStore,
                    onUpdateOpportunity,
                    titleStyle,
                    titleSearchStore,
                    onPressOpportunity,
                    selectedOpportunityStore: localStore.selectedOpportunityStore,
                    onSelectOpportunity: (selectedOpportunityStore) =>
                      localStore.setSelectedOpportunityStore(selectedOpportunityStore),
                  }}
                />
              </View>
              <Vr style={[margins.HorizontalL, { alignSelf: 'stretch' }]} />
            </>
          )}
          <RelatedOpportunitiesLayout
            {...{ getOpportunityStore, onUpdateOpportunity, onPressOpportunity, getEditable, titleStyle, theme }}
          />
        </View>
      );
    },
  ),
);

/*componentWillUnmount() {
    const { getOpportunityStore } = this.props;
    getOpportunityStore().clearAll();
  }*/
