import React from 'react';
import { StyleProp, View, ViewStyle } from 'react-native';
import { withTheme } from 'react-native-paper';
import { LabeledTextInput, TextInputProps } from '../../../lib';
import { Group } from '../../../lib/ui/atoms/Group';
import { Switch } from '../../../lib/ui/atoms/Switch';
import OpportunityStore from '../../../stores/OpportunityStore';
import { SectionStyle } from '../../CurationPage';

interface SubmittedOpportunityProps {
  style?: StyleProp<ViewStyle>;
  opportunityStore: OpportunityStore;
  theme: ReactNativePaper.ThemeProp;
  sectionStyle: SectionStyle;
  onCuratedToggle: () => void;
}

export const SubmittedOpportunity = withTheme((props: SubmittedOpportunityProps) => {
  const { opportunityStore, style, onCuratedToggle, theme } = props;
  const { styles, fontSizes, fonts, colors } = theme;
  const { components, margins, paddings } = styles;
  const { editable } = opportunityStore;
  return (
    <View style={[margins.BottomML, { flex: 1 }]}>
      <Group
        title="Problem Statement"
        description={''}
        additionalDescriptionContent={
          <Switch
            style={[margins.TopML]}
            label={'Toggle original submission'}
            getOn={() => true}
            onOff={() => onCuratedToggle()}
            onOn={() => {}}
            textStyle={[fontSizes.small, fonts.medium]}
          />
        }
      >
        {getLabeledTextInput({
          opportunityStore,
          fieldName: 'title',
          labelText: 'Proposed Problem Title',
          textInputProps: { multiline: false },
          style: [components.rowStyle],
        })}
        {getLabeledTextInput({
          opportunityStore,
          fieldName: 'statement',
          labelText: 'Problem Statement',
          textInputProps: { multiline: false },
          style: [components.rowStyle],
        })}
        {getLabeledTextInput({
          opportunityStore,
          fieldName: 'context',
          labelText: 'Problem Context (impact, costs, constraints)',
          textInputProps: { multiline: true },
          style: [components.rowStyle],
        })}
        {getLabeledTextInput({
          opportunityStore,
          fieldName: 'benefits',
          labelText: 'Benefits',
          textInputProps: { multiline: true },
          style: [components.rowStyle],
        })}
        {getLabeledTextInput({
          opportunityStore,
          fieldName: 'solutionConcepts',
          labelText: 'Solution Concepts',
          textInputProps: { multiline: true },
          style: [components.rowStyle],
        })}
      </Group>
    </View>
  );
});

const getLabeledTextInput = (params: {
  opportunityStore: OpportunityStore;
  fieldName: string;
  labelText: string;
  style?: StyleProp<ViewStyle>;
  textInputProps: Partial<TextInputProps>;
}) => {
  const { opportunityStore, fieldName, labelText, style, textInputProps } = params;
  return (
    <LabeledTextInput
      style={style}
      textInputProps={{
        multiline: true,
        spellcheck: false,
        getValue: () => opportunityStore.getSubmissionValue(fieldName),
        setValue: () => {},
        editable: false,
        ...textInputProps,
      }}
      labelText={labelText}
    />
  );
};
