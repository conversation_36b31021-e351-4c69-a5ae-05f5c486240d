import { Linking, View } from 'react-native';
import { withTheme } from 'react-native-paper';

import { Text } from '../../../lib';
import { IconButton } from '../../../lib/ui/atoms/IconButton';
import { ExistingSolution } from '../../../services/codegen/types';
import { OpportunityStore } from '../../../stores';
import { AddExistingSolution } from './AddExistingSolution';

interface ExistingSolutionItemProps {
  solution: ExistingSolution;
  isEditing: boolean;
  editable: boolean;
  opportunityStore: OpportunityStore;
  onEdit: (solutionId: string) => void;
  onCancelEdit: () => void;
  theme: ReactNativePaper.ThemeProp;
}

export const ExistingSolutionItem = withTheme(
  ({ solution, isEditing, editable, opportunityStore, onEdit, onCancelEdit, theme }: ExistingSolutionItemProps) => {
    const { colors, fonts } = theme;

    if (isEditing) {
      return (
        <AddExistingSolution
          opportunityStore={opportunityStore}
          defaultExistingSolution={solution}
          setIsOpen={onCancelEdit}
        />
      );
    }

    return (
      <View
        style={{
          backgroundColor: 'white',
          padding: 12,
          gap: 12,
          borderRadius: 4,
          borderWidth: 1,
          borderColor: colors.border,
        }}
      >
        <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
          <View style={{ flexDirection: 'row', gap: 8 }}>
            <View
              style={{
                backgroundColor: solution.needsModification ? colors.negativeColor : '#258750',
                padding: 4,
                borderRadius: 4,
              }}
            >
              <Text style={{ color: 'white' }}>{solution.needsModification ? 'Needs Modification' : 'Ready'}</Text>
            </View>
            <Text style={fonts.medium}>{solution.title}</Text>
          </View>
          {editable && (
            <IconButton icon="pencil" color={colors.secondaryTextColor} onPress={() => onEdit(solution.id)} />
          )}
        </View>
        {solution.organization && <Text>Organization of Origin: {solution.organization}</Text>}
        <Text
          style={{ color: 'blue', textDecorationLine: 'underline' }}
          onPress={() => Linking.openURL(solution.source)}
        >
          {solution.source}
        </Text>
        <Text style={{ color: colors.secondaryTextColor }}>
          {solution.needsModification
            ? 'Solution needs modification in order to be fielded.'
            : 'Solution is ready for fielding'}
        </Text>
      </View>
    );
  },
);
