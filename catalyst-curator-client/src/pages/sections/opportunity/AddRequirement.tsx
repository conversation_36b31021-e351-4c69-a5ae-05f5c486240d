import { observer, useLocalObservable } from 'mobx-react';
import { View } from 'react-native';
import { withTheme } from 'react-native-paper';
import { Button, Label, Text, TextInput } from '../../../lib';
import { OpportunityStore } from '../../../stores';
import { Requirement } from '../../../services/codegen/types';

type AddRequirementProps = {
  opportunityStore: OpportunityStore;
  theme: ReactNativePaper.ThemeProp;
  defaultRequirement?: Requirement;
  setIsOpen: (value: boolean) => void;
};
export const AddRequirement = withTheme(
  observer(({ opportunityStore, theme, defaultRequirement, setIsOpen }: AddRequirementProps) => {
    const {
      colors,
      fonts,
      styles: { components },
    } = theme;

    const localStore = useLocalObservable(() => ({
      source: defaultRequirement?.source || '',
      setSource(value: string) {
        this.source = value;
      },

      title: defaultRequirement?.title || '',
      setTitle(value: string) {
        this.title = value;
      },
      poc: defaultRequirement?.poc || '',
      setPoc(value: string) {
        this.poc = value;
      },
    }));

    function isValid() {
      return localStore.source !== '' && localStore.poc !== '';
    }

    async function handleSave() {
      if (!isValid()) return;
      if (defaultRequirement) {
        await opportunityStore.updateRequirement(defaultRequirement.id, {
          source: localStore.source,
          title: localStore.title,
          poc: localStore.poc,
        });
      } else {
        await opportunityStore.addRequirement({
          source: localStore.source,
          title: localStore.title,
          poc: localStore.poc,
        });
      }
      setIsOpen(false);
    }

    async function handleDelete() {
      if (defaultRequirement) {
        await opportunityStore.deleteRequirement(defaultRequirement.id);
      }
      localStore.setSource('');
      localStore.setTitle('');
      localStore.setPoc('');
      setIsOpen(false);
    }

    return (
      <View
        style={{
          gap: 12,
          backgroundColor: 'white',
          padding: 12,
          borderWidth: 1,
          borderColor: colors.border,
          borderRadius: 4,
        }}
      >
        <View>
          <Label>Source (URL)</Label>
          <TextInput
            getValue={() => localStore.source}
            setValue={(source) => localStore.setSource(source)}
            containerStyle={{ maxWidth: undefined }}
          />
        </View>
        <View>
          <Label>Title (Optional)</Label>
          <TextInput getValue={() => localStore.title} setValue={(title) => localStore.setTitle(title)} />
        </View>
        <View>
          <Label>POC (URL)</Label>
          <TextInput getValue={() => localStore.poc} setValue={(poc) => localStore.setPoc(poc)} />
        </View>
        <View style={{ flexDirection: 'row', alignSelf: 'flex-end', gap: 12 }}>
          <Button
            labelStyle={[{ textTransform: 'capitalize', color: colors.secondaryTextColor }, fonts.mediumTitle]}
            type="secondary"
            onPress={handleDelete}
          >
            Delete
          </Button>
          <Button
            labelStyle={[{ textTransform: 'capitalize' }, fonts.mediumTitle]}
            type="primary"
            onPress={handleSave}
            getDisabled={() => !isValid()}
          >
            Save
          </Button>
        </View>
      </View>
    );
  }),
);
