import { withTheme } from 'react-native-paper';
import { ContentDialog } from '../../../lib/ui/molecules/ContentDialog';
import { Button, DialogProps, Message, Title } from '../../../lib';
import { Linking, ScrollView, View } from 'react-native';
import { Text } from '../../../lib/ui/atoms/Text';

interface OwnerConfirmRemoveDialogProps extends DialogProps {
  onConfirm: () => void;
  theme: ReactNativePaper.ThemeProp;
}
export const OwnerConfirmRemoveDialog = withTheme(({ theme, onConfirm, getVisible }: OwnerConfirmRemoveDialogProps) => {
  const {
    styles: { margins, paddings, components, fontSizes, fonts },
    colors,
  } = theme;

  return (
    <ContentDialog
      {...{ onConfirm, theme }}
      noTitleBar={true}
      style={{ borderRadius: 16 }}
      contentStyle={[{ borderRadius: 16, width: 510, height: 290, backgroundColor: theme.colors.background }]}
      getVisible={getVisible}
      showClose={true}
    >
      <Title style={[{ alignSelf: 'flex-start' }, margins.TopL]}>Remove Owner</Title>
      <View style={[margins.TopML, { gap: 40 }]}>
        <Text style={components.messageInfoStyle}>
          This Opportunity Owners will be removed from the Owner History of this Opportunity. You can restore any user
          card by clicking View Removed at any time.
        </Text>
        <View style={{ flexDirection: 'row', justifyContent: 'flex-end', gap: 8 }}>
          <Button
            type="primary"
            labelStyle={(fonts.mediumTitle, fontSizes.xSmall, { textTransform: 'capitalize', color: colors.text })}
            style={{ backgroundColor: colors.background }}
          >
            Cancel
          </Button>
          <Button
            type="primary"
            labelStyle={{ textTransform: 'capitalize' }}
            style={(fonts.mediumTitle, fontSizes.xSmall, { backgroundColor: colors.negativeColor })}
            onPress={onConfirm}
          >
            Remove
          </Button>
        </View>
      </View>
    </ContentDialog>
  );
});
