import React from 'react';
import { ContentDialog } from '../../../lib/ui/molecules/ContentDialog';
import { ScrollView, View } from 'react-native';
import { withTheme } from 'react-native-paper';
import { observer } from 'mobx-react';
import { RelatedOpportunitiesLayout } from './RelatedOpportunitiesLayout';
import { OpportunityStore } from '../../../stores';
import { Message } from '../../../lib';
import { Wait } from '../../../lib/ui/molecules/Wait';
import { Opportunity, UpdateOperator } from '../../../services/codegen/types';
import { RelatedOpportunitySelectionType } from '../../../constants/RelatedOpportunity';
import { Router } from '../../../platform/Router';
import { MainStackParamList } from '../../../routing/screens';
interface RelationshipsDialogProps {
  getOpportunityStore: () => OpportunityStore | undefined; //observable
  onPressOpportunity: (id: string) => void;
  onClose: () => void;
  theme: ReactNativePaper.ThemeProp;
}

export const RelationshipsDialog = withTheme(
  observer(({ getOpportunityStore, onPressOpportunity, onClose, theme }: RelationshipsDialogProps) => {
    const {
      styles: { margins, paddings, components, fontSizes },
      colors,
    } = theme;
    const opportunityStore = getOpportunityStore();
    if (!opportunityStore) return null;
    return (
      <ContentDialog
        getVisible={() => !!opportunityStore}
        onConfirm={onClose}
        title="Relationships"
        contentStyle={[components.relationshipsDialogStyle, { alignItems: 'flex-start' }]}
      >
        <Wait until={() => opportunityStore.initialized}>
          <ScrollView
            style={[{ flex: 1 }, margins.TopML]}
            contentContainerStyle={[{ flex: 1 }]}
            keyboardShouldPersistTaps="handled"
            contentInsetAdjustmentBehavior="always"
          >
            <View style={[{ flex: 1 }, paddings.HorizontalML]}>
              <RelatedOpportunitiesLayout
                {...{
                  getOpportunityStore: () => opportunityStore,
                  onUpdateOpportunity: handleOnRelatedOpportunityChanged,
                  onPressOpportunity,
                  getEditable: () => false,
                }}
              />
            </View>
          </ScrollView>
        </Wait>
      </ContentDialog>
    );
  }),
);

// place holder for possible allowing editing from the dialog
const handleOnRelatedOpportunityChanged = async (
  operator: UpdateOperator,
  opportunity: Partial<Opportunity>,
  type: RelatedOpportunitySelectionType,
) => {};
