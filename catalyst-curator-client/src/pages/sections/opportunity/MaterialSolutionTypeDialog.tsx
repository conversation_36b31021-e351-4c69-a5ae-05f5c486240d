import { withTheme } from 'react-native-paper';
import { ContentDialog } from '../../../lib/ui/molecules/ContentDialog';
import { DialogProps, Title } from '../../../lib';
import { ScrollView } from 'react-native';
import { Text } from '../../../lib/ui/atoms/Text';

interface MaterialSolutionTypeDialogProps extends DialogProps {
  onConfirm: () => void;
  theme: ReactNativePaper.ThemeProp;
}

export const MaterialSolutionTypeDialog = withTheme(
  ({ theme, onConfirm, getVisible }: MaterialSolutionTypeDialogProps) => {
    const {
      styles: { margins, components },
    } = theme;

    return (
      <ContentDialog
        {...{ onConfirm, theme }}
        noTitleBar={true}
        style={{ borderRadius: 16 }}
        contentStyle={[{ borderRadius: 16, width: 660, height: 200, backgroundColor: theme.colors.background }]}
        getVisible={getVisible}
        showClose={true}
      >
        <Title style={[{ alignSelf: 'flex-start' }, margins.TopL]}>Materiel Solution Type</Title>
        <ScrollView
          style={[margins.TopML]}
          contentContainerStyle={[{}]}
          keyboardShouldPersistTaps="handled"
          contentInsetAdjustmentBehavior="always"
        >
          <Text style={components.messageInfoStyle}>Placeholder</Text>
        </ScrollView>
      </ContentDialog>
    );
  },
);
