import { withTheme } from 'react-native-paper';
import { ContentDialog } from '../../../lib/ui/molecules/ContentDialog';
import { Button, DialogProps, LabeledTextInput, Title, urlValidator } from '../../../lib';
import { View } from 'react-native';
import { Text } from '../../../lib/ui/atoms/Text';
import { LinkLocals } from './Links';
import { InfoCard } from '../../../lib/ui/molecules/InfoCard';
import { UserStore } from '../../../stores';
import { observer } from 'mobx-react';

interface LinkDialogProps extends DialogProps {
  onConfirm: () => void;
  onDelete?: () => void;
  theme: ReactNativePaper.ThemeProp;
  locals: LinkLocals;
  userStore: UserStore;
}
export const LinkDialog = withTheme(
  observer(({ theme, onConfirm, onDelete, getVisible, locals, userStore, onDismiss }: LinkDialogProps) => {
    const {
      styles: { margins },
      colors,
    } = theme;

    return (
      <ContentDialog
        {...{ onConfirm, theme }}
        noTitleBar={true}
        onDismiss={onDismiss}
        style={{ borderRadius: 16 }}
        contentStyle={[{ borderRadius: 8, width: 840, height: 460, backgroundColor: colors.paper, paddingTop: 0 }]}
        getVisible={getVisible}
        showClose={false}
      >
        <Title style={[{ alignSelf: 'flex-start' }, margins.TopL]}>
          {locals.editingLinkId ? 'Edit Link' : 'Add Link'}
        </Title>
        <Text style={{ alignSelf: 'flex-start' }}>Do not include CUI data. Fill in the details.</Text>
        <View style={[margins.TopML, { flexDirection: 'row', flex: 1, width: '100%' }]}>
          <View style={{ flex: 1, gap: 20 }}>
            <LabeledTextInput
              labelText="Link URL"
              textInputProps={{
                getValue: () => locals.url,
                setValue: (value) => locals.setUrl(value),
              }}
              getHelperTextProps={() => ({
                type: 'error',
                children: locals.urlErrorMessage,
              })}
            />
            <View>
              <LabeledTextInput
                labelText="Link Name"
                textInputProps={{
                  getValue: () => locals.name,
                  setValue: (value) => locals.setName(value),
                }}
              />
            </View>
            <View>
              <LabeledTextInput
                labelText="Link Notes"
                textInputProps={{
                  getValue: () => locals.notes,
                  setValue: (value) => locals.setNotes(value),
                }}
              />
            </View>
          </View>
          <View style={{ marginLeft: 16, flex: 1 }}>
            <InfoCard
              title={locals.name || locals.url || 'Link Preview'}
              subtitle={locals.url}
              createdAt={new Date().toISOString()}
              createdBy={userStore.user}
              notes={locals.notes || undefined}
              id="edit"
            />
          </View>
        </View>
        <View
          style={{
            width: '100%',
            flexDirection: 'row',
            gap: 8,
          }}
        >
          {locals.editingLinkId && onDelete ? (
            <Button
              type="secondary"
              labelStyle={{ textTransform: 'capitalize', color: colors.negativeColor }}
              style={{ borderWidth: 0, flex: 1 }}
              contentStyle={{ justifyContent: 'flex-end' }}
              onPress={onDelete}
            >
              Delete
            </Button>
          ) : (
            <View style={{ flex: 1 }} />
          )}
          <View style={{ flexDirection: 'row', gap: 8, flex: 1, justifyContent: 'flex-end' }}>
            <Button
              type="secondary"
              labelStyle={{ textTransform: 'capitalize', color: colors.secondaryTextColor }}
              style={{ borderWidth: 0 }}
              onPress={() => locals.setIsDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              type="primary"
              labelStyle={{ textTransform: 'capitalize' }}
              onPress={onConfirm}
              getDisabled={() => {
                if (!locals.url) {
                  locals.setUrlErrorMessage('URL is required');
                  return true;
                }
                if (!urlValidator(locals.url)) {
                  locals.setUrlErrorMessage('Invalid URL');
                  return true;
                }
                locals.setUrlErrorMessage('');
                return false;
              }}
            >
              Confirm
            </Button>
          </View>
        </View>
      </ContentDialog>
    );
  }),
);
