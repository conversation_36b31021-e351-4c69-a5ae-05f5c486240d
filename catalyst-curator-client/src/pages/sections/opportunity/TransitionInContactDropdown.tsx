import { observer, useLocalObservable } from 'mobx-react';
import { withTheme } from 'react-native-paper';
import { Pressable, View } from 'react-native';
import { Target } from '@nandorojo/anchor';
import { DependentDropdown, MenuGroup, MenuItem } from '../../../lib';
import { OpportunityStore } from '../../../stores';

interface TransitionInContactDropdownProps {
  editable: boolean;
  theme: ReactNativePaper.ThemeProp;
  opportunityStore: OpportunityStore;
  handleDebounce: (opportunityStore: OpportunityStore) => void;
}
export const TransitionInContactItems: MenuGroup[] = [
  { item: { label: 'Lethality', value: 'Lethality' } },
  {
    item: { label: 'Protection and Survivability', value: 'Protection and Survivability' },
    children: [
      {
        item: {
          label: 'Protection and Survivability - Soldier Common CsUAS',
          value: 'Protection and Survivability - Soldier Common CsUAS',
        },
      },
    ],
  },
  {
    item: { label: 'Situational Awareness', value: 'Situational Awareness' },
    children: [
      {
        item: {
          label: 'Situational Awareness - Tactical Assault Kit (TAK)',
          value: 'Situational Awareness - Tactical Assault Kit (TAK)',
        },
      },
    ],
  },
  { item: { label: 'Mobility', value: 'Mobility' } },
  { item: { label: 'Human Performance', value: 'Human Performance' } },
  { item: { label: 'Soldier and Small Unit Power (S2UP)', value: 'Soldier and Small Unit Power (S2UP)' } },
  { item: { label: 'Communications', value: 'Communications' } },
  {
    item: { label: 'Robotic and Autonomous Systems', value: 'Robotic and Autonomous Systems' },
    children: [
      {
        item: {
          label: 'Accelerated Robotic and Autonomous Systems Common Control',
          value: 'Accelerated Robotic and Autonomous Systems Common Control',
        },
      },
    ],
  },
  { item: { label: 'Sustainment', value: 'Sustainment' } },
];
export const TransitionInContactDropdown = withTheme(
  observer(({ editable, handleDebounce, opportunityStore, theme }: TransitionInContactDropdownProps) => {
    const {
      styles: { components },
    } = theme;

    return (
      <View style={[components.rowStyle]}>
        <Target name={'TransitionInContact'}>
          <DependentDropdown
            defaultValues={[
              {
                fieldName: 'transitionInContactLineOfEffort',
                label: 'TiC LOE',
                value: {
                  label: opportunityStore.opportunity?.transitionInContactLineOfEffort || 'Unassigned',
                  value: 'unassigned',
                },
              },
              {
                label: undefined,
                value: undefined,
                fieldName: 'transitionInContactLineOfEffort',
              },
            ]}
            getMenuGroups={() => {
              return TransitionInContactItems;
            }}
            onItemSelected={(item, fieldName) => {
              opportunityStore.setValue(fieldName, item?.value);
              handleDebounce(opportunityStore);
            }}
            labeledDropdownMenuProps={{
              dropdownMenuProps: {
                getEditable: () => editable,
                getMenuItems: () => TransitionInContactItems.map((group) => group.item),
                onItemSelected: (item: MenuItem) => {
                  opportunityStore.setValue('transitionInContactLineOfEffor', item.value);
                  handleDebounce(opportunityStore);
                },
              },
            }}
          />
        </Target>
      </View>
    );
  }),
);
