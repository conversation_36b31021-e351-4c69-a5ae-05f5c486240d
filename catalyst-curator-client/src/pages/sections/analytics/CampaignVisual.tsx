import { observer, useLocalObservable } from 'mobx-react';
import { withTheme } from 'react-native-paper';
import { ChartCard } from './ChartCard';
import { VerticalBarChart } from '../../../appComponents/web/VerticalBarChart.web';
import { OpportunityReportsStore } from '../../../stores';
import { View } from 'react-native';
import { Text } from '../../../lib/ui/atoms/Text';
import { Button } from '../../../lib';
import { DataVisualTooltipText } from '../../../constants/DataVisualTooltipText';

type CampaignVisualProps = {
  theme: ReactNativePaper.ThemeProp;
  opportunityReportsStore: OpportunityReportsStore;
};

export const CampaignVisual = withTheme(
  observer(({ opportunityReportsStore, theme }: CampaignVisualProps) => {
    const {
      styles: { components, margins, paddings, fonts },
    } = theme;

    const localStore = useLocalObservable(() => ({
      visible: false,
      setVisible(visible: boolean) {
        this.visible = visible;
      },
    }));
    const numberOfEvents = Number(getSubmissions(opportunityReportsStore)?.length);

    const areThereEvents = getSubmissions(opportunityReportsStore)?.length;
    const eventFilterInfo = opportunityReportsStore.campaignListFilter;
    return (
      <ChartCard
        titleText="Events"
        style={{ flex: 2 }}
        menuType={'dots'}
        eventFilterInfo={eventFilterInfo}
        opportunityReportsStore={opportunityReportsStore}
        locals={localStore}
        tooltipText={DataVisualTooltipText.campaign}
      >
        {eventFilterInfo.info.length > 0 && numberOfEvents <= 5 && numberOfEvents > 0 ? (
          <VerticalBarChart
            data={getSubmissions(opportunityReportsStore)}
            barColors={['#225091']}
            dataKey="campaignCount"
            yAxisDataKey="campaign"
          />
        ) : !areThereEvents ? (
          <View
            style={{
              height: '100%',
              alignItems: 'center',
              flexShrink: 1,
              flexDirection: 'column',
              justifyContent: 'center',
              gap: 8,
            }}
          >
            <Text>No event data available</Text>
          </View>
        ) : numberOfEvents > 5 ? (
          <View
            style={{
              height: '100%',
              alignItems: 'center',
              flexShrink: 1,
              flexDirection: 'column',
              justifyContent: 'center',
              gap: 8,
            }}
          >
            <Text>Pick up to 5 events to display from the menu</Text>
            <Button
              onPress={() => localStore.setVisible(true)}
              labelStyle={[{ color: '#676D79', textTransform: 'capitalize' }]}
              type="secondary"
            >
              Add Events
            </Button>
          </View>
        ) : null}
      </ChartCard>
    );
  }),
);

const parseData = (data: OpportunityCampaignReportResult | undefined) => {
  const newData = data?.totals.map((total) => {
    const tenantBreakdown = data.tenantCampaignResults
      .filter((tenant) => {
        return tenant.campaign === total.campaign;
      })
      .map((tenant) => {
        return {
          name: tenant.tenant,
          value: tenant.campaignCount,
        };
      });

    return {
      ...total,
      tenantBreakdown,
    };
  });

  return newData?.filter((item) => item.campaign !== 'None');
};

const getSubmissions = (reportsStore: OpportunityReportsStore) => {
  const formattedData = parseData(reportsStore.campaignReportData);
  return formattedData;
};
