import { get, observable } from 'mobx';
import { observer, useLocalObservable } from 'mobx-react';
import { ReactNode, useRef } from 'react';
import { ScrollView, StyleProp, View, ViewStyle } from 'react-native';
import { withTheme } from 'react-native-paper';
import { OpportunityListStore, OpportunityReportsStore, UserStore } from '../../../stores';
import { PopupMenu } from '../../../lib/ui/atoms/PopupMenu';
import { Button, Hr, Label, SearchInput } from '../../../lib';
import { Checkbox } from '../../../lib/ui/atoms/Checkbox';
import { Privilege } from '../../../services/codegen/types';
import { getPortfolios } from '../../../lib/Privileges';
import {} from '@expo/vector-icons/';
import { ResourcesStore } from '../../../stores/ResourcesStore';

interface PortfolioDropdownProps {
  opportunityListStore: OpportunityListStore;
  anchor?: ReactNode;
  style?: StyleProp<ViewStyle>;
  theme: ReactNativePaper.ThemeProp;
  userStore: UserStore;
  resourcesStore: ResourcesStore;
  opportunityReportsStore: OpportunityReportsStore;
}

interface Locals {
  visible: boolean;
  searchTerm: string;
  filteredPortfolios: Privilege[] | null | undefined;
  setVisible(value: boolean): void;
  setFilteredPortfolios(value: Privilege[] | null | undefined): void;
  setSearchTerm(value: string): void;
}

/*
  Note: we have to send the 'values' of enums when querying directly
*/

export const PortfolioDropdown = withTheme(
  observer(
    ({
      anchor,
      opportunityListStore,
      userStore,
      style,
      theme,
      resourcesStore,
      opportunityReportsStore,
    }: PortfolioDropdownProps) => {
      const {
        styles: { margins, paddings, fonts, fontSizes },
        colors,
      } = theme;

      const localStore = useLocalObservable(() => ({
        visible: false,
        filteredPortfolios: getPortfolios(userStore.user?.privilegeGroups).sort((a, b) => a.name.localeCompare(b.name)),
        searchTerm: '',
        setVisible(value: boolean) {
          this.visible = value;
        },
        setFilteredPortfolios(value: Privilege[]) {
          this.filteredPortfolios = value;
        },
        setSearchTerm(value: string) {
          this.searchTerm = value;
        },
      }));

      return (
        <PopupMenu
          anchor={anchor}
          visible={localStore.visible}
          onDismiss={() => localStore.setVisible(false)}
          onClickAnchor={() => localStore.setVisible(true)}
          contentStyle={{ paddingHorizontal: 0, paddingTop: 0, minWidth: 175 }}
          style={[{ marginBottom: 0 }, style]}
        >
          <View style={[paddings.HorizontalM]}>
            <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'baseline' }}>
              <Label textStyle={[margins.TopM, fonts.regularTitle]}>Select Portfolio</Label>
            </View>
            <SearchInput
              style={[margins.TopMS, margins.BottomM]}
              getValue={() => localStore.searchTerm}
              setValue={(v) => {
                handleSearch(v, localStore, getPortfolios(userStore.user?.privilegeGroups));
                localStore.setSearchTerm(v);
              }}
              placeholder="Find a portfolio"
            />
            <ScrollView style={[{ maxHeight: 400 }]}>
              <Checkbox
                label={'(Select All)'}
                getChecked={() => getIsAllSelected(resourcesStore, getPortfolios(userStore.user?.privilegeGroups))}
                onChecked={() => handleCheckAll(resourcesStore, getPortfolios(userStore.user?.privilegeGroups))}
                onUnchecked={() => handleUncheckAll(resourcesStore, getPortfolios(userStore.user?.privilegeGroups))}
              />
              <Hr style={[{ alignSelf: 'stretch' }, margins.VerticalS, margins.RightM]} />
              <View style={{ gap: 8 }}>
                {localStore.filteredPortfolios?.map((portfolio) => {
                  return (
                    <Checkbox
                      key={portfolio.id}
                      label={portfolio.name}
                      getChecked={() => getCheckedFields(resourcesStore, portfolio)}
                      onChecked={() => handleCheckedFields(resourcesStore, portfolio)}
                      onUnchecked={() => handleUncheckedFields(resourcesStore, portfolio)}
                    />
                  );
                })}
              </View>
            </ScrollView>
            <View style={[margins.TopM, { flexDirection: 'row', justifyContent: 'center' }]}>
              <Button
                style={[{ width: '100%' }]}
                type={'primary'}
                compact={true}
                onPress={() => {
                  applyFilters(opportunityListStore, localStore, opportunityReportsStore);
                  opportunityReportsStore.campaignListFilter.updateCampaignFilterList([], resourcesStore.scope);
                }}
              >
                {' '}
                {'Apply'}
              </Button>
            </View>
          </View>
        </PopupMenu>
      );
    },
  ),
);

const getIsAllSelected = (resourcesStore: ResourcesStore, portfolios: Privilege[]) => {
  const isAllChecked = portfolios.every((resource) => {
    return resourcesStore.doesResourceExist(resource.resourceId);
  });

  const isAllUnchecked = resourcesStore.resources?.every((resource) => {
    return !portfolios.some((portfolio) => portfolio.resourceId === resource.resourceId);
  });

  if (isAllChecked) return true;
  if (isAllUnchecked) return false;
  return 'indeterminate';
};

const handleCheckAll = (resourcesStore: ResourcesStore, portfolios: Privilege[]) => {
  portfolios.forEach((portfolio) => {
    if (!resourcesStore.doesResourceExist(portfolio.resourceId)) {
      resourcesStore.addResource(portfolio.resourceId, portfolio.resourceType);
    }
  });
};

const handleUncheckAll = (resourcesStore: ResourcesStore, portfolios: Privilege[]) => {
  portfolios.forEach((portfolio) => {
    resourcesStore.deleteResource(portfolio.resourceId);
  });
};

const getCheckedFields = (resourcesStore: ResourcesStore, tenant: Privilege) => {
  return resourcesStore.doesResourceExist(tenant.resourceId);
};

const handleCheckedFields = (resourcesStore: ResourcesStore, tenant: Privilege) => {
  resourcesStore.addResource(tenant.resourceId, tenant.resourceType);
};

const handleUncheckedFields = (resourcesStore: ResourcesStore, tenant: Privilege) => {
  resourcesStore.deleteResource(tenant.resourceId);
};

const applyFilters = (
  opportunityListStore: OpportunityListStore,
  localStore: Locals,
  opportunityReportsStore: OpportunityReportsStore,
) => {
  opportunityListStore.queryItems();
  opportunityReportsStore.getReports();
  localStore.setVisible(false);
};

const handleSearch = (searchTerm: string, localStore: Locals, tenants: Privilege[] | null | undefined) => {
  if (!searchTerm) {
    localStore.setFilteredPortfolios(tenants);
  } else {
    const filtered = tenants?.filter((item) => item.name.toLowerCase().includes(searchTerm.toLowerCase()));
    localStore.setFilteredPortfolios(filtered);
  }
};
