import { observer } from 'mobx-react';
import { withTheme } from 'react-native-paper';
import { PieChart } from '../../../appComponents/web/PieChart.web';
import { ChartCard } from './ChartCard';

export type PieChartData = {
  tenant: string;
  [key: string]: number | string;
};

type PieChartVisualProps = {
  data: OpportunityStatusReportResult[] | OpportunityPriorityReportResult[] | OpportunityWFFReportResult[] | undefined;
  theme: ReactNativePaper.ThemeProp;
  title: string;
  tooltipText: string;
};
const colors = ['#3268DC', '#5C7BE2', '#7B8EE8', '#96A2EE', '#AFB6F4', '#C7CBFA', '#DEE0FF'];

export const PieChartVisual = withTheme(
  observer(({ data, title, theme, tooltipText }: PieChartVisualProps) => {
    const pieChartData = parsePieChartData(data as unknown as PieChartData[] | undefined);
    return (
      <ChartCard titleText={title} style={{ flex: 1 }} tooltipText={tooltipText}>
        <PieChart
          data={pieChartData}
          colors={selectColors(colors, pieChartData.length)}
          defaultIndex={getDefaultIndex(pieChartData)}
          dataKey="total"
        />
      </ChartCard>
    );
  }),
);

function selectColors(colors: string[], n: number): string[] {
  if (n > colors.length) {
    throw new Error('Number of colors requested is more than the available colors');
  }

  if (n === 1) {
    return [colors[0]];
  }

  const result = [];
  const step = (colors.length - 1) / (n - 1);

  for (let i = 0; i < n; i++) {
    const index = Math.round(i * step);
    result.push(colors[index]);
  }

  return result;
}

const mapNames = {
  highPriority: 'High',
  mediumPriority: 'Medium',
  lowPriority: 'Low',
  noPriority: 'None',
  missionCommand: 'Mission Command',
  movementManeuver: 'Movement Maneuver',
  forceProtection: 'Force Protection',
} as Record<string, string>;

const parsePieChartData = (data: PieChartData[] | undefined) => {
  const total = data?.find((item) => item.tenant === 'Total');
  const allOther = data?.filter((item) => item.tenant !== 'Total');
  if (!total || !allOther?.length) return [];
  const keys = Object.keys(allOther[0]).filter((key) => key !== 'tenant');

  const formattedData = keys
    .map((key) => {
      const name = (mapNames[key.split('Count')[0]] as string) || key.split('Count')[0];
      return {
        name,
        tenantBreakdown: allOther.map((result) => {
          return {
            name: result.tenant,
            value: result[key] as number,
          };
        }),
        total: total[key] as number,
      };
    })
    .filter((item) => item.total !== 0);

  return formattedData;
};

function getDefaultIndex(pieChartData: ReturnType<typeof parsePieChartData>): number {
  if (pieChartData.length === 0 || pieChartData[0].total !== 0) return 0;
  const isWFF = pieChartData[0].name === 'Mission Command';
  const isPriority = pieChartData[0].name === 'High';
  const isStatus = pieChartData[0].name === 'Approved';

  if (isWFF) {
    //return highest
    return pieChartData.reduce((acc, cur, index) => (cur.total > pieChartData[acc].total ? index : acc), 0);
  }
  if (isPriority || isStatus) {
    //return next with data
    return pieChartData.findIndex((item) => item.total !== 0);
  }

  return 1;
}
