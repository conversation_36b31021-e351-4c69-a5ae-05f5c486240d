import { ReactNode } from 'react';
import { StyleProp, View, ViewStyle } from 'react-native';
import { withTheme } from 'react-native-paper';
import { Text } from '../../../lib/ui/atoms/Text';
import { Tooltip } from '../../../lib';

interface DataCardProps {
  style?: StyleProp<ViewStyle>;
  theme: ReactNativePaper.ThemeProp;
  data: { name: string; value: number; tooltipText: string }[];
}
export const DataCard = withTheme(({ theme, style, data }: DataCardProps) => {
  const {
    colors,
    styles: { components, margins, fonts, paddings, fontSizes },
  } = theme;

  return (
    <View style={[components.dataCardStyle, components.shadow, style, paddings.HorizontalML]}>
      {data.map((item, index) => {
        return (
          <View
            key={index}
            style={[
              { flex: 1, justifyContent: 'center', gap: 4 },
              index !== data.length - 1
                ? { borderBottomColor: colors.border, borderBottomWidth: 1 }
                : { borderBottomWidth: 0 },
            ]}
          >
            <Tooltip text={item.tooltipText} style={{ justifyContent: 'center' }} hoverEffect>
              <Text style={[fonts.regularTitle, { color: colors.textSecondary }]}>{item.name}</Text>
            </Tooltip>
            <Text style={[{ fontSize: 24 }, fonts.regularTitle]}>{item.value ? item.value : 0}</Text>
          </View>
        );
      })}
    </View>
  );
});
