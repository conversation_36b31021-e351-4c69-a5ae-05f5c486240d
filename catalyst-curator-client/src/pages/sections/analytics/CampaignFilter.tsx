import { observer } from 'mobx-react';
import { View } from 'react-native';
import { withTheme } from 'react-native-paper';
import { PopupMenu } from '../../../lib/ui/atoms/PopupMenu';
import { Icon } from '../../../lib/ui/atoms/Icon';
import { Button, Label, SearchInput } from '../../../lib';
import { Checkbox } from '../../../lib/ui/atoms/Checkbox';
import { FilterInfoStore, GroupLabelValue } from '../../../stores/FilterInfoStore';
import { OpportunityReportsStore } from '../../../stores';
import { SearchField } from '../../../services/codegen/types';

interface Locals {
  visible: boolean;
  setVisible: (visible: boolean) => void;
}

type CampaignFilterProps = {
  theme: ReactNativePaper.ThemeProp;
  opportunityReportsStore: OpportunityReportsStore;
  locals: Locals;
};

export const CampaignFilter = withTheme(
  observer(({ theme, opportunityReportsStore, locals }: CampaignFilterProps) => {
    const {
      styles: { margins, paddings, fonts },
      colors,
    } = theme;

    const filterInfo = opportunityReportsStore.campaignListFilter;

    return (
      <PopupMenu
        anchor={<Icon name="dots-vertical" />}
        visible={locals.visible}
        onDismiss={() => locals.setVisible(false)}
        onClickAnchor={() => locals.setVisible(true)}
        contentStyle={[{ minWidth: 175 }, paddings.RightM]}
      >
        <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'baseline' }}>
          <Label textStyle={[margins.TopM, fonts.regularTitle]}>Select Events</Label>
        </View>
        <SearchInput
          style={[margins.TopMS, margins.BottomM]}
          getValue={() => filterInfo.searchTerm}
          setValue={(v) => {
            filterInfo.searchTerm = v;
          }}
          placeholder="Find an event"
        />
        <View style={{ gap: 8 }}>
          {filterInfo.filteredInfo?.map((checkbox) => {
            const groupCheckbox = checkbox as GroupLabelValue;
            if (groupCheckbox.label === null) return null;
            return (
              <Checkbox
                key={groupCheckbox.label}
                label={groupCheckbox.label}
                // disable unchecked if 5 checkboxes are checked
                getDisabled={() =>
                  areFiveChecked(filterInfo, opportunityReportsStore, filterInfo.groupName, groupCheckbox.searchField)
                }
                getChecked={() => {
                  const searchGroup = opportunityReportsStore.searchGroups.getSearchGroupFor(filterInfo.groupName);
                  return searchGroup ? filterInfo.getSelectedGroups(searchGroup, groupCheckbox.searchField) : false;
                }}
                onChecked={() =>
                  handleChecked(opportunityReportsStore, filterInfo.groupName, groupCheckbox.searchField)
                }
                onUnchecked={() =>
                  handleUnchecked(opportunityReportsStore, filterInfo.groupName, groupCheckbox.searchField)
                }
              />
            );
          })}
        </View>
        <View style={[margins.TopM, { flexDirection: 'row', justifyContent: 'center' }]}>
          <Button
            type={'primary'}
            style={[{ width: '100%' }]}
            compact={true}
            onPress={() => applyFilters(opportunityReportsStore, locals)}
          >
            {'Apply'}
          </Button>
        </View>
      </PopupMenu>
    );
  }),
);

const applyFilters = (opportunityReportsStore: OpportunityReportsStore, localStore: Locals) => {
  opportunityReportsStore.getCampaignReport();
  localStore.setVisible(false);
};

const handleChecked = (
  opportunityReportsStore: OpportunityReportsStore,
  groupName: string,
  searchField: SearchField,
) => {
  const searchGroup = opportunityReportsStore.searchGroups.getSearchGroupFor(groupName);
  if (!searchGroup) {
    opportunityReportsStore.searchGroups.newSearchGroup(groupName, 'or', searchField);
  } else {
    opportunityReportsStore.searchGroups.addToSearchGroup(groupName, searchField);
  }
};

const handleUnchecked = (
  opportunityReportsStore: OpportunityReportsStore,
  groupName: string,
  searchField: SearchField,
) => {
  const searchGroup = opportunityReportsStore.searchGroups.getSearchGroupFor(groupName);
  if (searchGroup) {
    opportunityReportsStore.searchGroups.deleteFromSearchGroup(groupName, searchField);
  }
};

const areFiveChecked = (
  filterInfoStore: FilterInfoStore,
  opportunityReportsStore: OpportunityReportsStore,
  groupName: string,
  searchField: SearchField,
) => {
  const searchGroup = opportunityReportsStore.searchGroups.getSearchGroupFor(groupName);
  const isChecked = searchGroup ? filterInfoStore.getSelectedGroups(searchGroup, searchField) : false;

  return (
    Number(opportunityReportsStore.searchGroups.getSearchGroupFor(groupName)?.operands?.length) >= 5 &&
    isChecked === false
  );
};
