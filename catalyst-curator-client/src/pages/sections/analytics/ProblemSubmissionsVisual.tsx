import { observer, useLocalObservable } from 'mobx-react';
import { withTheme } from 'react-native-paper';
import { ChartCard } from './ChartCard';
import { HorizontalBarChart } from '../../../appComponents/web/HorizontalBarChart.web';
import { StackedBarChart } from '../../../appComponents/web/StackedBarChart.web';
import { OpportunityReportsStore, UserStore } from '../../../stores';
import { View } from 'react-native';
import { Text } from '../../../lib/ui/atoms/Text';
import { DataVisualTooltipText } from '../../../constants/DataVisualTooltipText';

interface Locals {
  activeSubmissionsTab: string;
  dateDenomination: { label: string; value: string };
  setActiveSubmissionsTab: (tab: string) => void;
  setDateDenomination: (denomination: { label: string; value: string }) => void;
}

const SUBMISSIONS_TABS = [{ name: 'Column' }, { name: 'Stacked', showContext: true }];

const menuItems = [
  { label: 'Last 12 months', value: 'month' },
  { label: 'By year', value: 'year' },
];

interface ProblemSubmissionsVisualProps {
  opportunityReportsStore: OpportunityReportsStore;
  userStore: UserStore;
  theme: ReactNativePaper.ThemeProp;
}

export const ProblemSubmissionsVisual = withTheme(
  observer(({ theme, opportunityReportsStore, userStore }: ProblemSubmissionsVisualProps) => {
    const localStore = useLocalObservable<Locals>(() => ({
      activeSubmissionsTab: 'Stacked',
      dateDenomination: { label: 'Last 12 months', value: 'month' },
      setActiveSubmissionsTab(tab: string) {
        this.activeSubmissionsTab = tab;
      },
      setDateDenomination(denomination: { label: string; value: string }) {
        this.dateDenomination = denomination;
      },
    }));

    const isMultiTenant =
      userStore?.privilegeGroups &&
      userStore.privilegeGroups &&
      userStore.privilegeGroups &&
      userStore.privilegeGroups?.length > 1;

    const submissions = getSubmissions(localStore, opportunityReportsStore);
    const areThereSubmissions = !!submissions.length;

    if (!areThereSubmissions) {
      return (
        <ChartCard
          titleText="Problem Submissions"
          style={{ flex: 5 }}
          tooltipText={DataVisualTooltipText.problemSubmissions}
          menuType="select"
          dropdownMenuProps={{
            getEditable: () => true,
            getMenuItems: () => menuItems,
            getValue: () => localStore.dateDenomination,
            onItemSelected: (menuItem) => {
              localStore.setDateDenomination(menuItem);
            },
          }}
        >
          <View
            style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%', flexShrink: 1 }}
          >
            <Text>No submission data available</Text>
          </View>
        </ChartCard>
      );
    }
    return (
      <ChartCard
        tooltipText={DataVisualTooltipText.problemSubmissions}
        titleText="Problem Submissions"
        style={{ flex: 5 }}
        tabs={
          isMultiTenant
            ? {
                tabs: SUBMISSIONS_TABS,
                currentTab: localStore.activeSubmissionsTab,
                setTab: (tab) => {
                  localStore.setActiveSubmissionsTab(tab);
                },
              }
            : undefined
        }
        menuType="select"
        dropdownMenuProps={{
          getEditable: () => true,
          getMenuItems: () => menuItems,
          getValue: () => localStore.dateDenomination,
          onItemSelected: (menuItem) => {
            localStore.setDateDenomination(menuItem);
          },
        }}
      >
        {localStore.activeSubmissionsTab === 'Column' || !isMultiTenant ? (
          <HorizontalBarChart
            data={formatDates(submissions)}
            xAxisDataKey={localStore.dateDenomination.value}
            barColors={['#225091']}
            highlightedBarColor={'#2B76E5'}
            dataKey="submissionCount"
          />
        ) : localStore.activeSubmissionsTab === 'Stacked' ? (
          <StackedBarChart
            data={formatDates(getTopFiveAndOther(submissions))}
            barColors={['#BB3354', '#A25DDC', '#225091', '#FF642E', '#175A63', '#2B76E5']}
            xAxisDataKey={localStore.dateDenomination.value}
            tenantKeys={extractTenantKeys(getTopFiveAndOther(submissions))}
          />
        ) : null}
      </ChartCard>
    );
  }),
);

const parseData = (data: TenantSubmissionByYearReportResult | TenantSubmissionByMonthReportResult | undefined) => {
  if (!data) return [];
  //data needs to be sorted. dates are in format MM-YYYY

  const sortedData = {
    ...data,
    totals: data?.totals.slice().sort((a, b) => {
      if ('year' in a && 'year' in b) {
        return new Date(`${a.year}-01-01`) > new Date(`${b.year}-01-01`) ? 1 : -1;
      } else if ('month' in a && 'month' in b) {
        return new Date(`01-${a.month}`) > new Date(`01-${b.month}`) ? 1 : -1;
      } else return 0;
    }),
  };

  //dates need parsed to say Jan 2021 instead of 01-2021

  const newData = sortedData?.totals.map((total) => {
    //find in tenantSubmissionResults where month === total.month. If it is, add the tenant name if it does not yet exist, or add the submission count to the existing tenant name
    const tenantBreakdown = data?.tenantSubmissionResults
      .filter((tenant) => {
        if ('year' in tenant && 'year' in total) {
          return tenant.year === total.year;
        } else if ('month' in tenant && 'month' in total) return tenant.month === total.month;
      })
      .map((tenant) => {
        return {
          name: tenant.tenant,
          value: tenant.submissionCount,
        };
      });
    return {
      ...total,
      tenantBreakdown,
    };
  });

  return newData;
};

const getSubmissions = (localStore: Locals, reportsStore: OpportunityReportsStore) => {
  const data =
  localStore.dateDenomination.value === 'month'
      ? reportsStore.tenantSubmissionByMonthReportData
      : reportsStore.tenantSubmissionByYearReportData;

  const formattedData = parseData(data);
  return formattedData;
};

const extractTenantKeys = (data: any) => {
  const allKeys = new Set<string>();
  data.forEach((item: any) => {
    Object.keys(item).forEach((key) => {
      if (key !== 'month' && key !== 'submissionCount' && key !== 'year') {
        allKeys.add(key);
      }
    });
  });
  return Array.from(allKeys);
};

function getLatestDateWithSubmissions(data: ReturnType<typeof parseData>) {
  for (let i = data.length - 1; i >= 0; i--) {
    if (data[i].submissionCount > 0) {
      return data[i];
    }
  }
  return data[data.length - 1];
}

const getTopFiveAndOther = (data: ReturnType<typeof parseData>) => {
  if (!data || data.length === 0) return [];

  const latestDateWithSubmissions = getLatestDateWithSubmissions(data);
  const tenantsSortedByValue = latestDateWithSubmissions.tenantBreakdown.sort((a, b) => b.value - a.value);

  const topFive = tenantsSortedByValue.slice(0, 5);
  const sortedTopFive = topFive.sort((a, b) => b.name.localeCompare(a.name));
  //get a list of all tenants unique across all dates
  const allTenants = data.reduce((acc, curr) => {
    curr.tenantBreakdown.forEach((tenant) => {
      if (!acc.includes(tenant.name)) {
        acc.push(tenant.name);
      }
    });
    return acc;
  }, [] as string[]);

  const newData = data.map((item) => {
    const topFiveData = sortedTopFive.map((topFiveItem) => {
      const tenant = item.tenantBreakdown.find((tenant) => tenant.name === topFiveItem.name);
      return tenant?.value || 0;
    });

    //other should be the sum of all tenants not in the top 5
    const other = item.tenantBreakdown.filter(
      (tenant) => !topFive.find((topFiveItem) => topFiveItem.name === tenant.name),
    );

    const otherTotal = other.reduce((acc, curr) => acc + curr.value, 0);

    if ('year' in item && allTenants.length > 5) {
      return {
        year: item.year,
        Other: otherTotal,
        ...sortedTopFive.reduce(
          (acc, curr, index) => {
            acc[curr.name] = topFiveData[index];
            return acc;
          },
          {} as Record<string, number>,
        ),
        submissionCount: item.submissionCount,
      };
    } else if ('year' in item && allTenants.length <= 5) {
      return {
        year: item.year,
        ...item.tenantBreakdown.reduce(
          (acc, curr) => {
            acc[curr.name] = curr.value;
            return acc;
          },
          {} as Record<string, number>,
        ),
        submissionCount: item.submissionCount,
      };
    } else if ('month' in item && allTenants.length > 5) {
      return {
        month: item.month,
        Other: otherTotal,
        ...sortedTopFive.reduce(
          (acc, curr, index) => {
            acc[curr.name] = topFiveData[index];
            return acc;
          },
          {} as Record<string, number>,
        ),
        submissionCount: item.submissionCount,
      };
    } else if ('month' in item && allTenants.length <= 5) {
      return {
        month: item.month,
        ...item.tenantBreakdown.reduce(
          (acc, curr) => {
            acc[curr.name] = curr.value;
            return acc;
          },
          {} as Record<string, number>,
        ),
        submissionCount: item.submissionCount,
      };
    }
  });

  return newData;
};

const formatDates = (data: ReturnType<typeof getTopFiveAndOther> | ReturnType<typeof getSubmissions>) => {
  return data.map((item) => {
    if (!item) return;
    if ('year' in item) {
      return {
        ...item,
        year: new Date(`01-01-${item.year}`).toLocaleString('default', { year: 'numeric' }),
      };
    } else if ('month' in item) {
      const [month, year] = item.month.split('-');
      const localizedData = new Date(`${month}-01-${year}`).toLocaleString('default', {
        month: 'short',
        year: '2-digit',
      });

      const [monthString, yearString] = localizedData.split(' ');
      return {
        ...item,
        month: `${monthString} '${yearString}`,
      };
    }
  });
};
