import { observer } from 'mobx-react';
import React, { Component, useRef } from 'react';
import { StyleProp, TextStyle, View, ViewStyle } from 'react-native';
import { withTheme } from 'react-native-paper';
import { Debouncer, DebounceFn, Label } from '../../lib';
import { TagCloud } from '../../lib/ui/molecules/TagCloud';
import { TypeAheadInput } from '../../lib/ui/molecules/TypeAheadInput';
import { SectionStyle } from '../CurationPage';
import { Category, UpdateOperator } from '../../services/codegen/types';
import { CategoryStore } from '../../stores/CategoryStore';
import { Text } from '../../lib/ui/atoms/Text';

interface CategorySelectorProps {
  //may be observable
  getCategories: () => Category[] | undefined;
  getEditable?: () => boolean;
  categoryStore: CategoryStore;
  style?: StyleProp<ViewStyle>;
  titleStyle?: StyleProp<TextStyle>;
  theme: ReactNativePaper.ThemeProp;
  onPropertyCategoryChanged: (op: UpdateOperator, value: string) => Promise<void>;
}

export const CategorySelector = withTheme(
  observer((props: CategorySelectorProps) => {
    const { style, getCategories, getEditable, categoryStore, onPropertyCategoryChanged, theme, titleStyle } = props;
    const {
      fonts,
      styles: { margins },
    } = theme;
    const debouncerRef = useRef(Debouncer()).current;
    const categoryItems = categoryStore.matchedCategories.map((category: Category) => {
      return { label: category.name, value: category.name };
    });
    const categories = getCategories();
    return (
      <View style={[{ zIndex: 2 }, style]}>
        <Label>Custom Tags</Label>
        {getEditable && getEditable() && (
          <TypeAheadInput
            style={{ zIndex: 3 }}
            getValue={() => categoryStore.searchValue}
            onValueChange={(value) => handleOnSearchValueChange(debouncerRef, categoryStore, value)}
            onSubmit={() => handleOnSubmitCategory(categoryStore, onPropertyCategoryChanged)}
            getDropdownItems={() => categoryItems}
            buttonEnabled={true}
            placeholder="Search keyword OR press Enter to add the category"
          />
        )}
        {!!categories?.length && (
          <TagCloud
            style={[margins.BottomML]}
            tagStyle={[margins.S]}
            tags={categories?.map((category) => ({ id: category.id, text: category.name }))}
            getEditable={getEditable}
            onRemoveTag={(categoryId) => handleOnClearCategory(onPropertyCategoryChanged, categoryId)}
          />
        )}
        {(!getEditable || !getEditable()) && !categories?.length && <Text>No Categories Selected</Text>}
      </View>
    );
  }),
);

const componentWillUnmount = (categoryStore: CategoryStore) => {
  categoryStore.searchValue = '';
};

const handleOnSearchValueChange = (debouncer: DebounceFn, categoryStore: CategoryStore, text: string) => {
  categoryStore.searchValue = text;
  debouncer(() => {
    categoryStore.searchCategories(categoryStore.pageSize, text);
  }, 500);
};

const handleOnSubmitCategory = async (
  categoryStore: CategoryStore,
  onPropertyCategoryChanged: (op: UpdateOperator, value: string) => void,
) => {
  try {
    if (categoryStore.searchValue) {
      const result = await categoryStore.addCategory(categoryStore.searchValue);
      if (onPropertyCategoryChanged) await onPropertyCategoryChanged(UpdateOperator.Add, result.id);
      categoryStore.clearAll();
    }
  } catch (error) {
    categoryStore.addError(error as any);
  }
};

const handleOnClearCategory = (
  onPropertyCategoryChanged: (op: UpdateOperator, value: string) => void,
  categoryId: string,
) => {
  if (onPropertyCategoryChanged) onPropertyCategoryChanged(UpdateOperator.Rm, categoryId);
};
