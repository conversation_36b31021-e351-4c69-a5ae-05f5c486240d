import { StackNavigationProp } from '@react-navigation/stack';
import { Platform } from 'react-native';
import { nativeInitializer } from './nativeInitializer';
import { NativeRouter } from './NativeRouter';
import { Router } from './Router';
import { webInitializer } from './webInitializer';
import { WebRouter } from './WebRouter';

export class PlatformFactory {
  static getRouter<StackParams extends Record<string, object | undefined>>(
    navigator: StackNavigationProp<StackParams, keyof StackParams>,
  ): Router<StackParams> {
    if (Platform.OS === 'web') return WebRouter.getRouter<StackParams>(navigator);
    return NativeRouter.getRouter<StackParams>(navigator);
  }

  static getPlatformInitializer(): () => Promise<void> {
    if (Platform.OS === 'web') return webInitializer;
    return nativeInitializer;
  }
}
