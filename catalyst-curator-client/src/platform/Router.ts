export abstract class Router<StackParams extends Record<string, object | undefined>> {
  #scrollToTop: () => void = () => undefined;
  abstract goBack(): void;
  abstract navigate<RouteName extends keyof StackParams>(routeName: RouteName, params?: StackParams[RouteName]): void;
  abstract replace<RouteName extends keyof StackParams>(routeName: RouteName, params?: StackParams[RouteName]): void;

  registerScrollToTop(scrollToTop: () => void): void {
    this.#scrollToTop = scrollToTop;
  }

  scrollToTop(): void {
    this.#scrollToTop();
  }
}
