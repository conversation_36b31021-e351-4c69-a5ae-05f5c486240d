import { StackNavigationProp } from '@react-navigation/stack';
import { Router } from './Router';

export class WebRouter<StackParams extends Record<string, object | undefined>> extends Router<StackParams> {
  private constructor(private readonly navigation: StackNavigationProp<StackParams, keyof StackParams>) {
    super();
  }

  static getRouter<StackParams extends Record<string, object | undefined>>(
    navigation: StackNavigationProp<StackParams, keyof StackParams>,
  ): WebRouter<StackParams> {
    return new WebRouter(navigation);
  }

  goBack() {
    this.navigation.pop();
  }

  navigate<RouteName extends keyof StackParams>(routeName: RouteName, params?: StackParams[RouteName]) {
    (this.navigation as any).push(routeName, params);
  }

  replace<RouteName extends keyof StackParams>(routeName: RouteName, params?: StackParams[RouteName]) {
    (this.navigation as any).replace(routeName, params);
  }
}
