import AsyncStorage from '@react-native-async-storage/async-storage';
import Constants from 'expo-constants';
import { Linking } from 'react-native';
import { StoresProvider } from '../lib/stores/StoresProvider';
import { DevelopmentEnvironmentSubPath } from '../lib/constants';
import { CuratorStores, initialize } from './initializers';

export async function webInitializer(): Promise<void> {
  const defaultTenant = Constants?.expoConfig?.extra?.TenantDefault;

  // url stuff
  const url = await Linking.getInitialURL();
  if (!url) return Promise.reject('Failed to get initial url value.');
  const urlInfo = new URL(url);
  const hostSub = urlInfo.hostname.split('.')[0];
  const pathName = urlInfo.pathname;
  const paths = pathName.split('/') || [];
  const isDevelopmentEnvironment = DevelopmentEnvironmentSubPath.includes(hostSub);
  const containsTenantIndex = paths.findIndex((pathValue) => pathValue === 'tenant');
  let tenantHandle;
  // If tenant is in the path, we want to get the value and try to lookup the tenant when we initialize.
  // URL should look like /tenant/monumentsMen where monumentsMen is the handle
  if (isDevelopmentEnvironment && containsTenantIndex >= 0) tenantHandle = paths[containsTenantIndex + 1];
  if (!tenantHandle) tenantHandle = isDevelopmentEnvironment ? defaultTenant : hostSub || defaultTenant;

  // graphQl stuff
  const graphQLUrl = Constants?.expoConfig?.extra?.graphQLUrl;

  // if this is the login page, remove local storage for now
  if (
    pathName === '/' ||
    pathName === '/ic' ||
    pathName === '/InnovationAdministrator/' ||
    pathName === `/InnovationAdministrator/Login` ||
    containsTenantIndex >= 0
  ) {
    await AsyncStorage.clear().then(() => StoresProvider.get<CuratorStores>().clearCache());
  }

  // delegate to base initializer
  await initialize(tenantHandle, graphQLUrl, urlInfo.origin);
}
