import { StackNavigationProp } from '@react-navigation/stack';
import { Router } from './Router';

export class NativeRouter<StackParams extends Record<string, object | undefined>> extends Router<StackParams> {
  private constructor(private readonly navigation: StackNavigationProp<StackParams, keyof StackParams>) {
    super();
  }

  static getRouter<StackParams extends Record<string, object | undefined>>(
    navigation: StackNavigationProp<StackParams, keyof StackParams>,
  ): NativeRouter<StackParams> {
    return new NativeRouter(navigation);
  }

  goBack() {
    this.navigation.goBack();
  }

  navigate<RouteName extends keyof StackParams>(routeName: RouteName, params?: StackParams[RouteName]) {
    return (this.navigation as any).navigate(routeName, params);
  }

  replace<RouteName extends keyof StackParams>(routeName: RouteName, params?: StackParams[RouteName]) {
    return (this.navigation as any).replace(routeName, params);
  }
}
