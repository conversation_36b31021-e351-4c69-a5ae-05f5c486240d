import AsyncStorage from '@react-native-async-storage/async-storage';
import Constants from 'expo-constants';
import { action, computed, makeObservable, observable, override, toJS } from 'mobx';
import { CurationMeta, Dates, notEmptyValidator, phoneValidator, Validator } from '../lib';
import { TaskRunner } from '../lib/TaskRunner';
import { CuratorStores } from '../platform/initializers';
import { StoresProvider } from '../lib/stores/StoresProvider';
import {
  AuthResponse,
  Role,
  RoleNames,
  UpdateCurrentUserInput,
  UpdateCurrentUserLinks,
  User,
} from '../services/codegen/types';
import urqlClient from '../services/urql/CuratorUrqlClient';
import { userService } from '../services/UserService';
import { Store } from '../lib/stores/Store';
import { ApplicationMetaStore } from './ApplicationMetaStore';

const Persistence_Key = `@user_store_curator`;

const validators: Record<string, Validator[]> = {
  phone: [phoneValidator],
  firstName: [notEmptyValidator],
  lastName: [notEmptyValidator],
};

// @TODO
// Convert to use 'call()' function in superclass
// remove devounce from low-level components and add to call()

class UserStore extends Store {
  private static renewAuthTask = new TaskRunner();
  // Note!  All access to _user must be done through get user()
  // and all mutations must be done through the private setUser()
  private _user?: User = undefined;
  private _buffer: UpdateCurrentUserInput = {};
  private _signedIn = false;
  private _userMetaStore = new ApplicationMetaStore();
  private _isMenuCollapsed: boolean = false;

  constructor() {
    super(validators);
    makeObservable<
      UserStore,
      '_signedIn' | '_user' | 'setUser' | '_buffer' | 'setSignedIn' | '_isMenuCollapsed' | 'setIsMenuCollapsed'
    >(this, {
      _user: observable,
      _buffer: observable,
      saveCurrentUser: action,
      deleteCurrentUser: action,
      setUser: action,
      signIn: action,
      signOut: action,
      setSignedIn: action,
      _signedIn: observable,
      signedIn: computed,
      user: computed,
      privilegeGroups: computed,
      clearCache: override,
      restore: override,
      _isMenuCollapsed: observable,
      setIsMenuCollapsed: action,
    });
  }

  async signIn(tenantHandle: string, username: string, password: string): Promise<void> {
    await this.userLogin(tenantHandle, username, password);
  }

  async signOut(): Promise<void> {
    urqlClient.token = '';
    this._signedIn = false;
    const storesProvider = StoresProvider.get<CuratorStores>();
    const applicationStore = storesProvider.getStore('ApplicationStore');
    applicationStore.systemMessage = {
      message: 'Session ended. You’ve logged out successfully.',
      type: 'INFO-SECONDARY',
    };
    this.cancelRenewal();
    storesProvider.clearAll();
  }

  get isMenuCollapsed() {
    return this._isMenuCollapsed;
  }

  setIsMenuCollapsed(value: boolean) {
    this._isMenuCollapsed = value;
  }

  get signedIn(): boolean {
    return this._signedIn;
  }

  get user(): User | undefined {
    return { ...this._user, ...(this._buffer as any) };
  }

  get privilegeGroups() {
    return this._user?.privilegeGroups.flatMap((pg) => pg.privileges);
  }

  get isAdminUser() {
    return this._user?.roles?.some((role: Role) => role.name === RoleNames.Admin) ? true : false;
  }

  get isAnalystUser() {
    return this._user?.roles?.some((role: Role) => role.name === RoleNames.Analyst) ? true : false;
  }

  get isCuratorUser() {
    return this._user?.roles?.some((role: Role) => role.name === RoleNames.Curator) ? true : false;
  }

  setValue(name: string, value: any) {
    this.updateValue(this._buffer, name, value);
  }

  setValues(userValues: UpdateCurrentUserInput) {
    this.updateValues(this._buffer, userValues);
  }

  getValue(name: string, defaultValue?: any) {
    const value = (this.user as any)?.[name];
    return value || defaultValue;
  }

  get userMetaStore(): ApplicationMetaStore {
    return this._userMetaStore;
  }

  async saveCurrentUser(): Promise<void> {
    if (!this.hasErrors) {
      await this.call(async () => {
        // special case for options as it is defined as interface but actually JSON object, must remove __typename if present
        if ((this._buffer.options as any)?.__typename) delete (this._buffer.options as any)?.__typename;
        const userResult = await userService.updateCurrentUser({ input: this._buffer });
        this.mergeUser(userResult);
      });
      this.cacheStore();
    }
  }

  async deleteCurrentUser() {
    await this.call(async () => {
      return userService.deleteCurrentUser();
    });
    return this.signOut();
  }

  clearBuffer() {
    this._buffer = {};
    this.clearErrors();
  }

  async cacheStore() {
    // just for dev!
    if (Constants.expoConfig?.extra?.NODE_ENV == 'development') {
      if (this._user) (this._user as any).token = urqlClient.token;
      await AsyncStorage.setItem(Persistence_Key, JSON.stringify(toJS(this.user)));
    }
  }

  async localClearAll(): Promise<void> {
    this.setUser(undefined);
    this._userMetaStore.clearAll();
    // just for dev!
    if (Constants.expoConfig?.extra?.NODE_ENV == 'development') {
      this.clearCache();
    }
  }

  protected async localInitialize(): Promise<void> {}

  async clearCache() {
    await AsyncStorage.removeItem(Persistence_Key);
  }

  async restore(): Promise<void> {
    try {
      // just for dev!
      if (Constants.expoConfig?.extra?.NODE_ENV === 'development') {
        const userCache = await AsyncStorage.getItem(Persistence_Key);
        const user = userCache ? (JSON.parse(userCache) as User) : undefined;
        if (user) {
          this.setUser(user);
          if ((user as any).token) {
            this.setToken((user as any).token);
            await this.refreshCurrentUser();
            this.setSignedIn();
          }
        }
      }
    } catch (e) {
      console.log(e);
    }
  }

  async refreshCurrentUser(): Promise<void> {
    await this.call(async () => {
      const user = await userService.getCurrentUser();
      // note: there is no secure way to store this locally in a browser.  We'll have to use cookies if want the session to persist
      this.setUser(user);
    });
  }

  async saveUserApplicationMeta(): Promise<void> {
    const appMetaId = await this._userMetaStore.saveMeta();
    if (appMetaId) await this.saveCurrentUserLinks({ appMetaId });
  }

  async resetOpTableMeta(): Promise<void> {
    const tenantMeta = StoresProvider.get<CuratorStores>().getStore('TenantStore').getApplicationMeta();
    this._userMetaStore.oppTable!.tableMeta = (tenantMeta.curationMeta as CurationMeta).oppTable;
    await this.saveUserApplicationMeta();
  }

  async resetAnaTableMeta(): Promise<void> {
    const tenantMeta = StoresProvider.get<CuratorStores>().getStore('TenantStore').getApplicationMeta();
    this._userMetaStore.anaTable!.tableMeta = (tenantMeta.curationMeta as CurationMeta).anaTable;
    await this.saveUserApplicationMeta();
  }

  async resetProjTableMeta(): Promise<void> {
    const tenantMeta = StoresProvider.get<CuratorStores>().getStore('TenantStore').getApplicationMeta();
    this._userMetaStore.projTable!.tableMeta = (tenantMeta.curationMeta as CurationMeta).projTable;
    await this.saveUserApplicationMeta();
  }

  private mergeUser(user: User) {
    this.setUser({ ...this._user, ...this._buffer, ...user });
  }

  private async userLogin(tenantHandle: string, userName: string, password: string): Promise<AuthResponse> {
    this.clearErrors();
    return this.call(async () => {
      const response = await userService.loginUser({ tenantHandle, password, userName });
      this.setToken(response.token);
      this.setUser(response.user);
      // do this last as it will cause a reaction
      this.setSignedIn();
      this.scheduleRenewal(response);
      return response;
    });
  }

  private renewAuth() {
    return this.call(async () => {
      const authResponse = await userService.renewToken();
      urqlClient.token = authResponse.token;
      this.scheduleRenewal(authResponse);
      return authResponse;
    });
  }

  private scheduleRenewal(authResponse: AuthResponse) {
    const bufferInMillis = 60000; // 1 min
    const expiresAt = Dates.asDate(authResponse.expiresAt);
    if (expiresAt) {
      const remainingInMillis = expiresAt.getTime() - Date.now();
      // a negative remaning time will be called immediately, which should force logout
      UserStore.renewAuthTask.do(() => this.renewAuth(), Math.max(remainingInMillis - bufferInMillis, 0));
    }
  }

  private cancelRenewal() {
    UserStore.renewAuthTask.stop();
  }

  private setToken(token: string) {
    urqlClient.token = token;
  }

  private setSignedIn() {
    this._signedIn = true;
  }

  private setUser(user?: User) {
    this._user = user;
    const tenantAppMeta = StoresProvider.get<CuratorStores>().getStore('TenantStore').getApplicationMeta();
    if (user?.appMeta) {
      const projTable = user?.appMeta.curationMeta.projTable;
      const anaTable = user?.appMeta.curationMeta.anaTable;
      const oppTable = user?.appMeta.curationMeta.oppTable;

      this._userMetaStore.applicationMeta = {
        ...user.appMeta,
        curationMeta: {
          projTable: projTable?.cols?.length ? projTable : tenantAppMeta.curationMeta.projTable,
          anaTable: anaTable?.cols?.length ? anaTable : tenantAppMeta.curationMeta.anaTable,
          oppTable: oppTable?.cols?.length ? oppTable : tenantAppMeta.curationMeta.oppTable,
        },
      };
    } else {
      this._userMetaStore.applicationMeta = tenantAppMeta;
    }
    this.clearBuffer();
    this.cacheStore();
  }

  private async saveCurrentUserLinks(links: UpdateCurrentUserLinks): Promise<void> {
    await this.call(async () => {
      const userResult = await userService.updateCurrentUser({ input: {}, links });
    });
  }
}

export default UserStore;
