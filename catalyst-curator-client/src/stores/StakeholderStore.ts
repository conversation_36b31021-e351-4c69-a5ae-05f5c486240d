import { action, computed, makeObservable, observable, runInAction } from 'mobx';
import { Store } from '../lib/stores/Store';
import {
  CreateStakeholderInput,
  PageInfo,
  PagingInput,
  ProjectStakeholderType,
  SearchOperator,
  SearchSortInput,
  Stakeholder,
} from '../services/codegen/types';
import { stakeholderService } from '../services/StakeholderService';
import { emailValidator, phoneValidator, Validator } from '../lib';

// Custom validator to ensure at least one field is filled
const atLeastOneFieldValidator: Validator = (_value: any, store?: any) => {
  if (!store) return { valid: true };

  const fields = [
    'firstName',
    'lastName',
    'emailAddress',
    'altEmailAddress',
    'phone',
    'org',
    'title',
    'organizationRole',
  ];
  const hasValue = fields.some((field) => {
    const fieldValue = store.getValue(field);
    return fieldValue && fieldValue.trim() !== '';
  });

  return hasValue ? { valid: true } : { valid: false, message: 'At least one field must be filled' };
};

const validators: Record<string, Validator[]> = {
  emailAddress: [emailValidator],
  altEmailAddress: [emailValidator],
  phone: [phoneValidator],
  firstName: [atLeastOneFieldValidator],
};

export class StakeholderStore extends Store {
  static readonly DEFAULT_PAGE_SIZE = 500;
  static readonly DEFAULT_SEARCH_SORT: SearchSortInput = {
    sortFields: [{ fieldName: 'lastName', ascending: true }],
  };

  private _stakeholdersSearchResult: Stakeholder[] = [];
  private _pageInfo: PageInfo = {} as PageInfo;
  private _searchSortInput: SearchSortInput = StakeholderStore.DEFAULT_SEARCH_SORT;
  private _pagingInput: PagingInput = { pageSize: StakeholderStore.DEFAULT_PAGE_SIZE, cursor: '0' };
  private _keywordSearchValue: string = '';
  private _buffer: Record<string, any> = {};
  private _stakeholderInput: Partial<CreateStakeholderInput> = {};

  constructor() {
    super(validators);
    makeObservable<
      StakeholderStore,
      '_stakeholdersSearchResult' | '_pageInfo' | '_keywordSearchValue' | '_stakeholderInput' | '_buffer'
    >(this, {
      _stakeholdersSearchResult: observable.shallow,
      _stakeholderInput: observable,
      _buffer: observable,
      _pageInfo: observable,
      _keywordSearchValue: observable,
      searchStakeholders: action,
      addStakeholder: action,
      clearBuffer: action,
      initializeBufferForEditing: action,
      setValue: action,
      stakeholderInput: computed,
    });
  }

  get stakeholderInput(): CreateStakeholderInput {
    return { ...this._stakeholderInput, ...(this._buffer as any) } as CreateStakeholderInput;
  }

  get stakeholdersSearchResult() {
    return this._stakeholdersSearchResult;
  }

  validateAll(): boolean {
    return super.validateAll(this._buffer);
  }

  get pageInfo(): PageInfo {
    return this._pageInfo;
  }

  set keywordSearchValue(value: string) {
    this._keywordSearchValue = value;
    this._searchSortInput = !!value
      ? {
          searchFields: [
            {
              fieldNames: ['firstName', 'lastName', 'emailAddress'],
              operator: SearchOperator.Match,
              searchValue: value,
            },
          ],
        }
      : StakeholderStore.DEFAULT_SEARCH_SORT;
  }

  get keywordSearchValue(): string {
    return this._keywordSearchValue;
  }

  async searchStakeholders(): Promise<Stakeholder[]> {
    return this.call<Stakeholder[]>(async () => {
      const results = await stakeholderService.queryStakeholders({
        searchSortInput: this._searchSortInput,
        pagingInput: this._pagingInput,
      });
      runInAction(() => {
        this._stakeholdersSearchResult = results.map((r) => r).filter(Boolean) as Stakeholder[];
        // Note: StakeholderPage doesn't have pageInfo in the current schema, so we'll set a default
        this._pageInfo = {
          hasNext: false,
          hasPrevious: false,
          lastCursor: '0',
          lastPageSize: 0,
          retrievedCount: results.length,
          totalCount: results.length,
        };
      });
      return results;
    });
  }

  async addStakeholder(): Promise<Stakeholder> {
    await this.setValue('type', undefined);
    return this.call(async () => stakeholderService.createStakeholder({ input: { ...this.stakeholderInput } }));
  }

  async updateStakeholder(id: string): Promise<Stakeholder> {
    await this.setValue('type', undefined);
    return this.call(async () => stakeholderService.updateStakeholder({ id, input: this.stakeholderInput }));
  }

  async deleteStakeholder(id: string): Promise<boolean> {
    return this.call(async () => stakeholderService.deleteStakeholder(id));
  }

  setValue(name: string, value: any) {
    this.updateValue(this._buffer, name, value);
  }

  getValue(name: string, defaultValue?: any) {
    const bufferValue = this._buffer[name];
    if (bufferValue !== undefined) {
      return bufferValue;
    }

    const value = (this.stakeholderInput as any)?.[name];
    return value !== undefined ? value : defaultValue;
  }

  hasValue(name: string): boolean {
    return this._buffer.hasOwnProperty(name);
  }

  clearBuffer() {
    this._buffer = {};
  }

  static getFieldMapping(): Record<string, string> {
    return {
      firstName: 'firstName',
      lastName: 'lastName',
      title: 'title',
      phone: 'phone',
      email: 'emailAddress',
      altEmail: 'altEmailAddress',
      organization: 'org',
      organizationRole: 'organizationRole',
    };
  }

  getValueByOpportunityField(field: string, defaultValue?: any) {
    const mapping = StakeholderStore.getFieldMapping();
    return this.getValue(mapping[field], defaultValue);
  }

  setValueByOpportunityField(field: string, value: any) {
    const mapping = StakeholderStore.getFieldMapping();
    this.setValue(mapping[field], value);
  }

  getValueWithFallback(name: string, fallbackValue?: any): any {
    if (this.hasValue(name)) {
      return this.getValue(name);
    }
    return fallbackValue;
  }

  initializeBufferForEditing(stakeholder: Stakeholder, type?: ProjectStakeholderType) {
    this.setValue('firstName', stakeholder.firstName || '');
    this.setValue('lastName', stakeholder.lastName || '');
    this.setValue('title', stakeholder.title || '');
    this.setValue('phone', stakeholder.phone || '');
    this.setValue('emailAddress', stakeholder.emailAddress || '');
    this.setValue('altEmailAddress', stakeholder.altEmailAddress || '');
    this.setValue('org', stakeholder.org || '');
    this.setValue('organizationRole', stakeholder.organizationRole || '');
    if (type) {
      this.setValue('type', type);
    }
  }

  protected async localInitialize(): Promise<void> {}

  async localClearAll(): Promise<void> {
    this._stakeholdersSearchResult = [];
    this._pageInfo = {} as PageInfo;
    this._searchSortInput = StakeholderStore.DEFAULT_SEARCH_SORT;
    this._pagingInput = { pageSize: StakeholderStore.DEFAULT_PAGE_SIZE, cursor: '0' };
    this._keywordSearchValue = '';
    this._buffer = {};
    this._stakeholderInput = {};
  }
}
