import { action, makeObservable, observable, runInAction } from 'mobx';
import { Store } from '../lib/stores/Store';
import {
  CreateOpportunityOwnerInput,
  MutationUpdateOpportunityOwnerArgs,
  OpportunityOwnerStatus,
  PageInfo,
  PagingInput,
  SearchOperator,
  SearchSortInput,
  User,
} from '../services/codegen/types';
import { opportunityOwnerService } from '../services/OpportunityOwnerService';
import { emailValidator, notEmptyValidator, Validator } from '../lib';
import { userService } from '../services/UserService';

const validators: Record<string, Validator[]> = {
  firstName: [notEmptyValidator],
  lastName: [notEmptyValidator],
  emailAddress: [notEmptyValidator, emailValidator],
};
export class OpportunityOwnerStore extends Store {
  static readonly DEFAULT_PAGE_SIZE = 500;
  static readonly DEFAULT_SEARCH_SORT: SearchSortInput = {
    sortFields: [{ fieldName: 'lastName', ascending: true }],
  };

  private _opportunityOwnersSearchResult: User[] = [];
  private _pageInfo: PageInfo = {} as PageInfo;
  private _searchSortInput: SearchSortInput = OpportunityOwnerStore.DEFAULT_SEARCH_SORT;
  private _pagingInput: PagingInput = { pageSize: OpportunityOwnerStore.DEFAULT_PAGE_SIZE, cursor: '0' };
  private _keywordSearchValue: string = '';
  private _buffer: Record<string, any> = {};
  private _opportunityOwnerInput: Partial<CreateOpportunityOwnerInput> = {};

  constructor() {
    super(validators);
    makeObservable<
      OpportunityOwnerStore,
      '_opportunityOwnersSearchResult' | '_pageInfo' | '_keywordSearchValue' | '_opportunityOwnerInput' | '_buffer'
    >(this, {
      _opportunityOwnersSearchResult: observable.shallow,
      _opportunityOwnerInput: observable,
      _buffer: observable,
      _pageInfo: observable,
      _keywordSearchValue: observable,
      searchOwners: action,
      addOpportunityOwner: action,
      setSelectedSearchOwnerResult: action,
    });
  }

  setSelectedSearchOwnerResult(value: User) {
    this.setValue('firstName', value.firstName);
    this.setValue('lastName', value.lastName);
    this.setValue('emailAddress', value.emailAddress);
    this.setValue('phone', value.phone);
    this.setValue('org1', value.org1);
    this.setValue('org2', value.org2);
    this.setValue('org3', value.org3);
    this.setValue('org4', value.org4);
    this.setValue('altContact', value.altContact);
    this.setValue('userId', value.id);
  }

  get opportunityOwnerInput(): CreateOpportunityOwnerInput {
    return { ...this._opportunityOwnerInput, ...(this._buffer as any) } as CreateOpportunityOwnerInput;
  }

  get opportunityOwnersSearchResult() {
    return this._opportunityOwnersSearchResult;
  }

  validateAll(): boolean {
    return super.validateAll(this._buffer);
  }

  get pageInfo(): PageInfo {
    return this._pageInfo;
  }

  set keywordSearchValue(value: string) {
    this._keywordSearchValue = value;
    this._searchSortInput = !!value
      ? {
          searchFields: [
            {
              fieldNames: ['firstName', 'lastName', 'emailAddress'],
              operator: SearchOperator.Match,
              searchValue: value,
            },
          ],
        }
      : OpportunityOwnerStore.DEFAULT_SEARCH_SORT;
  }

  get keywordSearchValue(): string {
    return this._keywordSearchValue;
  }

  async searchOwners(): Promise<User[]> {
    return this.call<User[]>(async () => {
      const { results, pageInfo } = await userService.queryUsers({
        searchSortInput: this._searchSortInput,
        pagingInput: this._pagingInput,
      });
      runInAction(() => {
        this._opportunityOwnersSearchResult = results.map((r) => r).filter(Boolean) as User[];
        this._pageInfo = pageInfo;
      });
      return results;
    });
  }

  async addOpportunityOwner(opportunityId: string): Promise<OpportunityOwnerStatus> {
    return this.call(async () =>
      opportunityOwnerService.createOpportunityOwner({ input: { opportunityId, ...this.opportunityOwnerInput } }),
    );
  }

  async updateOpportunityOwner(id: string, input: MutationUpdateOpportunityOwnerArgs): Promise<OpportunityOwnerStatus> {
    return this.call(async () => opportunityOwnerService.updateOpportunityOwner(input));
  }

  setValue(name: string, value: any) {
    this.updateValue(this._buffer, name, value);
  }

  getValue(name: string, defaultValue?: any) {
    const value = (this.opportunityOwnerInput as any)?.[name];
    return value || defaultValue;
  }

  protected async localInitialize(): Promise<void> {}

  async localClearAll(): Promise<void> {
    this._opportunityOwnersSearchResult = [];
    this._pageInfo = {} as PageInfo;
    this._searchSortInput = OpportunityOwnerStore.DEFAULT_SEARCH_SORT;
    this._pagingInput = { pageSize: OpportunityOwnerStore.DEFAULT_PAGE_SIZE, cursor: '0' };
    this._keywordSearchValue = '';
    this._buffer = {};
    this._opportunityOwnerInput = {};
  }
}
