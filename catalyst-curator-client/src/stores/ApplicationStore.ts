import { InitialState } from '@react-navigation/native';
import { action, computed, makeObservable, observable } from 'mobx';
import { ErrorInfo, SystemMessage } from '../lib';
import { ApplicationConfig } from '../lib/types';
import { Store } from '../lib/stores/Store';

export class ApplicationStore extends Store {
  private _systemMessage?: SystemMessage = undefined;
  applicationLoaded = false;

  // at some point, we'll want to serailize router state for mobile.  web should use the url with Linking utility
  // https://reactnavigation.org/docs/navigation-container#initialstate
  navigationState?: InitialState;

  constructor(public applicationConfig: ApplicationConfig) {
    super();
    makeObservable<ApplicationStore, '_systemMessage'>(this, {
      applicationConfig: observable,
      applicationLoaded: observable,
      _systemMessage: observable,
      loadApplication: action,
      systemMessage: computed,
    });
  }

  async loadApplication(): Promise<void> {
    this.applicationLoaded = false;
    // do anything asynchrounous here (i.e load saved local state)
    this.applicationLoaded = true;
  }

  get systemMessage(): SystemMessage | undefined {
    return this._systemMessage;
  }

  set systemMessage(systemMessage: SystemMessage | undefined) {
    this._systemMessage = systemMessage;
  }

  clearSystemMessage() {
    this.systemMessage = undefined;
  }

  protected async localInitialize(): Promise<void> {
    return this.loadApplication();
  }
  async localClearAll(): Promise<void> {}
}
