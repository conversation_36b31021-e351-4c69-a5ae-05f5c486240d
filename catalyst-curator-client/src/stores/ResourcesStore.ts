import { action, computed, makeObservable, observable } from 'mobx';
import { PrivilegeGroup, Resource, ResourceType, Scope } from '../services/codegen/types';
import { Store } from '../lib/stores/Store';
import { getPortfolios } from '../lib/Privileges';
import UserStore from './UserStore';

export class ResourcesStore extends Store {
  private _resources: Resource[] | null = [];
  hasDefaultResources: boolean = false;

  constructor(private userStore: UserStore) {
    super();
    makeObservable<ResourcesStore, '_resources' | 'scope'>(this, {
      _resources: observable,
      addResource: action,
      deleteResource: action,
      hasDefaultResources: observable,
      clear: action,
      scope: computed,
    });
  }

  addResource(resourceId: string | undefined | null, resourceType: ResourceType) {
    if (!this._resources) this._resources = [];
    if (!resourceId) return;
    if (this.doesResourceExist(resourceId)) return;
    this._resources?.push({ resourceId, resourceType });
  }

  deleteResource(resourceId?: string | null) {
    if (!this._resources) return;
    this._resources = this._resources?.filter((resource) => resource.resourceId !== resourceId);
  }

  clear() {
    this._resources = null;
  }

  doesResourceExist(resourceId?: string | null): boolean {
    if (!this._resources) return false;
    return this._resources?.some((resource) => resource.resourceId === resourceId);
  }

  get resources() {
    return this._resources;
  }

  get scope() {
    const scope = { resources: [] } as Scope;

    if (scope.resources && this.resources) {
      scope.resources = this.resources;
    }
    return this.resources && this.hasDefaultResources ? scope : undefined;
  }

  private getResources(privilegeGroups: PrivilegeGroup[] | undefined): Resource[] {
    const privilegedTenants = getPortfolios(privilegeGroups);
    return privilegedTenants.map((portfolio) => ({
      resourceId: portfolio.resourceId as string,
      resourceType: portfolio.resourceType,
    }));
  }

  async setDefaultResources() {
    const defaultResources = this.getResources(this.userStore.user?.privilegeGroups);
    this.hasDefaultResources = defaultResources.length > 0;

    defaultResources.forEach((resource) => {
      this.addResource(resource.resourceId, resource.resourceType);
    });
  }

  protected async localInitialize(): Promise<void> {
    this.setDefaultResources();
  }

  protected async localClearAll(): Promise<void> {
    this.clear();
  }
}
