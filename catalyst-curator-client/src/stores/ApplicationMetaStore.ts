import { action, computed, makeObservable, observable, toJS } from 'mobx';
import { Store } from '../lib/stores/Store';
import { applicationMetaService } from '../services/ApplicationMetaService';
import { ApplicationMeta } from '../services/codegen/types';
import { TableMetaStore } from '../lib/stores/TableMetaStore';
import { AssertedAppMeta, CurationMeta } from '../lib';

export class ApplicationMetaStore extends Store {
  private _id?: string = undefined;
  private _applicationMeta?: Partial<AssertedAppMeta> = undefined;
  private _oppTable = new TableMetaStore();
  private _anaTable = new TableMetaStore();
  private _projTable = new TableMetaStore();

  constructor() {
    super();
    makeObservable<
      ApplicationMetaStore,
      '_oppTable' | '_applicationMeta' | '_anaTable' | '_projTable' | 'updateApplicationMeta'
    >(this, {
      _applicationMeta: observable.shallow,
      _oppTable: observable.shallow,
      _anaTable: observable.shallow,
      _projTable: observable.shallow,
      updateApplicationMeta: action,
    });
  }

  // setters must set all observables!
  set applicationMeta(applicationMeta: Partial<ApplicationMeta> | undefined) {
    this._id = applicationMeta?.id;
    this._applicationMeta = applicationMeta;
    this._oppTable.tableMeta = this._applicationMeta?.curationMeta?.oppTable;
    this._anaTable.tableMeta = this._applicationMeta?.curationMeta?.anaTable;
    this._projTable.tableMeta = this._applicationMeta?.curationMeta?.projTable;
    this.updateAppMeta();
  }

  // setters must set all observables!
  updateApplicationMeta(applicationMeta: Partial<ApplicationMeta>) {
    this._applicationMeta = applicationMeta;
    this._oppTable.tableMeta = this._applicationMeta?.curationMeta?.oppTable;
    this._anaTable.tableMeta = this._applicationMeta?.curationMeta?.anaTable;
    this._projTable.tableMeta = this._applicationMeta?.curationMeta?.projTable;
    this.updateAppMeta();
  }

  get oppTable(): TableMetaStore | undefined {
    return this._oppTable;
  }

  get anaTable(): TableMetaStore | undefined {
    return this._anaTable;
  }

  get projTable(): TableMetaStore | undefined {
    return this._projTable;
  }

  async saveMeta(): Promise<string | void> {
    if (this._id) {
      await this.call(async () => {
        if (!this._applicationMeta) return;
        await applicationMetaService.updateApplicationMeta({
          id: this._id!,
          input: {
            curationMeta: {
              anaTable: this.anaTable?.tableMeta,
              oppTable: this.oppTable?.tableMeta,
              projTable: this.projTable?.tableMeta,
            },
          },
        });
      });
    } else {
      return await this.call(async () => {
        if (!this._applicationMeta) return;
        const { id } = await applicationMetaService.createApplicationMeta({ input: this._applicationMeta });
        this._id = id;
        return id;
      });
    }
  }

  protected async localClearAll(): Promise<void> {
    this._id = undefined;
    this._applicationMeta = undefined;
    this._oppTable.tableMeta = undefined;
    this._oppTable.tableColMap = undefined;
    this._anaTable.tableMeta = undefined;
    this._anaTable.tableColMap = undefined;
    this._projTable.tableMeta = undefined;
    this._projTable.tableColMap = undefined;
  }

  protected async localInitialize(): Promise<void> {}

  private updateAppMeta() {
    this._applicationMeta!.curationMeta!.oppTable = this._oppTable.tableMeta as CurationMeta['oppTable'];
    this._applicationMeta!.curationMeta!.anaTable = this._anaTable.tableMeta as CurationMeta['anaTable'];
    this._applicationMeta!.curationMeta!.projTable = this._projTable.tableMeta as CurationMeta['projTable'];
  }
}
