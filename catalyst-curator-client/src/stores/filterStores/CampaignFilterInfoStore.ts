import { action, makeObservable } from 'mobx';
import { opportunityCalculationService } from '../../services/OpportunityCalulationService';
import { QueryOpportunityCalculationArgs, Scope, SearchOperator } from '../../services/codegen/types';
import { SearchField } from '../../services/codegen/types';
import { FilterInfoStore } from '../FilterInfoStore';

export class CampaignFilterInfoStore extends FilterInfoStore {
  private campaignInput: QueryOpportunityCalculationArgs;

  constructor() {
    super();
    makeObservable<CampaignFilterInfoStore, 'updateCampaignFilterList'>(this, {
      updateCampaignFilterList: action,
    });
    this.campaignInput = {
      calculation: { distinct: true, operations: [{ fieldName: this.groupName }] },
      scope: undefined,
      searchSortInput: undefined,
    };
  }

  async updateCampaignFilterList(searchFor: Array<string> = [], scope?: Scope): Promise<void> {
    this.campaignInput.searchSortInput = undefined;
    if (scope) this.campaignInput.scope = scope;
    if (searchFor.length > 0) {
      const updateSearchField: SearchField = {
        fieldNames: this.fieldNames,
        searchValue: searchFor,
        operator: SearchOperator.Match,
      };
      this.campaignInput.searchSortInput = { searchFields: [updateSearchField] };
    }
    const { operationResults } = await opportunityCalculationService.getOpportunityCalculation(this.campaignInput);
    this.setLabelSearchField(operationResults[0].result);
  }
}
