import { Priority } from '../../lib/Priority';
import { PRIORITY_LABELS } from '../../lib/ui/organisms/PriorityMenu';
import { SearchOperator } from '../../services/codegen/types';
import { FilterInfoStore } from '../FilterInfoStore';

export function priorityFilterStore(): FilterInfoStore {
  const priorityFilterInfo = new FilterInfoStore();
  priorityFilterInfo.fieldOrGroup = 'FIELD';
  priorityFilterInfo.groupName = 'priority';
  priorityFilterInfo.info = [
    { label: PRIORITY_LABELS[Priority.HIGH], value: Priority.HIGH },
    { label: PRIORITY_LABELS[Priority.MEDIUM], value: Priority.MEDIUM },
    { label: PRIORITY_LABELS[Priority.LOW], value: Priority.LOW },
    { label: PRIORITY_LABELS[Priority.NONE], value: Priority.NONE },
  ];
  return priorityFilterInfo;
}

export function relationshipsFilterStore(): FilterInfoStore {
  const relationshipsFilterInfo = new FilterInfoStore();
  relationshipsFilterInfo.fieldOrGroup = 'GROUP';
  relationshipsFilterInfo.groupName = 'relationships';
  relationshipsFilterInfo.info = [
    {
      label: 'Parents',
      searchField: { fieldNames: ['childOpportunityCount'], searchValue: 1, operator: SearchOperator.Gte },
    },
    {
      label: 'Linked',
      searchField: { fieldNames: ['linkedOpportunityCount'], searchValue: 1, operator: SearchOperator.Gte },
    },
    {
      label: 'Children',
      searchField: { fieldNames: ['parentOpportunityCount'], searchValue: 1, operator: SearchOperator.Gte },
    },
    {
      label: 'None',
      searchField: { fieldNames: ['relatedOpportunityCount'], searchValue: 1, operator: SearchOperator.Lt },
    },
  ];
  return relationshipsFilterInfo;
}

export function statusFilterStore(): FilterInfoStore {
  const statusFilterInfo = new FilterInfoStore();
  statusFilterInfo.fieldOrGroup = 'FIELD';
  statusFilterInfo.groupName = 'status';
  statusFilterInfo.info = [
    { label: 'APPROVED', value: 'Approved' },
    { label: 'PENDING', value: 'Pending' },
    { label: 'ARCHIVED', value: 'Archived' },
  ];
  return statusFilterInfo;
}

export function warFightingFunctionFilterStore(): FilterInfoStore {
  const warFightingFunctionFilterInfo = new FilterInfoStore();
  warFightingFunctionFilterInfo.fieldOrGroup = 'GROUP';
  warFightingFunctionFilterInfo.groupName = 'function';
  warFightingFunctionFilterInfo.info = [
    { label: 'APPROVED', value: 'Approved' },
    { label: 'PENDING', value: 'Pending' },
    { label: 'ARCHIVED', value: 'Archived' },
  ];
  warFightingFunctionFilterInfo.useSearchBar = true;
  return warFightingFunctionFilterInfo;
}
