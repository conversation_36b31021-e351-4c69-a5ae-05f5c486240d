import { action, computed, makeObservable, observable } from 'mobx';
import { SearchOperator } from '../services/codegen/types';
import { SearchField } from '../services/codegen/types';
import { groupsEqual, JsonSearchGroup } from '../lib/stores/JsonSearchGroups';

export type FilterInfo = FieldInfo | GroupInfo;

interface FieldInfo {
  groupName: string;
  useSearchBar?: boolean;
  fieldOrGroup: 'FIELD';
  labelValue: FieldLabelValue[];
}

interface GroupInfo {
  groupName: string;
  useSearchBar?: boolean;
  fieldOrGroup: 'GROUP';
  labelValue: GroupLabelValue[];
}

export interface GroupLabelValue {
  label: string | null;
  searchField: SearchField;
}
export interface FieldLabelValue {
  label: string | null;
  value: any;
}

type FieldGroupTypes = 'FIELD' | 'GROUP';

export class FilterInfoStore {
  private _fieldOrGroup: 'FIELD' | 'GROUP' = 'GROUP';
  private _info: GroupLabelValue[] | FieldLabelValue[] = [];
  private _useSearchBar: boolean = false;
  private _fieldNames: Array<string> = ['campaign'];
  private _groupName: string = 'campaign';
  private _searchTerm: string = '';

  private defaultCheckbox: GroupLabelValue = {
    label: 'Unassigned',
    searchField: { fieldNames: this.fieldNames, searchValue: null, operator: SearchOperator.Eq },
  };

  constructor() {
    makeObservable<FilterInfoStore, '_info' | '_searchTerm'>(this, {
      _info: observable,
      info: computed,
      setLabelSearchField: action,
      filteredInfo: computed,
      _searchTerm: observable,
      searchTerm: computed,
    });
  }

  get groupName() {
    return this._groupName;
  }

  set groupName(value: string) {
    this._groupName = value;
  }

  get fieldNames() {
    return this._fieldNames;
  }

  set fieldNames(value: Array<string>) {
    this._fieldNames = value;
  }

  get fieldOrGroup() {
    return this._fieldOrGroup;
  }

  set fieldOrGroup(value: 'FIELD' | 'GROUP') {
    this._fieldOrGroup = value;
  }

  get useSearchBar() {
    return this._useSearchBar;
  }

  set useSearchBar(value: boolean) {
    this._useSearchBar = value;
  }

  get info() {
    return this._info;
  }

  set info(value: GroupLabelValue[] | FieldLabelValue[]) {
    this._info = value;
  }

  get searchTerm() {
    return this._searchTerm;
  }

  set searchTerm(value: string) {
    this._searchTerm = value;
  }

  getSelectedGroups(searchGroup: JsonSearchGroup, searchField: SearchField) {
    if (searchGroup) {
      return !!searchGroup.operands?.some((operand) => groupsEqual(operand, searchField));
    }
    return false;
  }

  getSelectedFields(searchFields: SearchField[], value: any) {
    if (searchFields?.length) {
      return searchFields.some((searchField) => (searchField.searchValue as Array<any>).includes(value));
    }
    return false;
  }

  setLabelSearchField(operationResults: Array<string> = []) {
    if (this.fieldOrGroup === 'GROUP') {
      const updatedInfo: Array<GroupLabelValue> = [];

      if (!location.pathname.includes('overview')) {
        updatedInfo.push(this.defaultCheckbox);
      } else {
        const noneIndex = operationResults.findIndex((checkbox) => checkbox === 'None');
        if (noneIndex !== -1) {
          operationResults.splice(noneIndex, 1);
        }
      }

      operationResults.forEach((campaign) => {
        if (!campaign) return;
        updatedInfo.push({
          label: campaign,
          searchField: { fieldNames: this.fieldNames, searchValue: campaign, operator: SearchOperator.Match },
        });
      });
      this.info = updatedInfo;
      return;
    }
    // TODO set other fieldOrGroup
  }

  get filteredInfo() {
    if (!this.searchTerm) {
      return this.info;
    } else {
      return this.info.filter((item) => item.label?.toLowerCase().includes(this.searchTerm.toLowerCase()));
    }
  }
}
