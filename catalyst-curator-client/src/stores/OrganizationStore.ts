import { action, computed, makeObservable, observable } from 'mobx';
import { MenuGroup, MenuItem } from '../lib';
import { Store } from '../lib/stores/Store';
import { Organization } from '../lib/types';

export class OrganizationStore extends Store {
  constructor(private _organizations: Array<Organization> = []) {
    super();
  }

  async reset(organizations: Array<Organization> = []): Promise<void> {
    this._organizations = organizations;
  }

  get organizations() {
    return this._organizations;
  }

  async localClearAll() {}
  protected async localInitialize(): Promise<void> {}

  orgsToMenuGroups(): MenuGroup[] {
    return this.organizations.map((org) => this.mapOrg(org));
  }

  private mapOrg = (org: Organization): MenuGroup => {
    // @TODO - when orgs are in database, use id for value here
    const item: MenuItem = { label: org.label, value: org.label };
    return { item, children: org.children?.map((child) => this.mapOrg(child)) };
  };
}
