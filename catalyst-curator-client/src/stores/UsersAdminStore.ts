import { Roles, RolesInfo } from '../lib';
import { action, computed, makeObservable, observable, runInAction } from 'mobx';
import { PageInfo, PagingInput, SearchOperator, SearchSortInput, User } from '../services/codegen/types';
import { userService } from '../services/UserService';
import { Store } from '../lib/stores/Store';

export class UsersAdminStore extends Store {
  static readonly DEFAULT_PAGE_SIZE = 500;
  static readonly DEFAULT_SEARCH_SORT: SearchSortInput = {
    searchFields: [{ fieldNames: ['status'], operator: SearchOperator.Eq, searchValue: 'Verified' }],
    sortFields: [{ fieldName: 'lastName', ascending: true }],
  };

  private _users: User[] = [];
  private _pageInfo: PageInfo = {} as PageInfo;
  private _searchSortInput: SearchSortInput = UsersAdminStore.DEFAULT_SEARCH_SORT;
  private _pagingInput: PagingInput = { pageSize: UsersAdminStore.DEFAULT_PAGE_SIZE, cursor: '0' };
  private _keywordSearchValue: string = '';
  private _roleFilter: RolesInfo | 'Any' = 'Any';

  constructor() {
    super();
    makeObservable<UsersAdminStore, '_users' | '_pageInfo' | '_keywordSearchValue' | '_roleFilter'>(this, {
      _users: observable.shallow,
      _pageInfo: observable,
      _keywordSearchValue: observable,
      _roleFilter: observable,
      queryUsers: action,
      mergeUpdatedUser: action,
      deleteUser: action,
      keywordSearchValue: computed,
      pageInfo: computed,
      users: computed,
    });
  }

  get users() {
    if (this._roleFilter === 'Any') return this._users;
    //@TODO - move this filter to server side (complex query)
    return this._users.filter(
      (user) => Roles.determineRole(user.roles).effectiveRoleName === (this._roleFilter as RolesInfo).effectiveRoleName,
    );
  }

  async queryUsers(): Promise<User[]> {
    return this.call<User[]>(async () => {
      const { results, pageInfo } = await userService.queryUsers({
        searchSortInput: this._searchSortInput,
        pagingInput: this._pagingInput,
      });
      runInAction(() => {
        this._users = results as User[];
        this._pageInfo = pageInfo;
      });
      return results as User[];
    });
  }

  async mergeUpdatedUser(user: User): Promise<void> {
    const userIndex = this._users.findIndex((_user) => _user.id === user.id);
    if (userIndex > -1) {
      this._users.splice(userIndex, 1, user);
      this._users = [...this._users];
    }
  }

  async deleteUser(id: string) {
    await userService.deleteUser({ id: id });
    const userIndex = this._users.findIndex((_user) => _user.id === id);
    if (userIndex > -1) {
      this._users.splice(userIndex, 1);
      this._users = [...this._users];
    }
  }

  get pageInfo(): PageInfo {
    return this._pageInfo;
  }

  set roleFilter(roleFilter: RolesInfo) {
    this._roleFilter = roleFilter;
  }

  set keywordSearchValue(value: string) {
    this._keywordSearchValue = value;
    this._searchSortInput = !!value
      ? {
          searchFields: [
            {
              fieldNames: ['firstName', 'lastName', 'emailAddress'],
              operator: SearchOperator.Match,
              searchValue: value,
            },
            ...(UsersAdminStore.DEFAULT_SEARCH_SORT.searchFields as []),
          ],
          sortFields: UsersAdminStore.DEFAULT_SEARCH_SORT.sortFields,
        }
      : UsersAdminStore.DEFAULT_SEARCH_SORT;
  }

  get keywordSearchValue(): string {
    return this._keywordSearchValue;
  }

  async localClearAll(): Promise<void> {
    this._users = [];
  }

  protected async localInitialize(): Promise<void> {}
}
