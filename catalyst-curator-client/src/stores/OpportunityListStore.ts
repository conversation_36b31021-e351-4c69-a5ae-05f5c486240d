import { action, computed, makeObservable, observable, toJS } from 'mobx';

import {
  FilterOpportunitiesQuery,
  FilterOpportunitiesQueryVariables,
  Opportunity,
  PageInfo,
  PagingInput,
  Scope,
  SearchField,
  SearchOperator,
  SearchSortInput,
  SortField,
} from '../services/codegen/types';
import { listOpportunityService } from '../services/ListOpportunityService';
import { Store } from '../lib/stores/Store';
import { SearchFields, SearchOutput } from '../lib/stores/SearchFields';
import { JsonSearchGroups, JsonSearchOutput } from '../lib/stores/JsonSearchGroups';
import { downloadManagerLocation, openWebFileStream } from '../utilities/File';
import { ResourcesStore } from './ResourcesStore';
import { FilterInfoStore } from './FilterInfoStore';
import {
  warFightingFunctionFilterStore,
  priorityFilterStore,
  statusFilterStore,
  relationshipsFilterStore,
} from './filterStores/opportunityListFilterStores';
import { CampaignFilterInfoStore } from './filterStores/CampaignFilterInfoStore';

const SortKey = 'SortKey';
const KEYWORD_SEARCH_FIELD = {
  fieldNames: [
    'status',
    'title',
    'solutionPathway',
    'statusNotes',
    'campaign',
    'armyModernizationPriority',
    'echelonApplicability',
    'transitionInContactLineOfEffort',
    'org1',
    'org2',
    'categories.name',
    'stakeholders.name',
    'stakeholders.org',
    'user.org1',
    'user.org2',
  ],
  searchValue: '',
} as SearchField;

/* End default search settings */
const DELETED_FILTER: SearchField = {
  fieldNames: ['status'],
  operator: SearchOperator.Ne,
  searchValue: 'Deleted',
};

export const OpportunityListStoreNames = {
  OpportunityListStore: 'OpportunityListStoreName',
  AnalyticsListStore: 'AnalyticsListStoreName',
};

class OpportunityListStore extends Store {
  static readonly DEFAULT_PAGE_SIZE = 50;
  items: Array<Opportunity> = [];
  pageInfo: PageInfo = {} as PageInfo;
  private _pageSize: number = OpportunityListStore.DEFAULT_PAGE_SIZE;
  private _sortField: SortField | undefined = undefined;
  // simple search fields that are AND'd by default
  private _searchFields = new SearchFields();
  private _keywordSearchValue: string = '';
  private _searchGroups = new JsonSearchGroups();
  private _selection?: string = undefined;
  private _previousSearchFields: Map<string, JsonSearchOutput | SearchOutput | string | undefined> = new Map();
  private _storeName: string = 'OpportunityListStore';
  private _listFilterStores: Record<string, FilterInfoStore | CampaignFilterInfoStore> = {};
  returnPageSize?: number = undefined;

  constructor(private resources: ResourcesStore | undefined = undefined) {
    super();
    makeObservable<
      OpportunityListStore,
      'filterOpportunities' | '_sortField' | '_pageSize' | '_selection' | 'returnPageSize' | '_keywordSearchValue'
    >(this, {
      _sortField: observable,
      _pageSize: observable,
      pageSize: computed,
      _selection: observable,
      _keywordSearchValue: observable,
      returnPageSize: observable,
      pageInfo: observable,
      items: observable,
      setSortedColumn: action,
      clearSortedColumn: action,
      filterOpportunities: action,
      setOpportunities: action,
      setPageInfo: action,
      pageCount: computed,
      pageNumber: computed,
      clearKeywordSearchValue: action,
      resetSearchValues: action,
      restoreCacheFiltersFor: action,
    });

    this._listFilterStores = {
      warFightingFunctionFilterStore: warFightingFunctionFilterStore(),
      priorityFilterInfo: priorityFilterStore(),
      statusFilterInfo: statusFilterStore(),
      relationshipsFilterInfo: relationshipsFilterStore(),
      campaignInfoFilterStore: new CampaignFilterInfoStore(),
    };
  }

  // manage row selection
  get listFilterStores() {
    return this._listFilterStores;
  }

  get selection(): string | undefined {
    return this._selection;
  }

  set selection(selection: string | undefined) {
    this._selection = selection;
    this.returnPageSize = this.items.length;
  }

  // convert all this paging logic into a Paginator class
  get pageCount(): number {
    const { lastPageSize, totalCount } = this.pageInfo;
    return totalCount && lastPageSize ? Math.ceil(totalCount / lastPageSize) : 0;
  }

  get pageSize(): number {
    return this._pageSize;
  }

  get pageNumber(): number {
    const { totalCount, lastCursor } = this.pageInfo;
    return !totalCount || lastCursor === '0' ? 0 : Math.floor((parseInt(lastCursor) / totalCount) * this.pageCount);
  }

  set pageNumber(pageNumber: number) {
    this.pageInfo.lastCursor = (pageNumber * this.pageSize).toString();
  }

  get storeName() {
    return this._storeName;
  }

  set storeName(storeName: string) {
    this._storeName = storeName;
  }

  appendOpportunities(opportunities: Opportunity[]): void {
    opportunities = opportunities.filter((opportunity) => !this.items.some((item) => item.id === opportunity.id));
    this.items = this.items.concat(opportunities);
  }

  getSortedColumn(): SortField | undefined {
    return this._sortField;
  }

  setSortedColumn(sortField: SortField): void {
    this._sortField = sortField;
  }

  clearSortedColumn(): void {
    this._sortField = undefined;
  }

  getSortFieldStatus(fieldName: string): 'ascending' | 'descending' | undefined {
    const sortedColumn = this.getSortedColumn();
    if (!sortedColumn || sortedColumn.fieldName !== fieldName) return;
    return sortedColumn.ascending ? 'ascending' : 'descending';
  }

  get searchFields(): SearchFields {
    return this._searchFields;
  }

  get searchGroups(): JsonSearchGroups {
    return this._searchGroups;
  }

  get keywordSearchValue(): string {
    return this._keywordSearchValue;
  }

  set keywordSearchValue(value: string) {
    const searchField = KEYWORD_SEARCH_FIELD;
    searchField.searchValue = value;
    this._searchFields.setSearchFieldsFor('KEYWORD', [searchField]);
    this._keywordSearchValue = this._searchFields.getSearchFieldsFor('KEYWORD')?.[0].searchValue || '';
  }

  clearKeywordSearchValue(): void {
    this._searchFields.deleteAllSearchFieldsFor('KEYWORD');
    this._keywordSearchValue = this._searchFields.getSearchFieldsFor('KEYWORD')?.[0].searchValue || '';
  }

  clearCachedFiltersFor(namedFilter: string) {
    this._previousSearchFields.delete(`${namedFilter}.searchFields`);
    this._previousSearchFields.delete(`${namedFilter}.searchGroups`);
    this._previousSearchFields.delete(`${namedFilter}.keywordSearch`);
  }

  restoreCacheFiltersFor(namedFilter: string): void {
    this.resetSearchValues();
    const previousSearchFields = (this._previousSearchFields.get(`${namedFilter}.searchFields`) as SearchOutput) || [];
    Object.entries(previousSearchFields).reduce((acc: SearchOutput, [key, values]) => {
      if (values) {
        this.searchFields.setSearchFieldsFor(key, values);
      }
      return acc;
    }, {});

    const previousSearchGroupJson =
      (this._previousSearchFields.get(`${namedFilter}.searchGroups`) as JsonSearchOutput) || {};
    Object.entries(previousSearchGroupJson).reduce((acc: JsonSearchOutput, [key, value]) => {
      if (value) {
        this._searchGroups.setSearchGroupFor(key, value);
      }
      return acc;
    }, {});

    this.keywordSearchValue =
      (this._previousSearchFields.get(`${namedFilter}.keywordSearch`) as string) || this.keywordSearchValue;
  }

  cacheFiltersFor(namedFilter: string): void {
    this._previousSearchFields.set(`${namedFilter}.searchFields`, this._searchFields.asKeyValueObject());
    this._previousSearchFields.set(`${namedFilter}.searchGroups`, this.searchGroups.asKeyValueObject());
    this._previousSearchFields.set(`${namedFilter}.keywordSearch`, toJS(this.keywordSearchValue.toString()));
  }

  resetSearchValues(): void {
    this._searchFields.clear();
    this._searchGroups.clear();
    // hide deleted items from search
    this._searchFields.addSearchFieldFor('DELETED_FILTER', DELETED_FILTER);
    this._keywordSearchValue = this._searchFields.getSearchFieldsFor('KEYWORD')?.[0].searchValue || '';
    // If this changes, update 'isDefault' in searchFields and searchGroups
  }

  protected async localInitialize(): Promise<void> {
    const campaignInfoFilterStore = this.listFilterStores.campaignInfoFilterStore as CampaignFilterInfoStore;
    campaignInfoFilterStore.updateCampaignFilterList([], this.resources?.scope);
    await this.queryItems();
  }

  async queryItems(pageSize?: number): Promise<void> {
    this.cacheFiltersFor(this.storeName);
    return this.filterOpportunities({ pageSize: pageSize ? pageSize : this.pageSize, cursor: '0' });
  }

  async updatePageSize(): Promise<void> {
    if (!this.queryInProgress) {
      const pagingInput: PagingInput = { pageSize: this.pageSize, cursor: this.items.length.toString() };
      return this.filterOpportunities(pagingInput);
    }
  }

  async handleDownload(fileName: string, fields?: string[]) {
    const filterOpportunitiesInput = this.getFilterOpportunitiesQuery();
    const body = JSON.stringify({
      filterOpportunitiesInput: {
        ...filterOpportunitiesInput,
        scope: filterOpportunitiesInput.scope,
      },
      fields,
    });
    try {
      await openWebFileStream(`${downloadManagerLocation}/opp`, fileName, body);
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (e: any) {
      this.addError(e);
    }
  }

  getFilterOpportunitiesQuery(
    pagingInput: PagingInput = { pageSize: OpportunityListStore.DEFAULT_PAGE_SIZE, cursor: '0' },
  ) {
    const newSearchSortInput = {} as SearchSortInput;
    const sortField = this.getSortedColumn();
    newSearchSortInput.sortFields = sortField ? [sortField] : [];
    newSearchSortInput.searchFields = this._searchFields.asArray();
    newSearchSortInput.jsonSearchGroups = this._searchGroups.asArray();

    return {
      searchSortInput: newSearchSortInput,
      pagingInput,
      scope: this.resources?.scope,
    } as FilterOpportunitiesQueryVariables;
  }

  private async filterOpportunities(
    pagingInput: PagingInput = { pageSize: OpportunityListStore.DEFAULT_PAGE_SIZE, cursor: '0' },
  ): Promise<void> {
    if (!this.queryInProgress) {
      this.queryInProgress = true;
      const filterOpportunities = this.getFilterOpportunitiesQuery(pagingInput);
      try {
        const result: FilterOpportunitiesQuery =
          await listOpportunityService.filterOpportunitiesList(filterOpportunities);
        const {
          queryOpportunities: { results, pageInfo },
        } = result;
        if (pageInfo.hasPrevious === true) {
          this.appendOpportunities(results as Opportunity[]);
        } else {
          this.setOpportunities(results as Opportunity[]);
        }
        this.setPageInfo(pageInfo as PageInfo);
        this.queryInProgress = false;
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
      } catch (error: any) {
        this.addError(error);
        throw error;
      } finally {
        this.queryInProgress = false;
      }
    }
  }

  getOpportunities(id: string): Opportunity | undefined {
    return this.items.find((opportunity) => opportunity.id === id);
  }

  setOpportunities(opportunities: Array<Opportunity>): void {
    this.items = opportunities;
  }

  setPageInfo(pageInfo: PageInfo) {
    this.pageInfo = pageInfo;
  }

  async localClearAll(): Promise<void> {
    this.items = [];
    this.pageInfo = {} as PageInfo;
    this._sortField = undefined;
    this.resetSearchValues();
  }
}

export default OpportunityListStore;
