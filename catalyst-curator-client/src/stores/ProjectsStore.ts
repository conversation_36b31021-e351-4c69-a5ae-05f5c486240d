import { action, computed, makeObservable, observable } from 'mobx';
import { Store } from '../lib/stores/Store';
import {
  PageInfo,
  PagingInput,
  Project,
  QueryProjectsQueryVariables,
  SearchField,
  SearchOperator,
  SearchSortInput,
  SortField,
} from '../services/codegen/types';
import { projectService } from '../services/ProjectService';
import { SearchFields } from '../lib/stores/SearchFields';
import { JsonSearchGroups } from '../lib/stores/JsonSearchGroups';

const KEYWORD_SEARCH_FIELD = {
  fieldNames: [
    'title',
    'status',
    'type',
    'statusNotes',
    'projectStakeholders.stakeholder.firstName',
    'projectStakeholders.stakeholder.org',
  ],
  searchValue: '',
} as SearchField;

const DELETED_FILTER: SearchField = {
  fieldNames: ['status'],
  operator: SearchOperator.Ne,
  searchValue: 'Deleted',
};

class ProjectsStore extends Store {
  static readonly DEFAULT_PAGE_SIZE = 50;
  items: Array<Project> = [];
  pageInfo: PageInfo = {} as PageInfo;
  private _pageSize: number = ProjectsStore.DEFAULT_PAGE_SIZE;
  private _sortField: SortField | undefined = undefined;
  // @TODO - use the SearchFields observable instead (see OpportunityStore)
  private _searchFields = new SearchFields();
  private _searchGroups = new JsonSearchGroups();
  private _selection?: string = undefined;
  returnPageSize?: number = undefined;

  constructor() {
    super();
    makeObservable<ProjectsStore, 'filterProjects' | '_pageSize' | '_sortField' | '_selection' | 'returnPageSize'>(
      this,
      {
        _sortField: observable,
        _pageSize: observable,
        _selection: observable,
        pageInfo: observable,
        items: observable,
        setSortedColumn: action,
        clearSortedColumn: action,
        filterProjects: action,
        setProjects: action,
        setPageInfo: action,
        pageCount: computed,
        pageNumber: computed,
        pageSize: computed,
        returnPageSize: observable,
      },
    );
    this.resetSearchValues();
  }

  // convert all this paging logic into a Paginator class

  get selection(): string | undefined {
    return this._selection;
  }

  set selection(selection: string | undefined) {
    this._selection = selection;
    this.returnPageSize = this.items.length;
  }

  get pageCount(): number {
    const { lastPageSize, totalCount } = this.pageInfo;
    return totalCount && lastPageSize ? Math.ceil(totalCount / lastPageSize) : 0;
  }

  get pageNumber(): number {
    // lastCursor is the previous zero-based starting index
    // pageNumber is the zero-based page, calculated from the last response values
    const { totalCount, lastCursor } = this.pageInfo;
    return !totalCount || lastCursor === '0' ? 0 : Math.floor((parseInt(lastCursor) / totalCount) * this.pageCount);
  }

  get pageSize(): number {
    return this._pageSize;
  }

  appendProjects(projects: Project[]): void {
    projects = projects.filter((project) => !this.items.some((item) => item.id === project.id));
    this.items = this.items.concat(projects);
  }

  getSortedColumn(): SortField | undefined {
    return this._sortField;
  }

  setSortedColumn(sortField: SortField): void {
    this._sortField = sortField;
  }

  clearSortedColumn(): void {
    this._sortField = undefined;
  }

  getSortFieldStatus(fieldName: string): 'ascending' | 'descending' | undefined {
    const sortField = this.getSortedColumn();
    if (!sortField || sortField.fieldName !== fieldName) return;
    return sortField.ascending ? 'ascending' : 'descending';
  }

  async queryItems(pageSize?: number): Promise<void> {
    return this.filterProjects({ pageSize: pageSize ? pageSize : this.pageSize });
  }

  async updatePageSize(): Promise<void> {
    if (!this.queryInProgress) {
      const pagingInput: PagingInput = { pageSize: this.pageSize, cursor: this.items.length.toString() };
      return this.filterProjects(pagingInput);
    }
  }

  private async filterProjects(
    pagingInput: PagingInput = { pageSize: ProjectsStore.DEFAULT_PAGE_SIZE, cursor: '0' },
  ): Promise<void> {
    if (!this.queryInProgress) {
      this.queryInProgress = true;
      const newSearchSortInput = {} as SearchSortInput;
      const sortedColumn = this.getSortedColumn();

      newSearchSortInput.sortFields = sortedColumn ? [sortedColumn] : [];
      newSearchSortInput.searchFields = this._searchFields.asArray();
      newSearchSortInput.jsonSearchGroups = this._searchGroups.asArray();

      const filterProjects = {
        searchSortInput: newSearchSortInput,
        pagingInput,
      } as QueryProjectsQueryVariables;
      try {
        const result = await projectService.queryProject(filterProjects);
        const { results, pageInfo } = result;
        if (pageInfo.hasPrevious === true) {
          this.appendProjects(results as Project[]);
        } else {
          this.setProjects(results as Project[]);
        }
        this.setPageInfo(pageInfo as PageInfo);
        this.queryInProgress = false;
      } catch (error: any) {
        this.addError(error);
        throw error;
      } finally {
        this.queryInProgress = false;
      }
    }
  }

  getProjects(id: string): Project | undefined {
    return this.items.find((project) => project.id === id);
  }

  setProjects(projects: Array<Project>): void {
    this.items = projects;
  }

  setPageInfo(pageInfo: PageInfo) {
    this.pageInfo = pageInfo;
  }

  get searchFields(): SearchFields {
    return this._searchFields;
  }

  get searchGroups(): JsonSearchGroups {
    return this._searchGroups;
  }

  getKeywordSearchValue(): string | undefined {
    return this._searchFields.getSearchFieldsFor('KEYWORD')?.[0].searchValue;
  }

  setKeywordSearchValue(value: string): void {
    const searchField = KEYWORD_SEARCH_FIELD;
    searchField.searchValue = value;
    this._searchFields.setSearchFieldsFor('KEYWORD', [searchField]);
  }

  clearKeywordSearchValue(): void {
    this._searchFields.deleteAllSearchFieldsFor('KEYWORD');
  }

  resetSearchValues(): void {
    this._searchFields.clear();
    this._searchGroups.clear();
    this._searchFields.addSearchFieldFor('DELETED_FILTER', DELETED_FILTER);
  }

  async localClearAll(): Promise<void> {
    this.items = [];
    this.pageInfo = {} as PageInfo;
    this._sortField = undefined;
    this.resetSearchValues();
  }
  protected async localInitialize(): Promise<void> {
    return this.queryItems();
  }
}

export default ProjectsStore;
