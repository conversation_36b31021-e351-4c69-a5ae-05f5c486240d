import { action, computed, makeObservable, observable, override } from 'mobx';
import { Store } from '../lib/stores/Store';
import { SearchField, SearchOperator, Opportunity } from '../services/codegen/types';
import { listOpportunityService } from '../services/ListOpportunityService';

const DEFAULT_PAGE_SIZE = 10;

interface SearchValue {
  fieldName: string;
  value?: string;
}

interface SortValue {
  fieldName: string;
}
export class OpportunitySearchStore extends Store {
  private _matchedOpportunities: Partial<Opportunity>[] = [];
  private _searchValues: Record<string, string | undefined> = {};
  private _sortValue: SortValue = { fieldName: 'title' };
  private _pageSize: number = DEFAULT_PAGE_SIZE;

  constructor() {
    super();
    makeObservable<OpportunitySearchStore, '_matchedOpportunities' | '_searchValues' | '_pageSize' | '_sortValue'>(
      this,
      {
        _matchedOpportunities: observable,
        _searchValues: observable,
        _sortValue: observable,
        _pageSize: observable,
        queryOpportunities: action,
        sortField: computed,
        pageSize: computed,
        matchedOpportunities: computed,
        clearAll: override,
        clearCache: override,
        restore: override,
      },
    );
  }

  get matchedOpportunities(): Partial<Opportunity>[] {
    return this._matchedOpportunities;
  }

  getMatchedOpportunityById(id: string) {
    return this.matchedOpportunities.find((opportunities) => opportunities.id === id);
  }

  getMatchedOpportunity(fieldValues: SearchValue[]) {
    return this.matchedOpportunities.find((matchOpportunity) => {
      fieldValues.every((fieldValue) => {
        (matchOpportunity as any)[fieldValue.fieldName] === fieldValue.value ||
          (!(matchOpportunity as any)[fieldValue.fieldName] && !fieldValue.value);
      });
    });
  }

  uniqueMatches(fieldName: string): string[] {
    return this.matchedOpportunities.reduce((accum, opportunities) => {
      const value = (opportunities as any)[fieldName];
      if (value && !accum.includes(value)) accum.push(value);
      return accum;
    }, [] as string[]);
  }

  setFieldSearchValue(fieldSearchValue: SearchValue) {
    const { fieldName, value } = fieldSearchValue;
    this._searchValues[fieldName] = value;
  }

  getSearchValue(fieldName: string): string | undefined {
    return this._searchValues[fieldName];
  }

  set sortField(sortField: string) {
    this._sortValue.fieldName = sortField;
  }

  get sortField(): string {
    return this._sortValue.fieldName;
  }

  set pageSize(pageSize: number) {
    this._pageSize = pageSize;
  }

  get pageSize() {
    return this._pageSize;
  }

  async localClearAll(): Promise<void> {
    this._matchedOpportunities = [];
    this._searchValues = {};
    this._sortValue = { fieldName: 'title' };
    this._pageSize = DEFAULT_PAGE_SIZE;
  }

  protected async localInitialize(): Promise<void> {}

  async queryOpportunities(): Promise<void> {
    const searchFields: SearchField[] = [];
    Object.keys(this._searchValues).forEach((searchFieldName) => {
      if (this._searchValues[searchFieldName])
        searchFields.push({
          fieldNames: [searchFieldName],
          operator: SearchOperator.Match,
          searchValue: this._searchValues[searchFieldName],
        });
    });
    return searchFields.length
      ? this.call(async () => {
          const result = await listOpportunityService.findOpportunities({
            pagingInput: { pageSize: this._pageSize },
            searchSortInput: {
              searchFields,
              sortFields: [{ fieldName: this.sortField, ascending: true }],
            },
          });
          this._matchedOpportunities = result.queryOpportunities.results as Partial<Opportunity>[];
        })
      : undefined;
  }
}
