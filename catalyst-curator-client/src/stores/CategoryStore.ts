import { computed, makeObservable, observable, override } from 'mobx';
import { Store } from '../lib/stores/Store';
import { categoryService } from '../services/CategoryService';
import { Category } from '../services/codegen/types';

const DEFAULT_PAGE_SIZE = 10;

export class CategoryStore extends Store {
  private _matchedCategories: Array<Category> = [];
  private _searchValue: string = '';
  private _pageSize: number = DEFAULT_PAGE_SIZE;

  constructor() {
    super();
    makeObservable<CategoryStore, '_matchedCategories' | '_searchValue' | '_pageSize'>(this, {
      _matchedCategories: observable,
      _searchValue: observable,
      _pageSize: observable,
      searchValue: computed,
      pageSize: computed,
      matchedCategories: computed,
      clearAll: override,
      clearCache: override,
      restore: override,
    });
  }

  get matchedCategories() {
    return this._matchedCategories;
  }

  getMatchedCategoryById(id: string) {
    return this.matchedCategories.find((category) => category.id === id);
  }

  getMatchedCategoryByName(name: string) {
    return this.matchedCategories.find((category) => category.name === name);
  }

  set searchValue(searchValue: string) {
    this._searchValue = searchValue;
  }

  get searchValue() {
    return this._searchValue;
  }

  set pageSize(pageSize: number) {
    this._pageSize = pageSize;
  }

  get pageSize() {
    return this._pageSize;
  }

  async localClearAll(): Promise<void> {
    this._matchedCategories = [];
    this._searchValue = '';
    this._pageSize = DEFAULT_PAGE_SIZE;
  }

  protected async localInitialize(): Promise<void> {}

  getCategory(id?: string, name?: string): Promise<Category> {
    return this.call(async () => categoryService.getCategory({ id, name }));
  }

  searchCategories(pageSize: Number = DEFAULT_PAGE_SIZE, searchValue: string): Promise<void> {
    return this.call(async () => {
      this._matchedCategories = await categoryService.getCategories(pageSize, '^' + searchValue);
    });
  }

  async addCategory(name: string): Promise<Category> {
    return this.getMatchedCategoryByName(name) || this.call(async () => categoryService.addCategory(name));
  }

  removeCategory(id: string): void {
    // TODO: Stub for late date for admins to remove stakeholder entries.
  }
}
