import { action, computed, makeObservable, observable } from 'mobx';
import { Roles, RolesInfo } from '../lib';
import { emailValidator, notEmptyValidator, phoneValidator, Validator } from '../lib';
import { CreateUserInput, UpdateCurrentUserInput, User, UserLinks, VerifiedStatus } from '../services/codegen/types';
import { userService } from '../services/UserService';
import { Store } from '../lib/stores/Store';

const validators: Record<string, Validator[]> = {
  phone: [phoneValidator],
  firstName: [notEmptyValidator],
  lastName: [notEmptyValidator],
  emailAddress: [notEmptyValidator, emailValidator],
};

// @TODO
// Convert to use 'call()' function in superclass
// remove debounce from low-level components and add to call()

class UserAdminStore extends Store {
  // Note!  All access to _user must be done through get user()
  // and all mutations must be done through the private setUser()
  private _buffer: Record<string, any> = {};
  private _pendingUserLinks: UserLinks = {};

  constructor(private _user: User) {
    super(validators);
    makeObservable<UserAdminStore, '_user' | 'setUser' | '_buffer' | '_pendingUserLinks'>(this, {
      _user: observable,
      _buffer: observable,
      _pendingUserLinks: observable,
      saveUser: action,
      setUser: action,
      user: computed,
      rolesInfo: computed,
      pendingUserLinks: computed,
    });
  }

  get user(): User {
    return { ...this._user, ...(this._buffer as any) };
  }

  get rolesInfo(): RolesInfo {
    if (this._pendingUserLinks.roleNames?.length) {
      return Roles.determineRoleFromNames(this._pendingUserLinks.roleNames);
    }
    return Roles.determineRole(this.user.roles);
  }

  setValue(name: string, value: any) {
    this.updateValue(this._buffer, name, value);
  }

  setValues(userValues: UpdateCurrentUserInput) {
    this.updateValues(this._buffer, userValues);
  }

  set pendingUserLinks(links: UserLinks) {
    this._pendingUserLinks = links;
  }

  get pendingUserLinks() {
    return this._pendingUserLinks;
  }

  getValue(name: string, defaultValue?: string) {
    const value = (this.user as any)?.[name];
    return value || defaultValue;
  }

  validateAll(): boolean {
    return super.validateAll(this._buffer);
  }

  async saveUser(): Promise<void> {
    if (!this.hasErrors) {
      return this.call<void>(async () => {
        const links = this.pendingUserLinks;
        const userResult = await userService.updateUser({ input: this._buffer, id: this.user.id, links });
        this.mergeUser(userResult as User);
      });
    }
  }

  async createUser(): Promise<void> {
    if (!this.hasErrors) {
      return this.call<void>(async () => {
        const links = this.pendingUserLinks;
        const userResult = await userService.createUser({
          input: { status: VerifiedStatus.Verified, ...this._buffer } as CreateUserInput,
          links,
        });
        this.mergeUser(userResult as User);
      });
    }
  }

  clearBuffer() {
    this._buffer = {};
    this._pendingUserLinks = {};
    this.clearErrors();
  }

  protected async localClearAll(): Promise<void> {}
  protected async localInitialize(): Promise<void> {}

  private mergeUser(user: Partial<User>) {
    this.setUser({ ...this._user, ...user });
  }

  private setUser(user: User) {
    this._user = user;
    this.clearBuffer();
  }
}

export default UserAdminStore;
