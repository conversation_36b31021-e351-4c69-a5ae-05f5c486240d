import { action, computed, makeObservable, observable } from 'mobx';
import { Store } from '../lib/stores/Store';
import {
  OpportunityOwnerStatus,
  OwnershipStatus,
  User,
  Opportunity,
  UpdateOpportunityOwnerInput,
} from '../services/codegen/types';
import { opportunityOwnerService } from '../services/OpportunityOwnerService';

export class OpportunityOwnerListStore extends Store {
  private _opportunityOwnerStatuses: OpportunityOwnerStatus[] = [];
  private _opportunityId: string;

  constructor(opportunityId: string) {
    super();
    this.localInitialize();
    this._opportunityId = opportunityId;
    makeObservable<OpportunityOwnerListStore, '_opportunityOwnerStatuses' | 'opportunityOwnerStatuses'>(this, {
      _opportunityOwnerStatuses: observable,
      setOpportunityOwnerStatuses: action,
      localClearAll: action,
      refresh: action,
      updateOpportunityOwner: action,
      opportunityOwnerStatuses: computed,
      currentOwner: computed,
      initialOwner: computed,
      previousOwners: computed,
      removedOwners: computed,
      hasRemovedOwners: computed,
      hasPreviousOwners: computed,
    });
  }

  get opportunityOwnerStatuses(): OpportunityOwnerStatus[] {
    return this._opportunityOwnerStatuses;
  }

  get currentOwner(): OpportunityOwnerStatus | undefined {
    const currentOwner = this._opportunityOwnerStatuses.find(
      (ownerStatus) => ownerStatus.status === OwnershipStatus.Current,
    );
    return currentOwner;
  }

  getOpportunityId(): string {
    return this._opportunityId;
  }

  get initialOwner(): OpportunityOwnerStatus | undefined {
    return this._opportunityOwnerStatuses.find((ownerStatus) => ownerStatus.status === OwnershipStatus.Initial);
  }

  get previousOwners(): OpportunityOwnerStatus[] {
    return this._opportunityOwnerStatuses
      .filter((ownerStatus) => {
        return ownerStatus.status === OwnershipStatus.Previous && ownerStatus.isRemoved !== true;
      })
      .sort((a, b) => {
        // Sort by statusSetRemovedAt descending (latest removed first)
        return new Date(b.statusSetRemovedAt ?? 0).getTime() - new Date(a.statusSetRemovedAt ?? 0).getTime();
      });
  }

  get removedOwners(): OpportunityOwnerStatus[] {
    return this._opportunityOwnerStatuses.filter((ownerStatus) => ownerStatus.isRemoved === true);
  }

  get hasRemovedOwners(): boolean {
    return this.removedOwners.length > 0;
  }

  get hasPreviousOwners(): boolean {
    return this.previousOwners.length > 0;
  }

  async refresh(): Promise<void> {
    return this.call(async () => {
      const { results } = await opportunityOwnerService.queryOpportunityOwners({
        searchSortInput: {
          searchFields: [
            {
              fieldNames: ['opportunity.id'],
              operator: 'EQ' as any,
              searchValue: this._opportunityId,
            },
          ],
        },
      });
      this.setOpportunityOwnerStatuses(results);
    });
  }

  async updateOpportunityOwner(args: { id: string; input: UpdateOpportunityOwnerInput }): Promise<void> {
    return this.call(async () => {
      await opportunityOwnerService.updateOpportunityOwner(args);
    });
  }

  setOpportunityOwnerStatuses(ownerStatuses: OpportunityOwnerStatus[]): void {
    this._opportunityOwnerStatuses = ownerStatuses;
  }

  protected async localInitialize(): Promise<void> {
    return this.refresh();
  }

  async localClearAll(): Promise<void> {
    this._opportunityOwnerStatuses = [];
  }
}
