import { action, makeObservable, observable, runInAction } from 'mobx';
import { Store } from '../lib/stores/Store';
import { Scope, SearchOperator, SearchSortInput } from '../services/codegen/types';
import { ReportStore } from './ReportStore';
import { ResourcesStore } from './ResourcesStore';
import { JsonSearchGroups } from '../lib/stores/JsonSearchGroups';
import { CampaignFilterInfoStore } from './filterStores/CampaignFilterInfoStore';

export class OpportunityReportsStore extends Store {
  private campaignReport?: ReportStore = new ReportStore();
  private priorityReport?: ReportStore = new ReportStore();
  private statusReport?: ReportStore = new ReportStore();
  private submissionReport?: ReportStore = new ReportStore();
  private tenantSubmissionByYearReport?: ReportStore = new ReportStore();
  private tenantSubmissionByMonthReport?: ReportStore = new ReportStore();
  private WFFReport?: ReportStore = new ReportStore();
  private _searchGroups = new JsonSearchGroups();

  priorityReportData?: OpportunityPriorityReportResult[] = undefined;
  statusReportData?: OpportunityStatusReportResult[] = undefined;
  WFFReportData?: OpportunityWFFReportResult[] = undefined;
  campaignReportData?: OpportunityCampaignReportResult = undefined;
  submissionReportData?: OpportunitySubmissionReportResult = undefined;
  tenantSubmissionByYearReportData?: TenantSubmissionByYearReportResult = undefined;
  tenantSubmissionByMonthReportData?: TenantSubmissionByMonthReportResult = undefined;
  areReportsFetched = false;
  campaignListFilter: CampaignFilterInfoStore = new CampaignFilterInfoStore();

  constructor(private resources: ResourcesStore) {
    super();
    makeObservable<
      OpportunityReportsStore,
      | 'campaignReportData'
      | 'priorityReportData'
      | 'statusReportData'
      | 'submissionReportData'
      | 'tenantSubmissionByYearReportData'
      | 'tenantSubmissionByMonthReportData'
      | 'WFFReportData'
      | 'areReportsFetched'
    >(this, {
      campaignReportData: observable,
      priorityReportData: observable,
      statusReportData: observable,
      submissionReportData: observable,
      tenantSubmissionByYearReportData: observable,
      tenantSubmissionByMonthReportData: observable,
      WFFReportData: observable,
      areReportsFetched: observable,
      campaignListFilter: observable,
      getReports: action,
      getCampaignReport: action,
      getPriorityReport: action,
      getStatusReport: action,
      getSubmissionReport: action,
      getTenantSubmissionByYearReport: action,
      getTenantSubmissionByMonthReport: action,
      getWFFReport: action,
    });
    this.campaignListFilter.groupName = 'campaign';
    this.campaignListFilter.useSearchBar = true;
  }

  get searchGroups(): JsonSearchGroups {
    return this._searchGroups;
  }

  async getCampaignReport(): Promise<void> {
    const newSearchSortInput = {} as SearchSortInput;
    newSearchSortInput.jsonSearchGroups = this._searchGroups.asArray();
    await this.campaignReport?.getNewReport({
      reportInput: { queries: [{ reportName: 'oppCampaign', searchSortInput: newSearchSortInput }] },
      scope: this.resources.scope,
    });

    runInAction(() => {
      this.campaignReportData = this.campaignReport?.report?.data;
    });
  }

  async getPriorityReport(): Promise<void> {
    await this.priorityReport?.getNewReport({
      reportInput: { queries: [{ reportName: 'oppPriority' }] },
      scope: this.resources.scope,
    });
    runInAction(() => {
      this.priorityReportData = this.priorityReport?.report?.data;
    });
  }

  async getStatusReport(): Promise<void> {
    await this.statusReport?.getNewReport({
      reportInput: { queries: [{ reportName: 'oppStatus' }] },
      scope: this.resources.scope,
    });
    runInAction(() => {
      this.statusReportData = this.statusReport?.report?.data;
    });
  }

  async getSubmissionReport(): Promise<void> {
    await this.submissionReport?.getNewReport({
      reportInput: { queries: [{ reportName: 'oppSubmission' }] },
      scope: this.resources.scope,
    });
    runInAction(() => {
      this.submissionReportData = this.submissionReport?.report?.data;
    });
  }

  async getTenantSubmissionByYearReport(): Promise<void> {
    await this.tenantSubmissionByYearReport?.getNewReport({
      reportInput: { queries: [{ reportName: 'oppTenantSubmissionByYear' }] },
      scope: this.resources.scope,
    });
    runInAction(() => {
      this.tenantSubmissionByYearReportData = this.tenantSubmissionByYearReport?.report?.data;
    });
  }

  async getTenantSubmissionByMonthReport(): Promise<void> {
    const startDate = new Date();
    startDate.setFullYear(startDate.getFullYear() - 1);
    startDate.setDate(1);

    await this.tenantSubmissionByMonthReport?.getNewReport({
      reportInput: {
        queries: [
          {
            reportName: 'oppTenantSubmission',
            searchSortInput: {
              jsonSearchGroups: [{ fieldNames: ['createdAt'], operator: SearchOperator.Gte, searchValue: startDate }],
            },
          },
        ],
      },
      scope: this.resources.scope,
    });
    runInAction(() => {
      this.tenantSubmissionByMonthReportData = this.tenantSubmissionByMonthReport?.report?.data;
    });
  }

  async getWFFReport(): Promise<void> {
    await this.WFFReport?.getNewReport({
      reportInput: { queries: [{ reportName: 'oppWFF' }] },
      scope: this.resources.scope,
    });
    runInAction(() => {
      this.WFFReportData = this.WFFReport?.report?.data;
    });
  }

  async getReports(): Promise<void> {
    await this.getCampaignReport();
    await this.getPriorityReport();
    await this.getStatusReport();
    await this.getSubmissionReport();
    await this.getTenantSubmissionByYearReport();
    await this.getTenantSubmissionByMonthReport();
    await this.getWFFReport();
    runInAction(() => {
      this.areReportsFetched = true;
    });
  }

  protected async localInitialize(): Promise<void> {
    this.campaignListFilter.updateCampaignFilterList([], this.resources.scope);
    await this.getReports();
  }

  protected async localClearAll(): Promise<void> {}
}

export default OpportunityReportsStore;
