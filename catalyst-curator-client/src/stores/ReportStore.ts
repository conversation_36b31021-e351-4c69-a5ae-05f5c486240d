import { action, makeObservable, observable } from 'mobx';
import { Store } from '../lib/stores/Store';
import { opportunityReportService } from '../services/OpportunityReportService';
import { QueryReportArgs, Report, Scope } from '../services/codegen/types';

export class ReportStore extends Store {
  report?: Report = undefined;
  //Can request multiple reports. Only want one for now.
  constructor() {
    super();
    makeObservable<ReportStore, 'report' | 'setReport'>(this, {
      report: observable,
      setReport: action,
    });
    this.initialize();
  }

  private setReport(report: Report | undefined) {
    this.report = report;
  }

  async getNewReport(input: QueryReportArgs): Promise<Report> {
    const { reports } = await opportunityReportService.getOpportunitiesReport(input);

    this.setReport(reports[0] as Report);
    return reports[0];
  }

  protected async localClearAll(): Promise<void> {}

  protected async localInitialize(): Promise<void> {}
}
