import { OverrideStyles, AppStyles } from '../lib';

export function getStyleOverrides(theme: ReactNativePaper.ThemeProp): OverrideStyles {
  return {
    components: {
      menuStyle: {
        ...AppStyles.baseStyles.paddings.TopM,
        ...AppStyles.baseStyles.paddings.LeftM,
        minWidth: 300,
        elevation: 0,
        backgroundColor: theme.colors.listHeader,
      },
      dropDownMenuAnchorStyle: {
        ...AppStyles.baseStyles.buttonStyle,
        ...AppStyles.baseStyles.bordersComponent,
        ...AppStyles.baseStyles.paddings.HorizontalMS,
        borderColor: theme.colors.border,
        backgroundColor: theme.colors.surface,
        maxWidth: AppStyles.defaultValues.largeComponentWidth,
        height: AppStyles.defaultValues.defaultComponentHeight,
        maxHeight: AppStyles.defaultValues.defaultComponentHeight,
      },
    },
  };
}
