import {
  CreateRequirementDocument,
  CreateRequirementMutation,
  CreateRequirementMutationVariables,
  DeleteRequirementDocument,
  DeleteRequirementMutation,
  DeleteRequirementMutationVariables,
  Requirement,
  GetRequirementDocument,
  GetRequirementQuery,
  GetRequirementQueryVariables,
  GetRequirementsByOpportunityDocument,
  GetRequirementsByOpportunityQuery,
  GetRequirementsByOpportunityQueryVariables,
  MutationCreateRequirementArgs,
  MutationDeleteRequirementArgs,
  MutationUpdateRequirementArgs,
  QueryGetRequirementArgs,
  UpdateRequirementMutation,
  UpdateRequirementMutationVariables,
  UpdateRequirementDocument,
  QueryGetRequirementsByOpportunityArgs,
} from './codegen/types';
import urqlClient from './urql/CuratorUrqlClient';

class RequirementService {
  async createRequirement(input: MutationCreateRequirementArgs): Promise<Requirement> {
    const { data, error } = await urqlClient.executeMutation<
      CreateRequirementMutation,
      CreateRequirementMutationVariables
    >(CreateRequirementDocument, input);
    if (error) throw error;
    return data!.createRequirement as Requirement;
  }

  async getRequirement(input: QueryGetRequirementArgs): Promise<Requirement> {
    const { data, error } = await urqlClient.executeQuery<GetRequirementQuery, GetRequirementQueryVariables>(
      GetRequirementDocument,
      input,
    );
    if (error) throw error;
    return data!.getRequirement as Requirement;
  }

  async getRequirementsByOpportunity(input: QueryGetRequirementsByOpportunityArgs): Promise<Requirement[]> {
    const { data, error } = await urqlClient.executeQuery<
      GetRequirementsByOpportunityQuery,
      GetRequirementsByOpportunityQueryVariables
    >(GetRequirementsByOpportunityDocument, input);
    if (error) throw error;
    return data!.getRequirementsByOpportunity as Requirement[];
  }

  async updateRequirement(input: MutationUpdateRequirementArgs): Promise<Requirement> {
    const { data, error } = await urqlClient.executeMutation<
      UpdateRequirementMutation,
      UpdateRequirementMutationVariables
    >(UpdateRequirementDocument, input);
    if (error) throw error;
    return data!.updateRequirement as Requirement;
  }

  async deleteRequirement(input: MutationDeleteRequirementArgs): Promise<boolean> {
    const { data, error } = await urqlClient.executeMutation<
      DeleteRequirementMutation,
      DeleteRequirementMutationVariables
    >(DeleteRequirementDocument, input);
    if (error) throw error;
    return data!.deleteRequirement;
  }
}

export const requirementService = new RequirementService();
