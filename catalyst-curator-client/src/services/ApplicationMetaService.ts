import {
  ApplicationMeta,
  CreateApplicationMetaDocument,
  CreateApplicationMetaMutation,
  CreateApplicationMetaMutationVariables,
  DeleteApplicationMetaDocument,
  DeleteApplicationMetaMutation,
  DeleteApplicationMetaMutationVariables,
  GetApplicationMetaDocument,
  GetApplicationMetaQuery,
  GetApplicationMetaQueryVariables,
  MutationCreateApplicationMetaArgs,
  MutationDeleteApplicationMetaArgs,
  MutationUpdateApplicationMetaArgs,
  QueryGetApplicationMetaArgs,
  UpdateApplicationMetaDocument,
  UpdateApplicationMetaMutation,
  UpdateApplicationMetaMutationVariables,
} from './codegen/types';
import urqlClient from './urql/CuratorUrqlClient';

class ApplicationMetaService {
  resetCache: boolean = false;

  async getApplicationMeta(input: QueryGetApplicationMetaArgs) {
    const { data, error } = await urqlClient.executeQuery<GetApplicationMetaQuery, GetApplicationMetaQueryVariables>(
      GetApplicationMetaDocument,
      input,
    );
    if (error) throw error;
    return data!.getApplicationMeta as ApplicationMeta;
  }

  async createApplicationMeta(input: MutationCreateApplicationMetaArgs) {
    const { data, error } = await urqlClient.executeMutation<
      CreateApplicationMetaMutation,
      CreateApplicationMetaMutationVariables
    >(CreateApplicationMetaDocument, input);
    if (error) throw error;
    return data!.createApplicationMeta as ApplicationMeta;
  }

  async updateApplicationMeta(input: MutationUpdateApplicationMetaArgs) {
    const { data, error } = await urqlClient.executeMutation<
      UpdateApplicationMetaMutation,
      UpdateApplicationMetaMutationVariables
    >(UpdateApplicationMetaDocument, input);
    if (error) throw error;
    return data!.updateApplicationMeta as ApplicationMeta;
  }

  async deleteApplicationMeta(input: MutationDeleteApplicationMetaArgs) {
    const { data, error } = await urqlClient.executeMutation<
      DeleteApplicationMetaMutation,
      DeleteApplicationMetaMutationVariables
    >(DeleteApplicationMetaDocument, input);
    if (error) throw error;
    return !!data?.deleteApplicationMeta;
  }
}

export const applicationMetaService = new ApplicationMetaService();
