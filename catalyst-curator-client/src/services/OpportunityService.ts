import {
  GetOpportunityDocument,
  GetOpportunityQuery,
  GetOpportunityQueryVariables,
  GetOpportunitySearchResultDocument,
  GetOpportunitySearchResultQuery,
  GetOpportunitySearchResultQueryVariables,
  MutationDeleteOpportunityArgs,
  MutationUpdateOpportunityArgs,
  Opportunity,
  QueryGetOpportunityArgs,
  UpdateOpportunityDocument,
  UpdateOpportunityMutation,
  UpdateOpportunityMutationVariables,
} from './codegen/types';
import urqlClient from './urql/CuratorUrqlClient';

class OpportunityService {
  async getOpportunity(input: QueryGetOpportunityArgs): Promise<Opportunity> {
    const { data, error } = await urqlClient.executeQueryNoCache<GetOpportunityQuery, GetOpportunityQueryVariables>(
      GetOpportunityDocument,
      input,
    );
    if (error) throw error;
    return data!.getOpportunity as Opportunity;
  }

  async getOpportunitySearchResult(input: QueryGetOpportunityArgs): Promise<Partial<Opportunity>> {
    const { data, error } = await urqlClient.executeQueryNoCache<
      GetOpportunitySearchResultQuery,
      GetOpportunitySearchResultQueryVariables
    >(GetOpportunitySearchResultDocument, input);
    if (error) throw error;
    return data!.getOpportunity as Partial<Opportunity>;
  }

  async updateOpportunity(input: MutationUpdateOpportunityArgs): Promise<Opportunity> {
    const { data, error } = await urqlClient.executeMutation<
      UpdateOpportunityMutation,
      UpdateOpportunityMutationVariables
    >(UpdateOpportunityDocument, input);
    if (error) throw error;
    return data!.updateOpportunity as Opportunity;
  }
}

export const opportunityService = new OpportunityService();
