import {
  AddLinkDocument,
  DeleteLinkDocument,
  UpdateLinkDocument,
  Link,
  AddLinkMutation,
  AddLinkMutationVariables,
  DeleteLinkMutation,
  DeleteLinkMutationVariables,
  UpdateLinkMutation,
  UpdateLinkMutationVariables,
  MutationAddLinkArgs,
  MutationDeleteLinkArgs,
  MutationUpdateLinkArgs,
} from './codegen/types';
import urqlClient from './urql/CuratorUrqlClient';

class LinkService {
  async addLink(input: MutationAddLinkArgs): Promise<Link> {
    const { data, error } = await urqlClient.executeMutation<AddLinkMutation, AddLinkMutationVariables>(
      AddLinkDocument,
      input,
    );
    if (error) throw error;
    return data!.addLink as Link;
  }

  async updateLink(input: MutationUpdateLinkArgs): Promise<Link> {
    const { data, error } = await urqlClient.executeMutation<UpdateLinkMutation, UpdateLinkMutationVariables>(
      UpdateLinkDocument,
      input,
    );
    if (error) throw error;
    return data!.updateLink as Link;
  }

  async deleteLink(input: MutationDeleteLinkArgs): Promise<boolean> {
    const { data, error } = await urqlClient.executeMutation<DeleteLinkMutation, DeleteLinkMutationVariables>(
      DeleteLinkDocument,
      input,
    );
    if (error) throw error;
    return data!.deleteLink;
  }
}

export const linkService = new LinkService();
