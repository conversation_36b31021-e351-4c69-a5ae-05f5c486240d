import {
  GetTenantDocument,
  GetTenantInfoDocument,
  GetTenantInfoQuery,
  GetTenantInfoQueryVariables,
  GetTenantQuery,
  GetTenantQueryVariables,
  QueryGetTenantArgs,
  QueryGetTenantInfoArgs,
  Tenant,
  TenantInfo,
} from './codegen/types';
import urqlClient from './urql/CuratorUrqlClient';

class TenantService {
  async getTenant(input: QueryGetTenantArgs): Promise<Tenant> {
    const { data, error } = await urqlClient.executeQuery<GetTenantQuery, GetTenantQueryVariables>(
      GetTenantDocument,
      input,
    );
    if (error) throw error;
    return data!.getTenant as Tenant;
  }

  async getTenantInfo(input: QueryGetTenantInfoArgs): Promise<TenantInfo> {
    const { data, error } = await urqlClient.executeQuery<GetTenantInfoQuery, GetTenantInfoQueryVariables>(
      GetTenantInfoDocument,
      input,
    );
    if (error) throw error;
    return data!.getTenantInfo as TenantInfo;
  }
}

export const tenantService = new TenantService();
