import {
  CreateOpportunityOwnerDocument,
  CreateOpportunityOwnerMutation,
  CreateOpportunityOwnerMutationVariables,
  GetOpportunityOwnerDocument,
  GetOpportunityOwnerQuery,
  GetOpportunityOwnerQueryVariables,
  MutationCreateOpportunityOwnerArgs,
  OpportunityOwnerStatus,
  QueryGetOpportunityOwnerArgs,
  QueryOpportunityOwnersDocument,
  QueryOpportunityOwnersQuery,
  QueryOpportunityOwnersQueryVariables,
  UpdateOpportunityOwnerDocument,
  UpdateOpportunityOwnerMutation,
  UpdateOpportunityOwnerMutationVariables,
  MutationUpdateOpportunityOwnerArgs,
  PagingInput,
  SearchSortInput,
  OpportunityOwnerPage,
} from './codegen/types';
import urqlClient from './urql/CuratorUrqlClient';

class OpportunityOwnerService {
  async createOpportunityOwner(input: MutationCreateOpportunityOwnerArgs): Promise<OpportunityOwnerStatus> {
    const { data, error } = await urqlClient.executeMutation<
      CreateOpportunityOwnerMutation,
      CreateOpportunityOwnerMutationVariables
    >(CreateOpportunityOwnerDocument, input);
    if (error) throw error;
    return data!.createOpportunityOwner as OpportunityOwnerStatus;
  }

  async getOpportunityOwner(input: QueryGetOpportunityOwnerArgs): Promise<OpportunityOwnerStatus | null> {
    const { data, error } = await urqlClient.executeQuery<GetOpportunityOwnerQuery, GetOpportunityOwnerQueryVariables>(
      GetOpportunityOwnerDocument,
      input,
    );
    if (error) throw error;
    return data!.getOpportunityOwner as OpportunityOwnerStatus | null;
  }

  async queryOpportunityOwners(input: {
    pagingInput?: PagingInput;
    searchSortInput?: SearchSortInput;
  }): Promise<OpportunityOwnerPage> {
    const { data, error } = await urqlClient.executeQueryNoCache<
      QueryOpportunityOwnersQuery,
      QueryOpportunityOwnersQueryVariables
    >(QueryOpportunityOwnersDocument, input);
    if (error) throw error;
    return data!.queryOpportunityOwners as OpportunityOwnerPage;
  }

  async updateOpportunityOwner(input: MutationUpdateOpportunityOwnerArgs): Promise<OpportunityOwnerStatus> {
    const { data, error } = await urqlClient.executeMutation<
      UpdateOpportunityOwnerMutation,
      UpdateOpportunityOwnerMutationVariables
    >(UpdateOpportunityOwnerDocument, input);
    if (error) throw error;
    return data!.updateOpportunityOwner as OpportunityOwnerStatus;
  }
}

export const opportunityOwnerService = new OpportunityOwnerService();
