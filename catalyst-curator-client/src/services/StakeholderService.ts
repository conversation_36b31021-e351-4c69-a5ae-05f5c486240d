import { OperationResult } from '@urql/core';
import {
  CreateOpportunityOwnerMutationVariables,
  CreateStakeholderDocument,
  CreateStakeholderMutation,
  DeleteStakeholderDocument,
  GetStakeholderDocument,
  MutationCreateStakeholderArgs,
  MutationUpdateStakeholderArgs,
  QueryGetStakeholderArgs,
  QueryQueryStakeholdersArgs,
  QueryStakeholdersDocument,
  Stakeholder,
  UpdateStakeholderDocument,
  UpdateStakeholderMutation,
  UpdateStakeholderMutationVariables,
} from './codegen/types';
import urqlClient from './urql/CuratorUrqlClient';

class StakeholderService {
  async getStakeholder(input: QueryGetStakeholderArgs): Promise<Stakeholder> {
    return urqlClient.executeQuery(GetStakeholderDocument, input).then((results: OperationResult) => {
      const { data, error } = results;
      return new Promise<Stakeholder>((resolve, reject) => {
        if (data) {
          return resolve(data.getStakeholder);
        }
        return reject(error);
      });
    });
  }

  async queryStakeholders(query: QueryQueryStakeholdersArgs): Promise<Array<Stakeholder>> {
    return urqlClient
      .executeQueryNoCache(QueryStakeholdersDocument, {
        pagingInput: query.pagingInput,
        searchSortInput: query.searchSortInput,
      })
      .then((results: OperationResult) => {
        const { data, error } = results;
        return new Promise<Array<Stakeholder>>((resolve, reject) => {
          if (data) {
            return resolve(data.queryStakeholders.results);
          }
          return reject(error);
        });
      });
  }

  async createStakeholder(input: MutationCreateStakeholderArgs): Promise<Stakeholder> {
    const { data, error } = await urqlClient.executeMutation<
      CreateStakeholderMutation,
      CreateOpportunityOwnerMutationVariables
    >(CreateStakeholderDocument, input);

    if (error) throw error;
    return data!.createStakeholder as Stakeholder;
  }

  async updateStakeholder(input: MutationUpdateStakeholderArgs): Promise<Stakeholder> {
    const { data, error } = await urqlClient.executeMutation<
      UpdateStakeholderMutation,
      UpdateStakeholderMutationVariables
    >(UpdateStakeholderDocument, input);

    if (error) throw error;
    return data!.updateStakeholder as Stakeholder;
  }

  async deleteStakeholder(id: string): Promise<boolean> {
    return urqlClient.executeMutation(DeleteStakeholderDocument, { id }).then((results: OperationResult) => {
      const { data, error } = results;
      return new Promise<boolean>((resolve, reject) => {
        if (data) {
          return resolve(data.deleteStakeholder);
        }
        return reject(error);
      });
    });
  }
}

export const stakeholderService = new StakeholderService();
