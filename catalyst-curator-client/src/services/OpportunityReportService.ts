import {
  GetOpportunitiesReportDocument,
  GetOpportunitiesReportQuery,
  GetOpportunitiesReportQueryVariables,
  QueryReportArgs,
  ReportResponse,
} from './codegen/types';
import urqlClient from './urql/CuratorUrqlClient';

class ReportService {
  async getOpportunitiesReport(input: QueryReportArgs) {
    const { data, error } = await urqlClient.executeQuery<
      GetOpportunitiesReportQuery,
      GetOpportunitiesReportQueryVariables
    >(GetOpportunitiesReportDocument, input);
    if (error) throw error;
    return data!.report as ReportResponse;
  }
}

export const opportunityReportService = new ReportService();
