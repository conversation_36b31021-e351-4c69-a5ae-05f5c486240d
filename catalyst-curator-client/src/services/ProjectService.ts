import {
  CreateProjectFromOpportunityDocument,
  CreateProjectFromOpportunityMutation,
  CreateProjectFromOpportunityMutationVariables,
  DeleteProjectDocument,
  DeleteProjectMutation,
  DeleteProjectMutationVariables,
  GetProjectDocument,
  GetProjectQuery,
  GetProjectQueryVariables,
  MutationCreateProjectFromOpportunityArgs,
  MutationDeleteProjectArgs,
  MutationUpdateProjectArgs,
  Project,
  ProjectPage,
  QueryGetProjectArgs,
  QueryProjectsDocument,
  QueryProjectsQuery,
  QueryProjectsQueryVariables,
  QueryQueryProjectsArgs,
  UpdateProjectDocument,
  UpdateProjectMutation,
  UpdateProjectMutationVariables,
} from './codegen/types';
import urqlClient from './urql/CuratorUrqlClient';

class ProjectService {
  async getProject(input: QueryGetProjectArgs): Promise<Project> {
    const { data, error } = await urqlClient.executeQueryNoCache<GetProjectQuery, GetProjectQueryVariables>(
      GetProjectDocument,
      input,
    );
    if (error) throw error;
    return data!.getProject as Project;
  }

  async createProjectFromOpportunity(input: MutationCreateProjectFromOpportunityArgs): Promise<Project> {
    const { data, error } = await urqlClient.executeMutation<
      CreateProjectFromOpportunityMutation,
      CreateProjectFromOpportunityMutationVariables
    >(CreateProjectFromOpportunityDocument, input);
    if (error) throw error;
    return data!.createProjectFromOpportunity as Project;
  }

  async updateProject(input: MutationUpdateProjectArgs): Promise<Partial<Project>> {
    const { data, error } = await urqlClient.executeMutation<UpdateProjectMutation, UpdateProjectMutationVariables>(
      UpdateProjectDocument,
      input,
    );
    if (error) throw error;
    return data!.updateProject as Partial<Project>;
  }

  async queryProject(input: QueryQueryProjectsArgs): Promise<ProjectPage> {
    const { data, error } = await urqlClient.executeQueryNoCache<QueryProjectsQuery, QueryProjectsQueryVariables>(
      QueryProjectsDocument,
      input,
    );
    if (error) throw error;
    return data!.queryProjects as ProjectPage;
  }
}

export const projectService = new ProjectService();
