import { OperationResult } from '@urql/core';
import {
  AddCategoryDocument,
  Category,
  GetAttachmentLocationQueryVariables,
  GetCategoriesDocument,
  GetCategoriesQuery,
  GetCategoriesQueryVariables,
  GetCategoryDocument,
  GetCategoryQuery,
  GetCategoryQueryVariables,
  QueryGetCategoryArgs,
} from './codegen/types';
import urqlClient from './urql/CuratorUrqlClient';

class CategoryService {
  async getCategory(input: QueryGetCategoryArgs): Promise<Category> {
    const { data, error } = await urqlClient.executeQuery<GetCategoryQuery, GetCategoryQueryVariables>(
      GetCategoryDocument,
      input,
    );
    if (error) throw error;
    return data!.getCategory as Category;
  }

  getCategories(pageSize: Number, searchValue: string): Promise<Array<Category>> {
    return urqlClient
      .executeQueryNoCache(GetCategoriesDocument, { pageSize, searchValue })
      .then((results: OperationResult) => {
        const { data, error } = results;
        return new Promise<Array<Category>>((resolve, reject) => {
          if (data) {
            return resolve(data.queryCategories.results);
          }
          return reject(error);
        });
      });
  }

  addCategory(name: string): Promise<Category> {
    return urqlClient.executeMutation(AddCategoryDocument, { name }).then((results: OperationResult) => {
      const { data, error } = results;
      return new Promise<Category>((resolve, reject) => {
        if (data) {
          return resolve(data.createCategory);
        }
        return reject(error);
      });
    });
  }
}

export const categoryService = new CategoryService();
