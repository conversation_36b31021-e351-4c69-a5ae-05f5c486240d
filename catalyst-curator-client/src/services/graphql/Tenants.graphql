query getTenant($id: String, $handleOrAlias: String) {
  getTenant(id: $id, handleOrAlias: $handleOrAlias) {
    id
    name
  }
}

query getTenantInfo($handleOrAlias: String) {
  getTenantInfo(handleOrAlias: $handleOrAlias) {
    tenantId
    name
    serverVersion
    meta {
      config
      theme
      content
      filterOtherPrivateOpportunities
    }
  }
}

query queryTenants($searchSortInput: SearchSortInput, $pagingInput: PagingInput) {
  queryTenants(searchSortInput: $searchSortInput, pagingInput: $pagingInput) {
    results {
      id
      name
      meta {
        config
        content
        filterOtherPrivateOpportunities
      }
    }
  }
}
