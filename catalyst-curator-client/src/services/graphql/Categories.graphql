mutation addCategory($name: String!) {
  createCategory(input: { name: $name }) {
    id
    name
  }
}

query getCategory($id: String, $name: String) {
  getCategory(id: $id, name: $name) {
    id
    name
  }
}

query getCategories($pageSize: Int = 10, $searchValue: AnyScalar!) {
  queryCategories(
    pagingInput: { pageSize: $pageSize }
    searchSortInput: {
      sortFields: [{ fieldName: "name", ascending: true }]
      searchFields: [{ fieldNames: ["name"], operator: MATCH, searchValue: $searchValue }]
    }
  ) {
    results {
      id
      name
    }
  }
}
