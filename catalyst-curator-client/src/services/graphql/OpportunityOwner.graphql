mutation createOpportunityOwner($input: CreateOpportunityOwnerInput!) {
  createOpportunityOwner(input: $input) {
    status
    statusSetPreviousAt
    statusSetRemovedAt
    isRemoved
    owner {
      organizationRole
      user {
        id
        emailAddress
        firstName
        lastName
        org1
        org2
        org3
        org4
        phone
        altContact
      }
    }
  }
}

query queryOpportunityOwners($pagingInput: PagingInput, $searchSortInput: SearchSortInput) {
  queryOpportunityOwners(pagingInput: $pagingInput, searchSortInput: $searchSortInput) {
    results {
      id
      status
      statusSetPreviousAt
      statusSetRemovedAt
      isRemoved
      createdAt
      owner {
        organizationRole
        user {
          id
          emailAddress
          firstName
          lastName
          org1
          org2
          org3
          org4
          phone
          altContact
        }
      }
    }
  }
}

query getOpportunityOwner($id: String!) {
  getOpportunityOwner(id: $id) {
    status
    statusSetPreviousAt
    statusSetRemovedAt
    isRemoved
    owner {
      organizationRole
      user {
        id
        emailAddress
        firstName
        lastName
        org1
        org2
        org3
        org4
        phone
        altContact
      }
    }
  }
}

mutation updateOpportunityOwner($id: String!, $input: UpdateOpportunityOwnerInput!) {
  updateOpportunityOwner(id: $id, input: $input) {
    status
    statusSetPreviousAt
    statusSetRemovedAt
    isRemoved
  }
}
