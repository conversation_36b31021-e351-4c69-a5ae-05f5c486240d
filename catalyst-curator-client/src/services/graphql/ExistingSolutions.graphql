mutation createExistingSolution($input: CreateExistingSolutionInput!, $links: ExistingSolutionLinks!) {
  createExistingSolution(input: $input, links: $links) {
    id
    source
    title
    organization
    needsModification
    createdAt
    updatedAt
  }
}

query getExistingSolution($id: String!) {
  getExistingSolution(id: $id) {
    id
    source
    title
    organization
    needsModification
    createdAt
    updatedAt
    opportunity {
      id
      title
    }
  }
}

query getExistingSolutionsByOpportunity($opportunityId: String!) {
  getExistingSolutionsByOpportunity(opportunityId: $opportunityId) {
    id
    source
    title
    organization
    needsModification
    createdAt
    updatedAt
  }
}

mutation updateExistingSolution($id: String!, $input: UpdateExistingSolutionInput!) {
  updateExistingSolution(id: $id, input: $input) {
    id
    source
    title
    organization
    needsModification
    updatedAt
  }
}

mutation deleteExistingSolution($id: String!) {
  deleteExistingSolution(id: $id)
}
