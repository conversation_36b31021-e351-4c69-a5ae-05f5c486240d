mutation createRequirement($input: CreateRequirementInput!, $links: RequirementLinks!) {
  createRequirement(input: $input, links: $links) {
    id
    source
    title
    poc
    createdAt
    updatedAt
  }
}

query getRequirement($id: String!) {
  getRequirement(id: $id) {
    id
    source
    title
    poc
    createdAt
    updatedAt
    opportunity {
      id
      title
    }
  }
}

query getRequirementsByOpportunity($opportunityId: String!) {
  getRequirementsByOpportunity(opportunityId: $opportunityId) {
    id
    source
    title
    poc
    createdAt
    updatedAt
  }
}

mutation updateRequirement($id: String!, $input: UpdateRequirementInput!) {
  updateRequirement(id: $id, input: $input) {
    id
    source
    title
    poc
    updatedAt
  }
}

mutation deleteRequirement($id: String!) {
  deleteRequirement(id: $id)
}
