query getApplicationMeta($id: String!) {
  getApplicationMeta(id: $id) {
    id
    curationMeta
  }
}

mutation createApplicationMeta($input: ApplicationMetaInput!) {
  createApplicationMeta(input: $input) {
    id
    curationMeta
  }
}

mutation updateApplicationMeta($input: ApplicationMetaInput!, $id: String!) {
  updateApplicationMeta(input: $input, id: $id) {
    id
    curationMeta
  }
}

mutation deleteApplicationMeta($id: String!) {
  deleteApplicationMeta(id: $id)
}
