mutation createStakeholder($input: CreateStakeholderInput!) {
  createStakeholder(input: $input) {
    id
    firstName
    lastName
    title
    emailAddress
    altEmailAddress
    phone
    org
    organizationRole
  }
}

query getStakeholder($id: String!) {
  getStakeholder(id: $id) {
    id
    firstName
    lastName
    title
    emailAddress
    altEmailAddress
    phone
    org
    organizationRole
  }
}

query queryStakeholders($pagingInput: PagingInput, $searchSortInput: SearchSortInput) {
  queryStakeholders(pagingInput: $pagingInput, searchSortInput: $searchSortInput) {
    results {
      id
      firstName
      lastName
      title
      emailAddress
      altEmailAddress
      phone
      org
      organizationRole
    }
  }
}

mutation updateStakeholder($id: String!, $input: UpdateStakeholderInput!) {
  updateStakeholder(id: $id, input: $input) {
    id
    org
    firstName
    lastName
    title
    phone
    emailAddress
    altEmailAddress
    organizationRole
  }
}

mutation deleteStakeholder($id: String!) {
  deleteStakeholder(id: $id)
}
