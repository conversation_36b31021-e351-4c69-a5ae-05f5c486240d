mutation registerUser($input: RegisterUserInput!) {
  register(input: $input) {
    user {
      id
    }
    token
  }
}

mutation userLogin($tenantHandle: String!, $password: String!, $userName: String!) {
  login(tenantHandle: $tenantHandle, password: $password, userName: $userName) {
    user {
      id
      emailAddress
      status
      firstName
      lastName
      org1
      org2
      org3
      org4
      phone
      altContact
      roles {
        id
        name
      }
      appMeta {
        id
        curationMeta
      }
      options {
        lastUsedServerVersion
      }
      privilegeGroups {
        id
        name
        privileges {
          id
          name
          resourceId
          resourceType
        }
      }
    }
    token
    expiresAt
  }
}

mutation renewToken {
  renew {
    token
    expiresAt
  }
}

mutation updateCurrentUser($input: UpdateCurrentUserInput!, $links: UpdateCurrentUserLinks) {
  updateCurrentUser(input: $input, links: $links) {
    id
  }
}

mutation deleteCurrentUser {
  deleteCurrentUser
}

query getCurrentUserStatus {
  getCurrentUser {
    id
    status
  }
}

query getCurrentUserInformation {
  getCurrentUser {
    id
    emailAddress
    status
    firstName
    lastName
    org1
    org2
    org3
    org4
    phone
    altContact
    roles {
      id
      name
    }
    appMeta {
      id
      curationMeta
    }
    options {
      lastUsedServerVersion
    }
    privilegeGroups {
      id
      name
      privileges {
        id
        name
        resourceId
        resourceType
      }
    }
  }
}

query queryUsers($searchSortInput: SearchSortInput, $pagingInput: PagingInput) {
  queryUsers(searchSortInput: $searchSortInput, pagingInput: $pagingInput) {
    results {
      id
      emailAddress
      status
      firstName
      lastName
      org1
      org2
      org3
      org4
      phone
      altContact
      roles {
        id
        name
      }
      owner {
        organizationRole
      }
    }
    pageInfo {
      hasNext
      hasPrevious
      lastCursor
      lastPageSize
      retrievedCount
      totalCount
    }
  }
}

mutation deleteUser($id: String!) {
  deleteUser(id: $id)
}

query getUser($id: String!) {
  getUser(id: $id) {
    id
    emailAddress
    firstName
    lastName
    org1
    org2
    org3
    org4
    phone
    status
    roles {
      id
      name
    }
  }
}

mutation createUser($input: CreateUserInput!, $links: UserLinks) {
  createUser(input: $input, links: $links) {
    id
    emailAddress
    firstName
    lastName
    org1
    org2
    org3
    org4
    status
    phone
    roles {
      id
      name
    }
  }
}

mutation updateUser($input: UpdateUserInput!, $id: String!, $links: UserLinks) {
  updateUser(input: $input, id: $id, links: $links) {
    id
    emailAddress
    firstName
    lastName
    org1
    org2
    org3
    org4
    status
    phone
    roles {
      id
      name
    }
  }
}
