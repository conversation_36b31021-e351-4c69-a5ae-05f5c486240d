mutation uploadAttachment($input: Upload!, $links: AttachmentLinks!) {
  addAttachment(input: $input, links: $links) {
    id
    name
    mimetype
    encoding
    displayName
    notes
  }
}

query getAttachmentLocation($id: String!) {
  getAttachmentLocation(id: $id) {
    location
  }
}

mutation deleteAttachment($id: String!) {
  deleteAttachment(id: $id)
}

mutation updateAttachment($input: UpdateAttachmentInput!) {
  updateAttachment(input: $input) {
    id
    name
    displayName
    notes
  }
}
