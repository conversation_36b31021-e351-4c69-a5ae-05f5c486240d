import {
  CalcOpportunitiesDocument,
  CalcOpportunitiesQuery,
  CalcOpportunitiesQueryVariables,
  CalcResponse,
  QueryOpportunityCalculationArgs,
} from './codegen/types';
import urqlClient from './urql/CuratorUrqlClient';

class OpportunityCalculationService {
  async getOpportunityCalculation(input: QueryOpportunityCalculationArgs) {
    const { data, error } = await urqlClient.executeQuery<CalcOpportunitiesQuery, CalcOpportunitiesQueryVariables>(
      CalcOpportunitiesDocument,
      input,
    );
    if (error) throw error;
    return data!.opportunityCalculation as CalcResponse;
  }
}

export const opportunityCalculationService = new OpportunityCalculationService();
