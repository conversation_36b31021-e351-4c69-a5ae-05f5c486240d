import urqlClient from './urql/CuratorUrqlClient';
import {
  FilterOpportunitiesDocument,
  FilterOpportunitiesQuery,
  FilterOpportunitiesQueryVariables,
  FindOpportunitiesDocument,
  FindOpportunitiesQuery,
  FindOpportunitiesQueryVariables,
} from './codegen/types';
import { OperationResult } from '@urql/core';

class ListOpportunityService {
  filterOpportunitiesList(filterOperation: FilterOpportunitiesQueryVariables): Promise<FilterOpportunitiesQuery> {
    return urqlClient
      .executeQueryNoCache(FilterOpportunitiesDocument, filterOperation)
      .then((results: OperationResult) => {
        const { data, error } = results;
        return new Promise<FilterOpportunitiesQuery>((resolve, reject) => {
          if (data) {
            return resolve(data);
          }
          return reject(error);
        });
      });
  }

  findOpportunities(filterOperation: FindOpportunitiesQueryVariables): Promise<FindOpportunitiesQuery> {
    return urqlClient
      .executeQueryNoCache(FindOpportunitiesDocument, filterOperation)
      .then((results: OperationResult) => {
        const { data, error } = results;
        return new Promise<FindOpportunitiesQuery>((resolve, reject) => {
          if (data) {
            return resolve(data);
          }
          return reject(error);
        });
      });
  }
}

export const listOpportunityService = new ListOpportunityService();
