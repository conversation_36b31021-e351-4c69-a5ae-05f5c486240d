import { DocumentNode } from 'graphql';
import urqlClient from './urql/CuratorUrqlClient';

class QueryService {
  async query<ARGS, QUERY, VARIABLES extends {}>(document: DocumentNode, input?: ARGS, noCache = false) {
    const { data, error } = noCache
      ? await urqlClient.executeQueryNoCache<QUERY, VARIABLES>(document, input)
      : await urqlClient.executeQuery<QUERY, VARIABLES>(document, input);
    if (error) throw error;
    return data;
  }

  async mutation<ARGS, MUTATION, VARIABLES extends {}>(document: DocumentNode, input?: ARGS) {
    const { data, error } = await urqlClient.executeMutation<MUTATION, VARIABLES>(document, input);
    if (error) throw error;
    return data;
  }

  async multipart<ARGS, MUTATION, VARIABLES extends {}>(document: DocumentNode, input?: ARGS) {
    const { data, error } = await urqlClient.executeMultipartExchangeMutation<MUTATION, VARIABLES>(document, input);
    if (error) throw error;
    return data;
  }
}

export const queryService = new QueryService();
