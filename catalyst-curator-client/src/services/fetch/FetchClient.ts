import urqlClient from '../urql/CuratorUrqlClient';

export type FetchMethod = 'GET' | 'POST';
export default class FetchClient {
  get(url: string, headers?: [string, string][] | Record<string, string> | Headers): Promise<Response> {
    return this.fetch(url, 'GET', headers);
  }

  post(
    url: string,
    headers?: [string, string][] | Record<string, string> | Headers,
    body?: BodyInit,
  ): Promise<Response> {
    return this.fetch(url, 'POST', headers, body);
  }

  async fetch(
    url: string,
    method: FetchMethod = 'GET',
    headers?: [string, string][] | Record<string, string> | Headers,
    body?: BodyInit,
  ): Promise<Response> {
    try {
      return await fetch(url, {
        mode: 'cors',
        method: method,
        headers: {
          Authorization: `Bearer ${urqlClient.token}`,
          ...(headers ? headers : []),
        },
        body,
      });
    } catch (error) {
      console.error(error);
      throw error;
    }
  }
}
