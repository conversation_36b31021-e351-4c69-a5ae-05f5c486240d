import { StackScreenProps } from '@react-navigation/stack';
import { Component } from 'react';
import { MainStackParamList } from '../routing/screens';
import { DashboardPage } from '../pages/DashboardPage';
import { PlatformFactory } from '../platform/PlatformFactory';
import { StoresProvider } from '../lib/stores/StoresProvider';
import { AppContent } from '../lib';
import { AppFooter } from '../appComponents/AppFooter';
import { CuratorStores } from '../platform/initializers';
import { View } from 'react-native';
import { withTheme } from 'react-native-paper';
import { Wait } from '../lib/ui/molecules/Wait';
import { WelcomeDialog } from '../pages/WelcomeDialog';
import { WebDrawer } from '../appComponents/web/WebDrawer';
import { OpportunityListStoreNames } from '../stores/OpportunityListStore';

type DashboardRouteProps = StackScreenProps<MainStackParamList, 'dashboard'>;
class DashboardRoute extends Component<DashboardRouteProps & { theme: ReactNativePaper.ThemeProp }> {
  #unsubscribe?: () => void;
  render() {
    const storesProvider = StoresProvider.get<CuratorStores>();
    const opportunityListStore = storesProvider.getStore('OpportunityListStore');
    const router = PlatformFactory.getRouter<MainStackParamList>(this.props.navigation);
    const userStore = storesProvider.getStore('UserStore');
    const tenantStore = storesProvider.getStore('TenantStore');

    const {
      styles: { components },
    } = this.props.theme;
    const dashboardProps = {
      opportunityListStore,
      router,
      userStore,
      tenantStore,
    };

    return (
      // User store and meta store should already be initialized here
      <Wait until={() => opportunityListStore.initialized}>
        <AppContent {...{ noScroll: true }}>
          <View style={[components.pageContainerConstraints, components.flexAll]}>
            <WelcomeDialog {...{ userStore, tenantStore }} />
            <WebDrawer {...dashboardProps} currentRouteName="dashboard" />
            <View style={[components.pageContentConstraints, components.flexAll]}>
              <DashboardPage {...dashboardProps} />
              <AppFooter />
            </View>
          </View>
        </AppContent>
      </Wait>
    );
  }

  componentDidMount() {
    const storesProvider = StoresProvider.get<CuratorStores>();
    this.#unsubscribe = this.props.navigation.addListener('focus', async () => {
      const opportunityListStore = storesProvider.getStore('OpportunityListStore');
      opportunityListStore.storeName = OpportunityListStoreNames.OpportunityListStore;
      opportunityListStore.restoreCacheFiltersFor(OpportunityListStoreNames.OpportunityListStore);
      const userMetaStore = storesProvider.getStore('UserStore').userMetaStore;
      const sortedCol = userMetaStore.oppTable?.initialSortCol;
      if (sortedCol)
        opportunityListStore.setSortedColumn({ fieldName: sortedCol.colId, ascending: !sortedCol.descending });

      const resourcesStore = storesProvider.getStore('ResourcesStore');
      resourcesStore.clear();
      await opportunityListStore?.initialize();
    });
  }

  componentWillUnmount() {
    this.#unsubscribe && this.#unsubscribe();
  }
}

export default withTheme(DashboardRoute);
