import { StackHeaderProps } from '@react-navigation/stack';
import React from 'react';
import { AppHeader } from '../appComponents/header/AppHeader';
import { CuratorStores } from '../platform/initializers';
import { PlatformFactory } from '../platform/PlatformFactory';
import { StoresProvider } from '../lib/stores/StoresProvider';
import { MainStackParamList } from '../routing/screens';
import getHeaderConfig from './getHeaderConfig';

export default function (props: StackHeaderProps): React.ReactNode {
  const storesProvider = StoresProvider.get<CuratorStores>();
  const userStore = storesProvider.getStore('UserStore');
  // additionl router props are available here if needed
  const { route, navigation, back } = props;
  const router = PlatformFactory.getRouter(navigation);
  const appHeaderProps = getHeaderConfig(route.name as keyof MainStackParamList);

  return <AppHeader {...appHeaderProps} previousTitle={back?.title} router={router} />;
}
