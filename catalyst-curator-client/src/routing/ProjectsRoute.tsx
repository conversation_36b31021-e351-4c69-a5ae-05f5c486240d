import { StackScreenProps } from '@react-navigation/stack';
import React, { Component } from 'react';
import { MainStackParamList } from './screens';
import { DashboardPage } from '../pages/DashboardPage';
import { PlatformFactory } from '../platform/PlatformFactory';
import { StoresProvider } from '../lib/stores/StoresProvider';
import { AppContent } from '../lib';
import { AppFooter } from '../appComponents/AppFooter';
import { CuratorStores } from '../platform/initializers';
import { ProjectsPage } from '../pages/ProjectsPage';
import { View } from 'react-native';
import { withTheme } from 'react-native-paper';
import { WebDrawer } from '../appComponents/web/WebDrawer';

type ProjectsRouteProps = StackScreenProps<MainStackParamList, 'projects'>;
class ProjectsRoute extends Component<ProjectsRouteProps & { theme: ReactNativePaper.ThemeProp }> {
  #unsubscribe?: () => void;

  render() {
    const storesProvider = StoresProvider.get<CuratorStores>();
    const projectsStore = storesProvider.getStore('ProjectsStore');
    const router = PlatformFactory.getRouter<MainStackParamList>(this.props.navigation);
    const userStore = storesProvider.getStore('UserStore');
    const tenantStore = storesProvider.getStore('TenantStore');
    const {
      styles: { components },
    } = this.props.theme;
    const projectsProps = {
      projectsStore,
      router,
      userStore,
    };

    return (
      <AppContent {...{ noScroll: false }}>
        <View style={[components.pageContainerConstraints, components.flexAll]}>
          <WebDrawer {...projectsProps} tenantStore={tenantStore} currentRouteName="projects" />
          <View style={[components.pageContentConstraints, components.flexAll]}>
            <ProjectsPage {...projectsProps} />
            <AppFooter />
          </View>
        </View>
      </AppContent>
    );
  }

  componentDidMount() {
    const storesProvider = StoresProvider.get<CuratorStores>();
    this.#unsubscribe = this.props.navigation.addListener('focus', () => {
      const projectsStore = storesProvider.getStore('ProjectsStore');
      const sortedCol = storesProvider.getStore('UserStore').userMetaStore.projTable?.initialSortCol;
      if (sortedCol) projectsStore.setSortedColumn({ fieldName: sortedCol.colId, ascending: !sortedCol.descending });
      projectsStore.queryItems(projectsStore.returnPageSize);
      projectsStore?.initialize();
    });
  }

  componentWillUnmount() {
    this.#unsubscribe && this.#unsubscribe();
  }
}

export default withTheme(ProjectsRoute);
