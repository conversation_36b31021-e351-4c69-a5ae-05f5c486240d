import { NavigatorScreenParams } from '@react-navigation/native';
import { createStackNavigator, StackHeaderProps } from '@react-navigation/stack';
import { CurationRoute, DashboardRoute, LoginRoute } from '.';
import getAppHeader from './getAppHeader';
import ProfileRoute from './ProfileRoute';
import ProjectRoute from './ProjectRoute';
import ProjectsRoute from './ProjectsRoute';
import { AnalyticsStackScreens } from './analytics/screens';

export type RootParamList = {};

export type RootStackParamList = {
  cAuth: NavigatorScreenParams<RootParamList>;
  cMain: NavigatorScreenParams<RootParamList>;
  notfound: undefined;
};

export type MainStackParamList = {
  login: undefined;
  dashboard: undefined;
  profile: { location?: 'basic' | 'advanced' | 'manage' };
  curation: { id: string };
  project: { id: string };
  projects: undefined;
  analytics: undefined;
};

export const MainStack = createStackNavigator<MainStackParamList>();
export const RootStack = createStackNavigator<RootStackParamList>();

export function LoginStackScreens(props: {}) {
  return (
    <MainStack.Navigator
      screenOptions={{
        cardStyle: { flex: 1 },
        header: (props: StackHeaderProps) => getAppHeader(props),
      }}
      initialRouteName={'login'}
    >
      <MainStack.Screen name={'login'} component={LoginRoute} options={{ title: 'Login' }} />
    </MainStack.Navigator>
  );
}

export function MainStackScreens(props: {}) {
  return (
    <MainStack.Navigator
      screenOptions={{
        cardStyle: { flex: 1 },
        headerShown: false,
        header: (props: StackHeaderProps) => getAppHeader(props),
      }}
      initialRouteName={'dashboard'}
    >
      <MainStack.Screen name={'dashboard'} component={DashboardRoute} options={{ title: 'Dashboard' }} />
      <MainStack.Screen name={'curation'} component={CurationRoute} options={{ title: 'Opportunity' }} />
      <MainStack.Screen name={'profile'} component={ProfileRoute} options={{ title: 'Profile' }} />
      <MainStack.Screen name={'projects'} component={ProjectsRoute} options={{ title: 'Projects' }} />
      <MainStack.Screen name={'project'} component={ProjectRoute} options={{ title: 'Project' }} />
      <MainStack.Screen name={'analytics'} component={AnalyticsStackScreens} options={{ title: 'Analytics' }} />
    </MainStack.Navigator>
  );
}
