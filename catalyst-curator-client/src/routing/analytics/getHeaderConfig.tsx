import React from 'react';
import { AnalyticsStackParamList } from './screens';

export default function <RouteName extends keyof AnalyticsStackParamList>(
  routeName: RouteName,
): { navGroup: string } | undefined {
  switch (routeName) {
    case 'overview':
      return {
        navGroup: 'Overview',
      };
    case 'reports':
      return {
        navGroup: 'Reports',
      };
    case 'table':
      return {
        navGroup: 'Table',
      };

    default:
      return undefined;
  }
}
