import { StackHeaderProps, createStackNavigator } from '@react-navigation/stack';
import TableRoute from './TableRoute';
import OverviewRoute from './OverviewRoute';
import getAnalyticsHeader from './getAnalyticsHeader';
import { StoresProvider } from '../../lib/stores/StoresProvider';
import { CuratorStores } from '../../platform/initializers';
import { useEffect } from 'react';

export type AnalyticsStackParamList = {
  overview: undefined;
  reports: undefined;
  table: undefined;
};

export const AnalyticsStack = createStackNavigator<AnalyticsStackParamList>();

export const AnalyticsStackScreens = () => {
  const storesProvider = StoresProvider.get<CuratorStores>();
  const userStore = storesProvider.getStore('UserStore');
  const resourcesStore = storesProvider.getStore('ResourcesStore');
  const tenantStore = storesProvider.getStore('TenantStore');
  const opportunityReportStore = storesProvider.getStore('OpportunityReportsStore');
  const opportunityListStore = storesProvider.getStore('OpportunityListStore');

  useEffect(() => {
    resourcesStore.setDefaultResources();
    opportunityReportStore?.initialize();
  }, []);

  return (
    <AnalyticsStack.Navigator
      screenOptions={{
        cardStyle: { flex: 1 },
        headerShown: false,
        header: (props: StackHeaderProps) =>
          getAnalyticsHeader(
            props,
            userStore,
            resourcesStore,
            opportunityReportStore,
            tenantStore,
            opportunityListStore,
          ),
      }}
      initialRouteName={'overview'}
    >
      <AnalyticsStack.Screen name={'overview'} component={OverviewRoute} options={{ title: 'Analytics Overview' }} />
      <AnalyticsStack.Screen name={'table'} component={TableRoute} options={{ title: 'Analytics Table' }} />
    </AnalyticsStack.Navigator>
  );
};
