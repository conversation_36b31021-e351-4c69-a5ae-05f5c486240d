import { StackScreenProps } from '@react-navigation/stack';
import { AppContent } from '../../lib';
import { TablePage } from '../../pages/analytics/TablePage';
import { MainStackParamList } from '../screens';
import { PlatformFactory } from '../../platform/PlatformFactory';
import { AppFooter } from '../../appComponents/AppFooter';
import { Component } from 'react';
import { Wait } from '../../lib/ui/molecules/Wait';
import { withTheme } from 'react-native-paper';
import { AnalyticsStackParamList } from './screens';
import { StoresProvider } from '../../lib/stores/StoresProvider';
import { CuratorStores } from '../../platform/initializers';
import { WebDrawer } from '../../appComponents/web/WebDrawer';
import { View } from 'react-native';
import { OpportunityListStoreNames } from '../../stores/OpportunityListStore';

export type TableRouteProps = StackScreenProps<AnalyticsStackParamList, 'table'>;
class TableRoute extends Component<TableRouteProps & { theme: ReactNativePaper.ThemeProp }> {
  // #unsubscribe?: () => void;

  render() {
    const storesProvider = StoresProvider.get<CuratorStores>();
    const userStore = storesProvider.getStore('UserStore');
    const tenantStore = storesProvider.getStore('TenantStore');
    const opportunityListStore = storesProvider.getStore('OpportunityListStore');
    const opportunityReportsStore = storesProvider.getStore('OpportunityReportsStore');

    const opportunityListFilterStore = opportunityListStore.listFilterStores;
    const mainStackRouter = PlatformFactory.getRouter<MainStackParamList>(this.props.navigation.getParent());
    const router = PlatformFactory.getRouter<AnalyticsStackParamList>(this.props.navigation);

    const {
      styles: { components },
    } = this.props.theme;

    const tableProps = {
      router,
      userStore,
      opportunityReportsStore,
      tenantStore,
      opportunityListStore,
      resourcesStore: storesProvider.getStore('ResourcesStore'),
      route: this.props.route,
    };
    return (
      <Wait until={() => opportunityListStore.initialized}>
        <AppContent {...{ noScroll: true }}>
          <View style={[components.pageContainerConstraints, components.flexAll]}>
            <WebDrawer
              userStore={userStore}
              router={mainStackRouter}
              tenantStore={tenantStore}
              currentRouteName="analytics"
            />
            <View style={[components.pageContentConstraints, components.flexAll]}>
              <TablePage
                {...tableProps}
                mainStackRouter={mainStackRouter}
                eventFilterInfo={opportunityListFilterStore.campaignInfoFilterStore}
              />
              <AppFooter />
            </View>
          </View>
        </AppContent>
      </Wait>
    );
  }

  componentDidMount() {
    const storesProvider = StoresProvider.get<CuratorStores>();
    const opportunityListStore = storesProvider.getStore('OpportunityListStore');
    opportunityListStore.storeName = OpportunityListStoreNames.AnalyticsListStore;
    opportunityListStore.restoreCacheFiltersFor(OpportunityListStoreNames.AnalyticsListStore);
    const sortedCol = storesProvider.getStore('UserStore').userMetaStore.anaTable?.initialSortCol;
    if (sortedCol) {
      opportunityListStore.setSortedColumn({ fieldName: sortedCol.colId, ascending: !sortedCol.descending });
    }
    opportunityListStore?.initialize();
  }
}

export default withTheme(TableRoute);
