import { StackScreenProps } from '@react-navigation/stack';
import { AppContent } from '../../lib';
import { AppFooter } from '../../appComponents/AppFooter';
import { Component } from 'react';
import { withTheme } from 'react-native-paper';
import { AnalyticsStackParamList } from './screens';
import { OverviewPage } from '../../pages/analytics/OverviewPage';
import { Wait } from '../../lib/ui/molecules/Wait';
import { StoresProvider } from '../../lib/stores/StoresProvider';
import { CuratorStores } from '../../platform/initializers';
import { PlatformFactory } from '../../platform/PlatformFactory';
import { MainStackParamList } from '../screens';
import { WebDrawer } from '../../appComponents/web/WebDrawer';
import { View } from 'react-native';

type OverviewRouteProps = StackScreenProps<AnalyticsStackParamList, 'overview'>;

class OverviewRoute extends Component<OverviewRouteProps & { theme: ReactNativePaper.ThemeProp }> {
  render() {
    const storesProvider = StoresProvider.get<CuratorStores>();
    const userStore = storesProvider.getStore('UserStore');
    const opportunityReportsStore = storesProvider.getStore('OpportunityReportsStore');
    const mainStackRouter = PlatformFactory.getRouter<MainStackParamList>(this.props.navigation.getParent());
    const router = PlatformFactory.getRouter<AnalyticsStackParamList>(this.props.navigation);
    const tenantStore = storesProvider.getStore('TenantStore');
    const {
      styles: { components },
    } = this.props.theme;
    const overviewProps = {
      router,
      userStore,
      opportunityReportsStore,
      tenantStore: tenantStore,
      opportunityListStore: storesProvider.getStore('OpportunityListStore'),
      resourcesStore: storesProvider.getStore('ResourcesStore'),
      route: this.props.route,
    };
    return (
      <Wait until={() => opportunityReportsStore.areReportsFetched}>
        <AppContent {...{ noScroll: true }}>
          <View style={[components.pageContainerConstraints, components.flexAll]}>
            <WebDrawer
              userStore={userStore}
              router={mainStackRouter}
              tenantStore={tenantStore}
              currentRouteName="analytics"
            />
            <View style={[components.pageContentConstraints, components.flexAll]}>
              <OverviewPage {...overviewProps} />
              <AppFooter />
            </View>
          </View>
        </AppContent>
      </Wait>
    );
  }

  componentDidMount(): void {
    const storesProvider = StoresProvider.get<CuratorStores>();
    const opportunityReportStore = storesProvider.getStore('OpportunityReportsStore');
    opportunityReportStore?.initialize();
  }
}

export default withTheme(OverviewRoute);
