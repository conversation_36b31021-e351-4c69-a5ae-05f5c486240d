import React from 'react';
import { AppHeaderProps } from '../appComponents/header/AppHeader';
import { MainStackParamList } from './screens';

export default function <RouteName extends keyof MainStackParamList>(
  routeName: RouteName,
): (Partial<AppHeaderProps> & { children?: React.ReactNode }) | undefined {
  switch (routeName) {
    case 'login':
      return {
        navGroup: 'Sign In',
        showBack: false,
      };
    case 'dashboard':
      return {
        navGroup: 'Opportunities',
        showBack: false,
      };
    case 'curation':
      return {
        navGroup: 'Opportunities',
        showBack: true,
      };
    case 'profile':
      return {
        navGroup: 'Profile',
        showBack: true,
      };
    case 'project':
      return {
        navGroup: 'Projects',
        showBack: true,
      };
    case 'projects':
      return {
        navGroup: 'Projects',
        showBack: false,
      };
    case 'analytics':
      return {
        navGroup: 'Analytics',
        showBack: false,
      };
    default:
      return undefined;
  }
}
