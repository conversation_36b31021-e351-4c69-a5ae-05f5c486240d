import { StackScreenProps } from '@react-navigation/stack';
import React, { Component } from 'react';
import { AppFooter } from '../appComponents/AppFooter';
import { AppContent } from '../lib';
import { ProfilePage } from '../pages/ProfilePage';
import { CuratorStores } from '../platform/initializers';
import { PlatformFactory } from '../platform/PlatformFactory';
import { StoresProvider } from '../lib/stores/StoresProvider';
import { MainStackParamList } from '../routing/screens';
import { withTheme } from 'react-native-paper';
import { View } from 'react-native';

import { WebDrawer } from '../appComponents/web/WebDrawer';

type ProfileRouteProps = StackScreenProps<MainStackParamList, 'profile'>;
class ProfileRoute extends Component<ProfileRouteProps & { theme: ReactNativePaper.ThemeProp }> {
  #unsubscribe?: () => void;

  render() {
    const router = PlatformFactory.getRouter<MainStackParamList>(this.props.navigation);
    const storesProvider = StoresProvider.get<CuratorStores>();
    const tenantStore = storesProvider.getStore('TenantStore');
    const {
      styles: { components },
    } = this.props.theme;
    const profileProps = {
      userStore: storesProvider.getStore('UserStore'),
      usersAdminStore: storesProvider.getStore('UsersAdminStore'),
      router,
      location: this.props.route?.params?.location,
    };

    return (
      <AppContent {...{ noScroll: false }}>
        <View style={[components.pageContainerConstraints, components.flexAll]}>
          <WebDrawer {...profileProps} tenantStore={tenantStore} currentRouteName="profile" />
          <View style={[components.pageContentConstraints, components.flexAll]}>
            <ProfilePage {...profileProps} />
            <AppFooter />
          </View>
        </View>
      </AppContent>
    );
  }

  componentDidMount() {
    const storesProvider = StoresProvider.get<CuratorStores>();
    // refersh the current userstore.  the usersstore is loaded lazily if the tab is clicked on.
    this.#unsubscribe = this.props.navigation.addListener('focus', () => {
      const userStore = storesProvider.getStore('UserStore');
      userStore.refreshCurrentUser();
    });
  }

  componentWillUnmount() {
    const storesProvider = StoresProvider.get<CuratorStores>();
    this.#unsubscribe && this.#unsubscribe();
  }
}

export default withTheme(ProfileRoute);
