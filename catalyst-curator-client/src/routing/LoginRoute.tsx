import { StackScreenProps } from '@react-navigation/stack';
import React, { Component } from 'react';
import { AppContent } from '../lib';
import { LoginPage } from '../pages/LoginPage';
import { CuratorStores } from '../platform/initializers';
import { PlatformFactory } from '../platform/PlatformFactory';
import { StoresProvider } from '../lib/stores/StoresProvider';
import { MainStackParamList } from '../routing/screens';
import { withTheme } from 'react-native-paper';
import { DoDDialog } from '../lib/ui/organisms/DoDDialog';

type LoginRouteProps = StackScreenProps<MainStackParamList, 'login'>;
class LoginRoute extends Component<LoginRouteProps & { theme: ReactNativePaper.ThemeProp }> {
  render() {
    const router = PlatformFactory.getRouter<MainStackParamList>(this.props.navigation);
    const storesProvider = StoresProvider.get<CuratorStores>();
    const {
      styles: { components },
    } = this.props.theme;
    const loginProps = {
      tenantStore: storesProvider.getStore('TenantStore'),
      applicationStore: storesProvider.getStore('ApplicationStore'),
      userStore: storesProvider.getStore('UserStore'),
      router,
    };
    return (
      <AppContent>
        <DoDDialog tenantStore={loginProps.tenantStore} />
        <LoginPage {...loginProps} />
      </AppContent>
    );
  }
}

export default withTheme(LoginRoute);
