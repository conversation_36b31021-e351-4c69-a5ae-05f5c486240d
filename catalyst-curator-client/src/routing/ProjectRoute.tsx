import { StackScreenProps } from '@react-navigation/stack';
import React, { Component } from 'react';
import { AppContent } from '../lib';
import { AppFooter } from '../appComponents/AppFooter';
import { ProjectPage } from '../pages/ProjectPage';
import { CuratorStores } from '../platform/initializers';
import { PlatformFactory } from '../platform/PlatformFactory';
import { StoresProvider } from '../lib/stores/StoresProvider';
import { MainStackParamList } from './screens';
import OpportunityStore from '../stores/OpportunityStore';
import { ProjectStore } from '../stores/ProjectStore';
import { View } from 'react-native';
import { withTheme } from 'react-native-paper';

import { WebDrawer } from '../appComponents/web/WebDrawer';

type ProjectRouteProps = StackScreenProps<MainStackParamList, 'project'>;
class ProjectRoute extends Component<ProjectRouteProps & { theme: ReactNativePaper.ThemeProp }> {
  #unsubscribe?: () => void;

  render() {
    const {
      route: {
        params: { id },
      },
    } = this.props;
    const router = PlatformFactory.getRouter<MainStackParamList>(this.props.navigation);
    const storesProvider = StoresProvider.get<CuratorStores>();
    const tenantStore = storesProvider.getStore('TenantStore');
    const {
      styles: { components },
    } = this.props.theme;
    const projectProps = {
      projectStore: ProjectStore.getStoreForId(id),
      router,
      userStore: storesProvider.getStore('UserStore'),
      categoryStore: storesProvider.getStore('CategoryStore'),
      stakeholderStore: storesProvider.getStore('StakeholderStore'),
    };

    return (
      <AppContent>
        <View style={[components.pageContainerConstraints, components.flexAll]}>
          <WebDrawer {...projectProps} tenantStore={tenantStore} currentRouteName="project" />
          <View style={[components.pageContentConstraints, components.flexAll]}>
            <ProjectPage {...projectProps} />
            <AppFooter />
          </View>
        </View>
      </AppContent>
    );
  }

  componentDidMount() {
    this.#unsubscribe = this.props.navigation.addListener('focus', () => {
      const {
        route: {
          params: { id },
        },
      } = this.props;
      ProjectStore.getStoreForId(id)?.refresh();
    });
  }

  componentWillUnmount() {
    this.#unsubscribe && this.#unsubscribe();
    const {
      route: {
        params: { id },
      },
    } = this.props;
    ProjectStore.removeStoreForId(id);
    const storesProvider = StoresProvider.get<CuratorStores>();
    storesProvider.getStore('CategoryStore').clearAll();
    storesProvider.getStore('StakeholderStore').clearAll();
  }
}

export default withTheme(ProjectRoute);
