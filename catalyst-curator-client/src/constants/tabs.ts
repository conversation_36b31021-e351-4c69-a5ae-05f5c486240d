import { AnalyticsStackParamList } from '../routing/analytics/screens';
import { MainStackParamList } from '../routing/screens';

type MainStackTab = { name: string; location: keyof MainStackParamList };
export const headerTabs: MainStackTab[] = [
  { name: 'Opportunities', location: 'dashboard' },
  { name: 'Projects', location: 'projects' },
  { name: 'Analytics', location: 'analytics' },
];

type AnalyticsStackTab = { name: string; location: keyof AnalyticsStackParamList };
export const analyticsTabs: AnalyticsStackTab[] = [
  { name: 'Overview', location: 'overview' },
  // { name: 'Reports', location: 'reports' },
  { name: 'Table', location: 'table' },
];
