import { RelatedOpportunityType } from '../services/codegen/types';

export enum RelatedOpportunitySelectionType {
  Child = RelatedOpportunityType.Child,
  Linked = RelatedOpportunityType.Linked,
  // the opportunity is linked but is the 'target' not the 'source' in the relationship
  // this is not important for display, but in communicating operations to the server
  LinkedNotOwner = 'LINK_NOT_OWNER',
  Parent = 'PARENT',
}

export const checkRelatedOpportunityTypeEquivalence = (
  selectionType: RelatedOpportunitySelectionType,
  type: RelatedOpportunityType,
): boolean => {
  if (selectionType === RelatedOpportunitySelectionType.Parent) return false;
  if (selectionType === RelatedOpportunitySelectionType.Child) return type === RelatedOpportunityType.Child;
  if (selectionType === RelatedOpportunitySelectionType.Linked) return type === RelatedOpportunityType.Linked;
  if (selectionType === RelatedOpportunitySelectionType.LinkedNotOwner) return type === RelatedOpportunityType.Linked;
  return false;
};
