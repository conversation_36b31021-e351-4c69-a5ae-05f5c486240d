import { MainStackParamList } from '../routing/screens';
import { RoleNames } from '../services/codegen/types';

const MainMenuItems: Array<NavigationDrawerMenuItem> = [
  {
    name: 'dashboard',
    label: 'Opportunities',
    icon: 'batch-prediction',
    roles: [RoleNames.Admin, RoleNames.Analyst, RoleNames.Curator],
  },
  {
    name: 'projects',
    label: 'Projects',
    icon: 'model-training',
    roles: [RoleNames.Admin, RoleNames.Analyst, RoleNames.Curator],
  },
  {
    name: 'analytics',
    label: 'Analytics',
    icon: 'bar-chart',
    roles: [RoleNames.Admin, RoleNames.Analyst],
  },
];

// const ToolMenuItems: Array<NavigationDrawerMenuItem> = [
//   {
//     name: 'innovation',
//     label: 'Gather Opportunities',
//     icon: 'add-to-photos',
//     roles: [RoleNames.Admin, RoleNames.Analyst, RoleNames.Curator],
//   },
// ];

const UserBadgeMenuItems: Array<NavigationDrawerMenuItem> = [
  {
    name: 'profile',
    label: 'User Management',
    icon: 'manage-accounts',
    location: 'manage',
    roles: [RoleNames.Admin],
  },
];

export type NavigationDrawerMenuItem = {
  name: keyof MainStackParamList;
  label: string;
  icon: string;
  location?: string;
  roles: Array<RoleNames>;
  subItems?: Array<NavigationDrawerMenuItem>;
};

export type NavigationDrawerMenuGroup = {
  name: string;
  displayLabel: boolean;
  roles: Array<RoleNames>;
  items: Array<NavigationDrawerMenuItem>;
};

export type NavigationDrawerMenu = {
  menu: Array<NavigationDrawerMenuGroup>;
};

export const NavigationDrawerMenuLayout = {
  menu: [
    {
      name: 'Main',
      displayLabel: false,
      roles: [RoleNames.Admin, RoleNames.Analyst, RoleNames.Curator],
      items: MainMenuItems,
    },
    // {
    //   name: 'Tools',
    //   displayLabel: true,
    //   items: ToolMenuItems,
    // },
    {
      name: 'Administrative',
      displayLabel: true,
      roles: [RoleNames.Admin],
      items: UserBadgeMenuItems,
    },
  ],
};

// export const NavigationDrawerMenuLayout = drawerMenuJson;
