import { LinkingOptions, NavigationContainer, ParamListBase, DefaultTheme } from '@react-navigation/native';
import { createURL } from 'expo-linking';
import { observer } from 'mobx-react';
import { Provider as PaperProvider } from 'react-native-paper';
import { SystemMessage } from './lib';
import { CuratorStores } from './platform/initializers';
import { StoresProvider } from './lib/stores/StoresProvider';
import { LoginStackScreens, MainStackScreens, RootStack } from './routing/screens';
import { ErrorBoundary } from './lib/ui/molecules/ErrorBoundary';
import AppBackgroundTasks from './AppBackgroundTasks';

const linkingOptions: LinkingOptions<ParamListBase> = { prefixes: [createURL('/')], enabled: true };
type AppProps = { onLayoutRootView?: () => Promise<void> };

export default observer(function App({ onLayoutRootView }: AppProps) {
  const storesProvider = StoresProvider.get<CuratorStores>();
  const applicationStore = storesProvider.getStore('ApplicationStore');
  const tenantStore = storesProvider.getStore('TenantStore');

  const {
    applicationConfig: { origin: Origin },
  } = applicationStore;
  // If the tenantStore is not initialized, use the defaultTheme provided by react-navigation.
  const { theme } = tenantStore || { theme: DefaultTheme };

  const activeHighlight = `
  textarea:focus-visible, select:focus-visible, input:focus-visible, button:focus-visible, div:focus-visible {
      outline: 1px solid ${theme?.colors?.activeHighlight};
  }
  `;
  const style = document.createElement('style');
  style.appendChild(document.createTextNode(activeHighlight));
  document.head.appendChild(style);

  const userStore = storesProvider.getStore('UserStore');
  const { signedIn, isAdminUser } = userStore;
  Origin && linkingOptions.prefixes.push(Origin);
  return (
    <PaperProvider theme={theme}>
      <NavigationContainer
        theme={{
          colors: {
            primary: theme.colors.primary,
            background: theme.colors.background,
            card: '#fff',
            text: theme.colors.text,
            border: theme.colors.border,
            notification: theme.colors.notice,
          },
          dark: theme.dark,
        }}
        linking={linkingOptions}
      >
        <ErrorBoundary>
          <AppBackgroundTasks
            userStore={userStore}
            onLayoutRootView={onLayoutRootView}
            theme={theme}
            logoutAfterMs={isAdminUser ? 600000 : 900000}
          >
            <RootStack.Navigator screenOptions={{ headerShown: false }}>
              {!signedIn && (
                <RootStack.Screen
                  key={'LoginStack'}
                  name={'cAuth'}
                  getComponent={() => LoginStackScreens}
                  options={{ title: 'Login' }}
                />
              )}
              {signedIn && (
                <RootStack.Screen
                  key={'MainStack'}
                  name={'cMain'}
                  getComponent={() => MainStackScreens}
                  options={{ title: 'Main' }}
                />
              )}
            </RootStack.Navigator>
            <SystemMessage
              getMessage={() => applicationStore?.systemMessage}
              onDismiss={() => applicationStore.clearSystemMessage()}
            />
          </AppBackgroundTasks>
        </ErrorBoundary>
      </NavigationContainer>
    </PaperProvider>
  );
});
