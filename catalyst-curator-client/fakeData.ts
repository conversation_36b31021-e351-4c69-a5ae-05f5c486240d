export const submissionsByMonth = [
  {
    month: 'Dec',
    tenantBreakdown: [
      { name: 'Alpha Corp', value: 19 },
      { name: 'Beta Holdings', value: 20 },
      { name: 'Gamma Solutions', value: 11 },
      { name: 'Delta Services', value: 12 },
      { name: 'Epsilon LLC', value: 25 },
      { name: 'Zeta Inc', value: 14 },
      { name: 'Theta Group', value: 14 },
    ],
    total: 115,
  },
  {
    month: 'Jan',
    tenantBreakdown: [
      { name: 'Alpha Corp', value: 12 },
      { name: 'Beta Holdings', value: 18 },
      { name: 'Gamma Solutions', value: 9 },
      { name: 'Delta Services', value: 15 },
      { name: 'Epsilon LLC', value: 24 },
      { name: 'Zeta Inc', value: 14 },
      { name: 'Theta Group', value: 11 },
    ],
    total: 103,
  },
  {
    month: 'Feb',
    tenantBreakdown: [
      { name: 'Alpha Corp', value: 16 },
      { name: 'Beta Holdings', value: 17 },
      { name: 'Gamma Solutions', value: 10 },
      { name: 'Delta Services', value: 14 },
      { name: 'Epsilon LLC', value: 19 },
      { name: 'Zeta Inc', value: 16 },
      { name: 'Theta Group', value: 12 },
    ],
    total: 104,
  },
  {
    month: 'Mar',
    tenantBreakdown: [
      { name: 'Alpha Corp', value: 14 },
      { name: 'Beta Holdings', value: 16 },
      { name: 'Gamma Solutions', value: 11 },
      { name: 'Delta Services', value: 17 },
      { name: 'Epsilon LLC', value: 15 },
      { name: 'Zeta Inc', value: 15 },
      { name: 'Theta Group', value: 10 },
    ],
    total: 98,
  },
  {
    month: 'Apr',
    tenantBreakdown: [
      { name: 'Alpha Corp', value: 13 },
      { name: 'Beta Holdings', value: 18 },
      { name: 'Gamma Solutions', value: 12 },
      { name: 'Delta Services', value: 16 },
      { name: 'Epsilon LLC', value: 18 },
      { name: 'Zeta Inc', value: 12 },
      { name: 'Theta Group', value: 14 },
    ],
    total: 103,
  },
  {
    month: 'May',
    tenantBreakdown: [
      { name: 'Alpha Corp', value: 17 },
      { name: 'Beta Holdings', value: 13 },
      { name: 'Gamma Solutions', value: 14 },
      { name: 'Delta Services', value: 15 },
      { name: 'Epsilon LLC', value: 20 },
      { name: 'Zeta Inc', value: 18 },
      { name: 'Theta Group', value: 13 },
    ],
    total: 110,
  },
  {
    month: 'Jun',
    tenantBreakdown: [
      { name: 'Alpha Corp', value: 15 },
      { name: 'Beta Holdings', value: 19 },
      { name: 'Gamma Solutions', value: 13 },
      { name: 'Delta Services', value: 14 },
      { name: 'Epsilon LLC', value: 17 },
      { name: 'Zeta Inc', value: 15 },
      { name: 'Theta Group', value: 15 },
    ],
    total: 108,
  },
  {
    month: 'Jul',
    tenantBreakdown: [
      { name: 'Alpha Corp', value: 18 },
      { name: 'Beta Holdings', value: 15 },
      { name: 'Gamma Solutions', value: 12 },
      { name: 'Delta Services', value: 17 },
      { name: 'Epsilon LLC', value: 22 },
      { name: 'Zeta Inc', value: 13 },
      { name: 'Theta Group', value: 16 },
    ],
    total: 113,
  },
  {
    month: 'Aug',
    tenantBreakdown: [
      { name: 'Alpha Corp', value: 16 },
      { name: 'Beta Holdings', value: 20 },
      { name: 'Gamma Solutions', value: 11 },
      { name: 'Delta Services', value: 13 },
      { name: 'Epsilon LLC', value: 23 },
      { name: 'Zeta Inc', value: 14 },
      { name: 'Theta Group', value: 17 },
    ],
    total: 114,
  },
  {
    month: 'Sep',
    tenantBreakdown: [
      { name: 'Alpha Corp', value: 14 },
      { name: 'Beta Holdings', value: 18 },
      { name: 'Gamma Solutions', value: 12 },
      { name: 'Delta Services', value: 15 },
      { name: 'Epsilon LLC', value: 21 },
      { name: 'Zeta Inc', value: 12 },
      { name: 'Theta Group', value: 15 },
    ],
    total: 107,
  },
  {
    month: 'Oct',
    tenantBreakdown: [
      { name: 'Alpha Corp', value: 15 },
      { name: 'Beta Holdings', value: 17 },
      { name: 'Gamma Solutions', value: 13 },
      { name: 'Delta Services', value: 16 },
      { name: 'Epsilon LLC', value: 18 },
      { name: 'Zeta Inc', value: 17 },
      { name: 'Theta Group', value: 12 },
    ],
    total: 108,
  },
  {
    month: 'Nov',
    tenantBreakdown: [
      { name: 'Alpha Corp', value: 17 },
      { name: 'Beta Holdings', value: 15 },
      { name: 'Gamma Solutions', value: 14 },
      { name: 'Delta Services', value: 18 },
      { name: 'Epsilon LLC', value: 16 },
      { name: 'Zeta Inc', value: 16 },
      { name: 'Theta Group', value: 13 },
    ],
    total: 109,
  },
];

export const status = [
  {
    name: 'APPROVED',
    tenantBreakdown: [
      { name: '101st', value: 11 },
      { name: '82nd', value: 15 },
      { name: 'The MonumentsMen', value: 30 },
    ],
    total: 56,
  },

  {
    name: 'PENDING',
    tenantBreakdown: [
      { name: '101st', value: 11 },
      { name: '82nd', value: 22 },
      { name: 'The MonumentsMen', value: 32 },
    ],
    total: 65,
  },

  {
    name: 'DELETED',
    tenantBreakdown: [
      { name: '101st', value: 5 },
      { name: '82nd', value: 10 },
      { name: 'The MonumentsMen', value: 40 },
    ],
    total: 55,
  },
];

export const submissionsByYear = [
  {
    month: '2023',
    tenantBreakdown: [
      { name: 'Alpha Corp', value: 17 },
      { name: 'Beta Holdings', value: 15 },
      { name: 'Gamma Solutions', value: 14 },
      { name: 'Delta Services', value: 18 },
      { name: 'Epsilon LLC', value: 16 },
      { name: 'Zeta Inc', value: 16 },
      { name: 'Theta Group', value: 13 },
    ],
    total: 109,
  },
  {
    month: '2024',
    tenantBreakdown: [
      { name: 'Alpha Corp', value: 19 },
      { name: 'Beta Holdings', value: 20 },
      { name: 'Gamma Solutions', value: 11 },
      { name: 'Delta Services', value: 12 },
      { name: 'Epsilon LLC', value: 25 },
      { name: 'Zeta Inc', value: 14 },
      { name: 'Theta Group', value: 14 },
    ],
    total: 115,
  },
];
