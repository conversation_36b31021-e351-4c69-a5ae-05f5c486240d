const fs = require('fs');

const path = require('path');
const rootProjecPath = path.resolve(__dirname);

// create a .env if it doesn't exist
const envSource = path.join(rootProjecPath, 'sample.env').replace(/\\/g, '\\\\');
const currentEnv = path.join(rootProjecPath, '.env').replace(/\\/g, '\\\\');
if (fs.existsSync(currentEnv)) {
  console.log('.env already exists. Please verify its content matches the sample.env');
}
try {
  console.log('Created a .env from sample.env. Please verify setup.');
  fs.copyFileSync(envSource, currentEnv);
} catch (e) {
  console.log('failed to create .env');
}
