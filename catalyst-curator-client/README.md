## Table of Contents

- [Styling](#styling)

## Styling

### Tenant Theme

The tenant theme is customizable within the tenant config file. For example: catalyst-server/config/tenants/pathfinder/theme.json.
In the curation client, this theme is not used, and the default curation theme will always be used.
In the innovation client, this theme will always be used.

### React Native Paper

Theme is provided across application with React Native Paper's PaperProvider component at the top level. [here](https://callstack.github.io/react-native-paper/).

The default RNP theme is taken from the TenantStore, modified and plugged into this provider.

To access the theme, use the high-order-component withTheme which will allow access to the theme prop.

### Changing the theme

In the curation client, the defaultCurationTheme can be changed (This is found in lib/theme/defaultCurationTheme.ts). However, it should be noted that if the TenantTheme type is added to, it will not be in the tenant theme config files unless also added there. Because this coupling exists, when a style is added to the defaultCurationTheme, and likewise the TenantTheme type, it should also be added to all of the tenant configs, or typed as optional.
