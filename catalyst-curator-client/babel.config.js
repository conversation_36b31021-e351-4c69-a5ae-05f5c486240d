module.exports = function (api) {
  api.cache(true);
  return {
    presets: ['babel-preset-expo'],
    env: {
      production: {
        plugins: [
          '@babel/plugin-transform-runtime',
          'react-native-paper/babel',
          'react-native-reanimated/plugin',
          'react-native-web',
          [
            'module-resolver',
            {
              alias: {
                '^react-native$': 'react-native-web',
                './lib': '../catalyst-lib/src/lib',
              },
            },
          ],
        ],
      },
    },
  };
};
