const path = require('path');
const { mergeConfig } = require('@react-native/metro-config');
const { getDefaultConfig } = require('@expo/metro-config');

const defaultConfig = getDefaultConfig(__dirname);
const config = {
  transformer: {
    getTransformOptions: async () => ({
      transform: {
        experimentalImportSupport: false,
        inlineRequires: true,
      },
    }),
    babelTransformerPath: require.resolve('react-native-svg-transformer'),
  },
  watchFolders: [path.resolve(__dirname, '../catalyst-lib/src/lib')],
  resolver: {
    nodeModulesPaths: [
      path.resolve(__dirname, 'node_modules'),
      path.resolve(path.resolve(__dirname, '../'), 'node_modules'),
    ],
  },
  cacheVersion: process.env.APP_ENV,
};

module.exports = mergeConfig(defaultConfig, config);
